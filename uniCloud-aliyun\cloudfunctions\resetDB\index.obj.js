// 云对象教程: https://uniapp.dcloud.net.cn/uniCloud/cloud-obj
const { log } = require("console")
const uniID = require('uni-id-common')

// jsdoc语法提示教程：https://ask.dcloud.net.cn/docs/#//ask.dcloud.net.cn/article/129
module.exports = {
	_before: function () { // 通用预处理器
		const clientInfo = this.getClientInfo()
		this.uniID = uniID.createInstance({ // 创建uni-id实例，其上方法同uniID
			clientInfo
		})
	},
	/**
	 * 清空所有表数据
	 */
	// async reset(){
	// 	const httpInfo = this.getHttpInfo()
	// 	let { tableList } = JSON.parse(httpInfo.body)
	// 	tableList.forEach(tName =>{
	// 		db.collection(tName).remove()
	// 	})
	// }
	async option(){
		const task = db.collection('task').get()
		console.log('task---', task)
	}
}