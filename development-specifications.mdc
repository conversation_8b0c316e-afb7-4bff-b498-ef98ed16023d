---
description: 
globs: 
alwaysApply: false
---
# 页面导航规则

## 使用 router 工具进行页面跳转

项目中所有的页面跳转都必须使用 [src/utils/tools.ts](mdc:src/utils/tools.ts) 中定义的 `router` 工具函数，而不是直接使用 uni 的原生导航 API。

### 可用的导航方法：

1. `router.push(url, params?)` - 保留当前页面，跳转到应用内的某个页面
2. `router.redirect(url, params?)` - 关闭当前页面，跳转到应用内的某个页面
3. `router.reLaunch(url, params?)` - 关闭所有页面，打开到应用内的某个页面
4. `router.switchTab(url)` - 跳转到 tabBar 页面
5. `router.back(delta?)` - 返回上一页面或多级页面

# 数据库规范

## 主键命名规范

数据库中使用 `_id` 作为主键，在前端页面渲染时也应直接使用 `_id`，无需重命名成 `id`。

# 通用函数使用规范

## 优先使用 tools.ts 中的工具函数

在编写通用函数前，必须先查看 [src/utils/tools.ts](mdc:src/utils/tools.ts) 是否已存在相同或类似功能的函数。如果存在，应直接使用现有函数，避免重复实现。

### 现有工具函数一览：

1. **字符串处理**
   - `generateUUID()` - 生成 UUID
   - `isUUID(uuid)` - 判断字符串是否为 UUID
   - `generateRandomString(length)` - 生成指定长度的随机字符串
   - `parseYAML(yamlString)` - 解析 YAML 内容

2. **数据类型判断**
   - `isObject(value)` - 判断是否为对象类型

3. **路由与参数**
   - `getUrlParams()` - 获取当前页面 URL 参数
   - `getRoute.params()` - 获取当前路由参数
   - `getRoute.path()` - 获取当前路由路径
   - `router` - 路由导航工具集

4. **样式处理**
   - `hexToRGBA(hex, alpha)` - 十六进制颜色转换为 RGBA

5. **功能工具**
   - `toolCloudSync` - 云同步功能控制
   - `renderMarkdown(content)` - Markdown 渲染
   - `parseMarkdown(content)` - Markdown 解析
   - `toolDebounce(func, delay)` - 函数防抖
   - `toolGetNetworkType()` - 获取网络状态
   - `toolGlobalData` - 全局数据存取
   - `parseRRULE(rruleString)` - 解析重复规则

## 新增通用函数规范

如需添加新的通用函数，应遵循以下步骤：

1. 确认 tools.ts 中不存在相同或类似功能的函数
2. 在 src/utils/tools.ts 中实现新函数，并添加适当的注释说明用途和参数
3. 使用 export 导出函数，确保其他模块可以引用
4. 在需要使用的地方通过 import 引入该函数

### 函数命名规范

- 普通函数使用驼峰命名法，如 `generateUUID`
- 工具集合使用 `tool` 前缀，如 `toolDebounce`、`toolGlobalData`
- 函数名应清晰表达其功能，避免使用模糊或过于简短的名称

