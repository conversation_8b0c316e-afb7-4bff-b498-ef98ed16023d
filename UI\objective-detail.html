<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>目标详情 - OKR助手</title>
		<link
			href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css"
			rel="stylesheet"
		/>
		<link
			rel="stylesheet"
			href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
		/>
		<!-- 引入共享组件 -->
		<script>
			// 动态加载 shared-components.html
			(function () {
				const xhr = new XMLHttpRequest();
				xhr.open("GET", "shared-components.html", true);
				xhr.onreadystatechange = function () {
					if (xhr.readyState === 4 && xhr.status === 200) {
						// 创建临时容器
						const tempDiv = document.createElement("div");
						tempDiv.innerHTML = xhr.responseText;

						// 提取 style 标签并添加到 head
						const styleTags = tempDiv.getElementsByTagName("style");
						for (let i = 0; i < styleTags.length; i++) {
							const styleContent = styleTags[i].textContent;
							const style = document.createElement("style");
							style.textContent = styleContent;
							document.head.appendChild(style);
						}

						// 提取script标签内容并执行
						const scriptTags = tempDiv.getElementsByTagName("script");
						if (scriptTags.length > 0) {
							const scriptContent = scriptTags[0].textContent;
							const script = document.createElement("script");
							script.textContent = scriptContent;
							document.head.appendChild(script);

							// 等待脚本加载后初始化标签栏
							setTimeout(function () {
								loadTabBar("tab-bar-container");
							}, 100);
						}

						// 添加模板到页面
						const templates = tempDiv.getElementsByTagName("template");
						for (let i = 0; i < templates.length; i++) {
							document.body.appendChild(templates[i].cloneNode(true));
						}
					}
				};
				xhr.send();
			})();
		</script>
		<style>
			@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap");

			:root {
				--color-primary: #3b82f6;
				--color-primary-light: rgba(59, 130, 246, 0.15);
				--color-primary-dark: #2563eb;
				--color-white: #ffffff;
				--color-black: #000000;
				--color-bg: #f5f7fa;
				--color-gray-50: #f9fafb;
				--color-gray-100: #f3f4f6;
				--color-gray-200: #e5e7eb;
				--color-gray-300: #d1d5db;
				--color-gray-400: #9ca3af;
				--color-gray-500: #6b7280;
				--color-gray-600: #4b5563;
				--color-gray-700: #374151;
				--color-gray-800: #1f2937;
				--color-gray-900: #111827;
				--rounded-sm: 0.125rem;
				--rounded-md: 0.375rem;
				--rounded-lg: 0.5rem;
				--rounded-xl: 0.75rem;
				--rounded-2xl: 1rem;
				--rounded-3xl: 1.5rem;
				--rounded-full: 9999px;
			}

			body {
				font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
					Helvetica, Arial, sans-serif;
				background: var(--color-bg);
				height: 100vh;
				display: flex;
				flex-direction: column;
				color: var(--color-gray-800);
				overflow-x: hidden;
				scrollbar-width: thin; /* Firefox */
				scrollbar-color: #a0a0a0 #f1f1f1; /* Firefox */
			}

			/* Webkit browsers (Chrome, Safari, Edge) */
			::-webkit-scrollbar {
				width: 8px; /* 滚动条宽度 */
			}

			::-webkit-scrollbar-track {
				background: #f1f1f1; /* 轨道背景色 */
			}

			::-webkit-scrollbar-thumb {
				background: #a0a0a0; /* 滑块颜色 */
				border-radius: 4px; /* 滑块圆角 */
			}

			::-webkit-scrollbar-thumb:hover {
				background: #888; /* 滑块鼠标悬停颜色 */
			}

			.content-area {
				flex: 1;
				overflow-y: auto;
				padding: 0 16px 90px;
			}

			.navbar {
				display: flex;
				align-items: center;
				padding: 15px 20px;
				background: var(--color-white);
				border-bottom: 1px solid var(--color-gray-200);
				position: sticky;
				top: 44px;
				z-index: 40;
			}

			.navbar-title {
				font-weight: 600;
				font-size: 18px;
				margin-left: 15px;
				color: var(--color-primary);
			}

			.navbar-back {
				color: var(--color-primary);
				font-size: 16px;
				width: 36px;
				height: 36px;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 8px;
				background: var(--color-gray-100);
			}

			.more-btn {
				width: 36px;
				height: 36px;
				border-radius: 8px;
				background: var(--color-white);
				color: var(--color-gray-600);
				display: flex;
				align-items: center;
				justify-content: center;
				border: 1px solid var(--color-gray-200);
			}

			.card {
				background: var(--color-white);
				border-radius: var(--rounded-lg);
				border: 1px solid var(--color-gray-200);
				transition: all 0.2s ease;
				overflow: hidden;
				margin-bottom: 16px;
				padding: 20px;
			}

			.card:hover {
				border-color: var(--color-gray-300);
			}

			.header-card {
				position: relative;
				overflow: hidden;
				margin-top: 15px;
			}

			.goal-tag {
				display: inline-block;
				padding: 6px 12px;
				border-radius: 6px;
				font-size: 12px;
				font-weight: 600;
				margin-bottom: 12px;
			}

			.goal-tag.blue {
				background-color: #ebf5ff;
				color: #3b82f6;
			}

			.goal-title {
				font-size: 24px;
				font-weight: 700;
				margin-bottom: 16px;
				line-height: 1.4;
				position: relative;
				z-index: 1;
				color: var(--color-gray-800);
			}

			.goal-description {
				font-size: 15px;
				line-height: 1.6;
				color: var(--color-gray-600);
				margin: 16px 0;
				overflow: hidden;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
				position: relative;
				transition: all 0.3s ease;
				cursor: pointer;
			}

			.goal-description.expanded {
				-webkit-line-clamp: unset;
			}

			.goal-description::after {
				content: "⋯";
				position: absolute;
				right: 0;
				bottom: 0;
				color: var(--color-primary);
				font-size: 16px;
				font-weight: bold;
				padding-left: 4px;
				background: linear-gradient(
					to right,
					transparent,
					var(--color-white) 50%
				);
			}

			.goal-description.expanded::after {
				display: none;
			}

			.toggle-description {
				color: var(--color-primary);
				font-size: 13px;
				font-weight: 500;
				cursor: pointer;
				display: inline-flex;
				align-items: center;
				margin-top: 4px;
			}

			.toggle-description i {
				font-size: 12px;
				margin-left: 4px;
			}

			.meta-row {
				display: flex;
				flex-wrap: wrap;
				margin: 0 -5px;
				gap: 12px;
			}

			.meta-item {
				display: flex;
				align-items: center;
				padding: 4px 8px;
				background: var(--color-gray-100);
				border-radius: 6px;
				font-size: 13px;
				color: var(--color-gray-700);
			}

			.meta-item i {
				margin-right: 5px;
				color: var(--color-primary);
				font-size: 14px;
			}

			.progress-container {
				margin: 20px 0;
			}

			.progress-info {
				display: flex;
				justify-content: space-between;
				margin-bottom: 8px;
			}

			.progress-label {
				font-weight: 600;
				font-size: 14px;
				color: var(--color-gray-700);
			}

			.progress-value {
				font-weight: 700;
				font-size: 16px;
				color: var(--color-primary);
			}

			.progress-bar {
				height: 8px;
				background: var(--color-gray-200);
				border-radius: 4px;
				overflow: hidden;
			}

			.progress-fill {
				height: 100%;
				background: var(--color-primary);
				border-radius: 4px;
			}

			.section-title {
				font-size: 18px;
				font-weight: 600;
				color: var(--color-gray-800);
				margin: 24px 0 16px;
				display: flex;
				align-items: center;
			}

			.section-title i {
				margin-right: 8px;
				color: var(--color-primary);
			}

			.kr-card {
				background: var(--color-white);
				border: 1px solid var(--color-gray-200);
				border-radius: var(--rounded-md);
				padding: 16px;
				margin-bottom: 12px;
				transition: all 0.2s ease;
				position: relative;
				overflow: hidden;
				z-index: 1;
				box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
				display: flex;
				flex-direction: column;
			}

			.kr-card::before {
				content: "";
				position: absolute;
				left: 0;
				bottom: 0;
				height: 4px;
				width: var(--progress-width, 0%);
				background: var(--color-primary);
				z-index: 1;
				transition: all 0.3s ease;
			}

			.kr-card:hover {
				box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
				border-color: var(--color-gray-300);
			}

			.kr-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 12px;
			}

			.kr-title {
				font-weight: 600;
				font-size: 16px;
				color: var(--color-gray-800);
				flex: 1;
			}

			.kr-progress {
				font-weight: 600;
				font-size: 14px;
				color: var(--color-white);
				background: var(--color-primary);
				padding: 2px 8px;
				border-radius: 12px;
				margin-left: 8px;
			}

			.kr-meta {
				display: flex;
				font-size: 13px;
				color: var(--color-gray-600);
				gap: 12px;
				margin-top: 8px;
			}

			.kr-meta i {
				margin-right: 4px;
			}

			.kr-tag {
				display: inline-flex;
				align-items: center;
				font-size: 12px;
				font-weight: 500;
				color: var(--color-gray-600);
			}

			.kr-tag.deadline {
				color: var(--color-primary);
			}

			.kr-tag.deadline::before {
				content: "\f073";
				font-family: "Font Awesome 5 Free";
				font-weight: 900;
				margin-right: 5px;
			}

			.kr-tag.tasks {
				color: #10b981;
				cursor: pointer;
				transition: all 0.2s ease;
			}

			.kr-tag.tasks:hover {
				color: #0d9668;
				text-decoration: underline;
			}

			.kr-tag.tasks::before {
				content: "\f0ae";
				font-family: "Font Awesome 5 Free";
				font-weight: 900;
				margin-right: 5px;
			}

			/* 关键结果任务列表样式 */
			.kr-tasks {
				margin-top: 10px;
				border-top: 1px dashed var(--color-gray-200);
				padding-top: 10px;
				display: none;
			}

			.kr-tasks.visible {
				display: block;
				animation: fadeIn 0.3s ease;
			}

			@keyframes fadeIn {
				from {
					opacity: 0;
					transform: translateY(-10px);
				}
				to {
					opacity: 1;
					transform: translateY(0);
				}
			}

			.kr-task-item {
				display: flex;
				align-items: center;
				padding: 6px 4px;
				font-size: 13px;
				color: var(--color-gray-700);
				border-radius: var(--rounded-sm);
			}

			.kr-task-item:hover {
				background: var(--color-gray-50);
			}

			.kr-task-item.completed {
				color: var(--color-gray-400);
				text-decoration: line-through;
			}

			.kr-task-checkbox {
				width: 16px;
				height: 16px;
				border: 1.5px solid var(--color-gray-300);
				border-radius: 3px;
				margin-right: 8px;
				position: relative;
				cursor: pointer;
				flex-shrink: 0;
			}

			.kr-task-checkbox.checked {
				background: var(--color-primary);
				border-color: var(--color-primary);
			}

			.kr-task-checkbox.checked::after {
				content: "";
				position: absolute;
				left: 5px;
				top: 2px;
				width: 4px;
				height: 8px;
				border: solid white;
				border-width: 0 2px 2px 0;
				transform: rotate(45deg);
			}

			.kr-tasks-toggle {
				color: var(--color-primary);
				font-size: 12px;
				font-weight: 500;
				cursor: pointer;
				align-self: flex-end;
				margin-top: 6px;
				display: inline-flex;
				align-items: center;
			}

			.kr-tasks-toggle i {
				margin-left: 4px;
				transition: transform 0.2s ease;
			}

			.kr-tasks-toggle.active i {
				transform: rotate(180deg);
			}

			.action-btn {
				font-size: 14px;
				font-weight: 500;
				padding: 10px 16px;
				border-radius: var(--rounded-md);
				text-align: center;
				transition: all 0.2s ease;
			}

			.action-btn.primary {
				background: var(--color-primary);
				color: white;
			}

			.action-btn.primary:hover {
				background: var(--color-primary-dark);
			}

			.action-btn.secondary {
				background: var(--color-white);
				color: var(--color-gray-700);
				border: 1px solid var(--color-gray-300);
			}

			.action-btn.secondary:hover {
				background: var(--color-gray-100);
			}

			.action-btn i {
				margin-right: 6px;
			}

			.action-row {
				display: flex;
				gap: 10px;
				margin-top: 20px;
			}

			.task-list {
				margin-top: 12px;
			}

			.task-item {
				display: flex;
				align-items: flex-start;
				padding: 12px;
				border-radius: var(--rounded-md);
				margin-bottom: 8px;
				background: var(--color-gray-50);
				border: 1px solid var(--color-gray-200);
			}

			.task-checkbox {
				width: 20px;
				height: 20px;
				border: 2px solid var(--color-gray-300);
				border-radius: 4px;
				margin-right: 12px;
				flex-shrink: 0;
				position: relative;
				transition: all 0.2s ease;
				cursor: pointer;
			}

			.task-checkbox.checked {
				background: var(--color-primary);
				border-color: var(--color-primary);
			}

			.task-checkbox.checked::after {
				content: "";
				position: absolute;
				left: 6px;
				top: 3px;
				width: 5px;
				height: 10px;
				border: solid white;
				border-width: 0 2px 2px 0;
				transform: rotate(45deg);
			}

			.task-content {
				flex: 1;
			}

			.task-title {
				font-weight: 500;
				margin-bottom: 4px;
				color: var(--color-gray-800);
			}

			.task-meta {
				font-size: 12px;
				color: var(--color-gray-600);
			}

			.badge {
				display: inline-block;
				padding: 2px 8px;
				border-radius: 4px;
				font-size: 12px;
				font-weight: 500;
				margin-left: 6px;
			}

			.badge.blue {
				background: #ebf5ff;
				color: #3b82f6;
			}

			.badge.green {
				background: #ecfdf5;
				color: #10b981;
			}

			.badge.red {
				background: #fee2e2;
				color: #ef4444;
			}

			.badge.gray {
				background: #f3f4f6;
				color: #6b7280;
			}

			.list-items {
				margin-left: 12px;
			}

			.list-item {
				display: flex;
				align-items: baseline;
				margin-bottom: 10px;
			}

			.list-item::before {
				content: "•";
				color: var(--color-primary);
				font-weight: bold;
				margin-right: 8px;
			}

			.reflection-card {
				border-left: 4px solid var(--color-primary);
			}

			.add-btn {
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 12px;
				border-radius: var(--rounded-md);
				background: var(--color-gray-100);
				color: var(--color-gray-700);
				font-weight: 500;
				font-size: 14px;
				border: 1px dashed var(--color-gray-300);
				transition: all 0.2s ease;
				margin-bottom: 12px;
				margin-left: auto;
				margin-right: auto;
				width: 80%;
				max-width: 300px;
			}

			.add-btn:hover {
				background: var(--color-gray-200);
				color: var(--color-gray-800);
			}

			.add-btn i {
				margin-right: 8px;
			}

			/* 新增时间信息卡片样式 */
			.time-info-container {
				display: grid;
				grid-template-columns: repeat(2, 1fr);
				gap: 10px;
				margin: 10px 0;
			}

			.time-card {
				background: var(--color-gray-50);
				border: 1px solid var(--color-gray-200);
				border-radius: 8px;
				padding: 8px 10px;
				display: flex;
				align-items: center;
				transition: all 0.2s ease;
			}

			.time-card:hover {
				background: var(--color-gray-100);
			}

			.time-card i {
				font-size: 16px;
				color: var(--color-primary);
				margin-right: 8px;
			}

			.time-content {
				display: flex;
				flex-direction: column;
			}

			.time-label {
				font-size: 11px;
				color: var(--color-gray-500);
				line-height: 1;
			}

			.time-value {
				font-size: 14px;
				font-weight: 600;
				color: var(--color-gray-800);
				margin-top: 2px;
			}

			.time-card.urgent .time-value {
				color: #ef4444;
			}

			.time-card.progress .time-value {
				color: #10b981;
			}

			.edit-btn {
				border-radius: 8px;
				padding: 8px 10px;
				font-size: 16px;
				background: var(--color-white);
				color: var(--color-primary);
				border: 1px solid var(--color-primary);
				display: flex;
				align-items: center;
				justify-content: center;
				width: 38px;
				height: 38px;
			}

			.edit-btn:hover {
				background: var(--color-gray-50);
			}

			.edit-btn i {
				margin-right: 0;
			}
		</style>
	</head>
	<body>
		<!-- 状态栏 -->
		<div id="status-bar-container"></div>

		<!-- 导航栏 -->
		<div class="navbar">
			<a href="home.html" class="navbar-back">
				<i class="fas fa-chevron-left"></i>
			</a>
			<div class="navbar-title">目标详情</div>
			<div class="flex-1"></div>
			<button class="edit-btn">
				<i class="fas fa-edit"></i>
			</button>
		</div>

		<!-- 内容区域 -->
		<div class="content-area">
			<!-- 目标概览卡片 -->
			<div class="card header-card">
				<h1 class="goal-title">提升产品研发团队效率</h1>

				<div class="goal-description" onclick="toggleDescription()">
					通过优化开发流程、引入自动化工具和提高团队协作来提高产品研发效率，减少交付时间，提升产品质量。
				</div>

				<!-- 替换原来的 time-info-container -->
				<div class="time-info-container">
					<div class="time-card">
						<i class="fas fa-calendar-plus"></i>
						<div class="time-content">
							<div class="time-label">开始时间</div>
							<div class="time-value">2023/4/1</div>
						</div>
					</div>
					<div class="time-card">
						<i class="fas fa-calendar-check"></i>
						<div class="time-content">
							<div class="time-label">结束时间</div>
							<div class="time-value">2023/6/30</div>
						</div>
					</div>
					<div class="time-card urgent">
						<i class="fas fa-hourglass-half"></i>
						<div class="time-content">
							<div class="time-label">剩余时间</div>
							<div class="time-value">45 天</div>
						</div>
					</div>
					<div class="time-card progress">
						<i class="fas fa-check-circle"></i>
						<div class="time-content">
							<div class="time-label">已完成 KR</div>
							<div class="time-value">1/3</div>
						</div>
					</div>
				</div>

				<div class="progress-container">
					<div class="progress-info">
						<div class="progress-label">总体进度</div>
						<div class="progress-value">60%</div>
					</div>
					<div class="progress-bar">
						<div class="progress-fill" style="width: 60%"></div>
					</div>
				</div>
			</div>

			<!-- 关键结果区域 -->
			<h2 class="section-title">
				<i class="fas fa-bullseye"></i>关键结果 (KRs)
			</h2>

			<div class="kr-card" style="--progress-width: 75%">
				<div class="kr-header">
					<div class="kr-title">将 CI/CD 流程自动化率提高到 95%</div>
					<div class="kr-progress">75%</div>
				</div>
				<div class="kr-meta">
					<div class="kr-tag deadline">
						<span>截止：5 月 30 日</span>
					</div>
					<div class="kr-tag tasks" onclick="toggleKrTasks('kr-tasks-1')">
						<span>3/4 任务</span>
					</div>
				</div>
				<div id="kr-tasks-1" class="kr-tasks">
					<div class="kr-task-item">
						<div
							class="kr-task-checkbox checked"
							onclick="toggleKrTaskStatus(this)"
						></div>
						<div>搭建自动化测试环境</div>
					</div>
					<div class="kr-task-item">
						<div
							class="kr-task-checkbox checked"
							onclick="toggleKrTaskStatus(this)"
						></div>
						<div>配置持续集成系统</div>
					</div>
					<div class="kr-task-item">
						<div
							class="kr-task-checkbox checked"
							onclick="toggleKrTaskStatus(this)"
						></div>
						<div>实现自动化部署流程</div>
					</div>
					<div class="kr-task-item">
						<div
							class="kr-task-checkbox"
							onclick="toggleKrTaskStatus(this)"
						></div>
						<div>持续交付流程优化</div>
					</div>
				</div>
			</div>

			<div class="kr-card" style="--progress-width: 45%">
				<div class="kr-header">
					<div class="kr-title">减少 30% 的产品缺陷数量</div>
					<div class="kr-progress">45%</div>
				</div>
				<div class="kr-meta">
					<div class="kr-tag deadline">
						<span>截止：6 月 15 日</span>
					</div>
					<div class="kr-tag tasks" onclick="toggleKrTasks('kr-tasks-2')">
						<span>2/5 任务</span>
					</div>
				</div>
				<div id="kr-tasks-2" class="kr-tasks">
					<div class="kr-task-item">
						<div
							class="kr-task-checkbox checked"
							onclick="toggleKrTaskStatus(this)"
						></div>
						<div>建立代码审查规范</div>
					</div>
					<div class="kr-task-item">
						<div
							class="kr-task-checkbox checked"
							onclick="toggleKrTaskStatus(this)"
						></div>
						<div>实施静态代码分析</div>
					</div>
					<div class="kr-task-item">
						<div
							class="kr-task-checkbox"
							onclick="toggleKrTaskStatus(this)"
						></div>
						<div>增加单元测试覆盖率</div>
					</div>
					<div class="kr-task-item">
						<div
							class="kr-task-checkbox"
							onclick="toggleKrTaskStatus(this)"
						></div>
						<div>实施 TDD 开发模式</div>
					</div>
					<div class="kr-task-item">
						<div
							class="kr-task-checkbox"
							onclick="toggleKrTaskStatus(this)"
						></div>
						<div>优化 QA 测试流程</div>
					</div>
				</div>
			</div>

			<div class="kr-card" style="--progress-width: 60%">
				<div class="kr-header">
					<div class="kr-title">将平均需求交付时间缩短 25%</div>
					<div class="kr-progress">60%</div>
				</div>
				<div class="kr-meta">
					<div class="kr-tag deadline">
						<span>截止：6 月 30 日</span>
					</div>
					<div class="kr-tag tasks" onclick="toggleKrTasks('kr-tasks-3')">
						<span>3/6 任务</span>
					</div>
				</div>
				<div id="kr-tasks-3" class="kr-tasks">
					<div class="kr-task-item">
						<div
							class="kr-task-checkbox checked"
							onclick="toggleKrTaskStatus(this)"
						></div>
						<div>优化需求分析流程</div>
					</div>
					<div class="kr-task-item">
						<div
							class="kr-task-checkbox checked"
							onclick="toggleKrTaskStatus(this)"
						></div>
						<div>改进团队协作方式</div>
					</div>
					<div class="kr-task-item">
						<div
							class="kr-task-checkbox checked"
							onclick="toggleKrTaskStatus(this)"
						></div>
						<div>精简开发流程</div>
					</div>
					<div class="kr-task-item">
						<div
							class="kr-task-checkbox"
							onclick="toggleKrTaskStatus(this)"
						></div>
						<div>提高代码复用率</div>
					</div>
					<div class="kr-task-item">
						<div
							class="kr-task-checkbox"
							onclick="toggleKrTaskStatus(this)"
						></div>
						<div>简化测试验收流程</div>
					</div>
					<div class="kr-task-item">
						<div
							class="kr-task-checkbox"
							onclick="toggleKrTaskStatus(this)"
						></div>
						<div>完善文档管理系统</div>
					</div>
				</div>
			</div>

			<button class="add-btn"><i class="fas fa-plus"></i>添加关键结果</button>

			<!-- 任务列表区域 -->
			<h2 class="section-title"><i class="fas fa-tasks"></i>相关任务</h2>

			<div class="task-list">
				<div class="task-item">
					<div class="task-checkbox checked"></div>
					<div class="task-content">
						<div class="task-title">搭建自动化测试环境</div>
						<div class="task-meta">
							<span>已完成</span>
							<span class="badge green">KR1</span>
						</div>
					</div>
				</div>

				<div class="task-item">
					<div class="task-checkbox checked"></div>
					<div class="task-content">
						<div class="task-title">实施代码审查流程</div>
						<div class="task-meta">
							<span>已完成</span>
							<span class="badge blue">KR2</span>
						</div>
					</div>
				</div>

				<div class="task-item">
					<div class="task-checkbox"></div>
					<div class="task-content">
						<div class="task-title">优化需求分析和评审流程</div>
						<div class="task-meta">
							<span>截止日期：5 月 20 日</span>
							<span class="badge red">紧急</span>
						</div>
					</div>
				</div>

				<div class="task-item">
					<div class="task-checkbox"></div>
					<div class="task-content">
						<div class="task-title">完善文档管理系统</div>
						<div class="task-meta">
							<span>截止日期：5 月 25 日</span>
							<span class="badge blue">KR3</span>
						</div>
					</div>
				</div>
			</div>

			<button class="add-btn"><i class="fas fa-plus"></i>添加任务</button>

			<!-- 阶段性反思 -->
			<h2 class="section-title"><i class="fas fa-lightbulb"></i>阶段性反思</h2>

			<div class="card reflection-card">
				<p class="mb-4 text-gray-700">
					中期反思：CI/CD
					流程自动化进展顺利，但需要加强团队成员培训。产品缺陷减少工作需要加速，考虑引入更严格的测试机制和代码审查标准。
				</p>
				<div class="list-items">
					<div class="list-item">
						积极因素：团队协作良好，自动化工具选择合适
					</div>
					<div class="list-item">
						改进点：增加自动化测试覆盖率，优化需求分析流程
					</div>
					<div class="list-item">
						下一步计划：组织团队培训，完善测试流程文档
					</div>
				</div>
				<div class="text-right mt-3 text-xs text-gray-500">
					更新于 2023/4/15
				</div>
			</div>

			<button class="add-btn"><i class="fas fa-plus"></i>添加反思记录</button>
		</div>

		<!-- 底部标签栏 -->
		<div id="tab-bar-container"></div>

		<script>
			// JavaScript 代码
			document.addEventListener("DOMContentLoaded", function () {
				// 状态栏
				const statusBarContainer = document.getElementById(
					"status-bar-container"
				);
				if (statusBarContainer) {
					const statusBarTemplate = document.getElementById(
						"ios-status-bar-template"
					);
					if (statusBarTemplate) {
						statusBarContainer.appendChild(
							statusBarTemplate.content.cloneNode(true)
						);
					}
				}

				// 设置进度背景
				const krCards = document.querySelectorAll(".kr-card");
				krCards.forEach((card) => {
					const progressValue = card.querySelector(".kr-progress").textContent;
					const percentage = parseInt(progressValue);
					card.style.setProperty("--progress-width", percentage + "%");
				});

				// 任务复选框交互
				const checkboxes = document.querySelectorAll(".task-checkbox");
				checkboxes.forEach(function (checkbox) {
					checkbox.addEventListener("click", function () {
						this.classList.toggle("checked");
						const taskTitle =
							this.nextElementSibling.querySelector(".task-title");
						if (this.classList.contains("checked")) {
							taskTitle.style.textDecoration = "line-through";
							taskTitle.style.color = "var(--color-gray-400)";
						} else {
							taskTitle.style.textDecoration = "none";
							taskTitle.style.color = "var(--color-gray-800)";
						}
					});
				});
			});

			// 描述展开/收起功能
			function toggleDescription() {
				const description = document.querySelector(".goal-description");
				description.classList.toggle("expanded");
			}

			// 关键结果任务列表展开/收起功能
			function toggleKrTasks(taskListId) {
				const taskList = document.getElementById(taskListId);
				taskList.classList.toggle("visible");
			}

			// 关键结果任务状态切换功能
			function toggleKrTaskStatus(checkbox) {
				checkbox.classList.toggle("checked");
				const taskTitle = checkbox.nextElementSibling.textContent;
				if (checkbox.classList.contains("checked")) {
					checkbox.style.backgroundColor = "var(--color-primary)";
					checkbox.style.borderColor = "var(--color-primary)";
					checkbox.style.borderWidth = "2px";
					checkbox.style.borderStyle = "solid";
					checkbox.style.borderRadius = "4px";
					checkbox.style.boxShadow = "0 0 0 2px var(--color-white)";
					checkbox.style.transform = "scale(1.2)";
					checkbox.style.transition = "all 0.2s ease";
					checkbox.nextElementSibling.style.textDecoration = "line-through";
					checkbox.nextElementSibling.style.color = "var(--color-gray-400)";
				} else {
					checkbox.style.backgroundColor = "var(--color-white)";
					checkbox.style.borderColor = "var(--color-gray-300)";
					checkbox.style.borderWidth = "1px";
					checkbox.style.borderStyle = "solid";
					checkbox.style.borderRadius = "4px";
					checkbox.style.boxShadow = "none";
					checkbox.style.transform = "scale(1)";
					checkbox.style.transition = "all 0.2s ease";
					checkbox.nextElementSibling.style.textDecoration = "none";
					checkbox.nextElementSibling.style.color = "var(--color-gray-800)";
				}
			}
		</script>
	</body>
</html>
