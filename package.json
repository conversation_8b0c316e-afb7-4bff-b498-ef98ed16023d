{"name": "uniapp-starter", "version": "1.0.0", "description": "uniapp 移动端跨平台脚手架", "author": "AaronZZH", "keywords": ["Uniapp", "TypeScript", "UnoCSS", "Pinia", "<PERSON><PERSON><PERSON>"], "scripts": {"本地开发": "", "start": "cross-env node ./deploy/index.js --model dev --env prod", "start:h5": "uni", "部署：生产环境": "", "deploy:wgt": "cross-env node ./deploy/index.js --platform app:wgt --env prod", "deploy": "cross-env node ./deploy/index.js --platform all --env prod", "deploy:apk": "cross-env node ./deploy/index.js --platform app:apk --env prod", "deploy:h5": "cross-env node ./deploy/index.js --platform H5 --env prod", "test": "jest --config tests/jest.config.js", "test:watch": "jest --config tests/jest.config.js --watch", "test:coverage": "jest --config tests/jest.config.js --coverage"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-alpha-4010520240507001", "@dcloudio/uni-app-plus": "3.0.0-alpha-4010520240507001", "@dcloudio/uni-components": "3.0.0-alpha-4010520240507001", "@dcloudio/uni-h5": "3.0.0-alpha-4010520240507001", "@dcloudio/uni-mp-alipay": "3.0.0-alpha-4010520240507001", "@dcloudio/uni-mp-baidu": "3.0.0-alpha-4010520240507001", "@dcloudio/uni-mp-jd": "3.0.0-alpha-4010520240507001", "@dcloudio/uni-mp-kuaishou": "3.0.0-alpha-4010520240507001", "@dcloudio/uni-mp-lark": "3.0.0-alpha-4010520240507001", "@dcloudio/uni-mp-qq": "3.0.0-alpha-4010520240507001", "@dcloudio/uni-mp-toutiao": "3.0.0-alpha-4010520240507001", "@dcloudio/uni-mp-weixin": "3.0.0-alpha-4010520240507001", "@dcloudio/uni-mp-xhs": "3.0.0-alpha-4010520240507001", "@dcloudio/uni-quickapp-webview": "3.0.0-alpha-4010520240507001", "@fortawesome/fontawesome-free": "^6.7.2", "@uni-helper/uni-app-types": "^0.5.9", "@uni-helper/uni-cloud-types": "^0.5.2", "@vueuse/shared": "^9.13.0", "blueimp-md5": "^2.18.0", "crypto-js": "^4.2.0", "currency.js": "^2.0.4", "dayjs": "^1.11.9", "dexie": "^4.0.7", "font-awesome": "^4.7.0", "less": "^4.2.0", "markdown-it": "^14.1.0", "marked": "^15.0.4", "pinia": "^2.0.32", "recordrtc": "^5.6.2", "rrule": "^2.8.1", "unocss": "^0.50.1", "vue": "^3.4.27", "vue-demi": "^0.13.11", "vue-i18n": "^9.2.2"}, "devDependencies": {"@babel/core": "^7.24.3", "@babel/preset-env": "^7.24.3", "@babel/preset-typescript": "^7.27.1", "@commitlint/cli": "^17.4.4", "@commitlint/config-conventional": "^17.4.4", "@dcloudio/types": "^3.4.8", "@dcloudio/uni-automator": "3.0.0-alpha-4010520240507001", "@dcloudio/uni-cli-shared": "3.0.0-alpha-4010520240507001", "@dcloudio/uni-stacktracey": "3.0.0-alpha-4010520240507001", "@dcloudio/vite-plugin-uni": "3.0.0-alpha-4010520240507001", "@iconify/json": "^2.2.26", "@jest/globals": "^27.0.0", "@nut-tree/nut-js": "^3.1.2", "@types/blueimp-md5": "^2.18.0", "@typescript-eslint/eslint-plugin": "^5.53.0", "@typescript-eslint/parser": "^5.53.0", "@unocss/eslint-config": "^0.58.5", "@unocss/preset-icons": "^0.50.1", "@vue/cli-plugin-unit-jest": "^5.0.8", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^9.1.0", "@vue/runtime-core": "^3.4.27", "@vue/test-utils": "^2.4.3", "@vueuse/core": "^9.13.0", "archiver": "^6.0.0", "axios": "^1.5.0", "babel-jest": "^27.0.0", "chalk": "4.1.0", "cos-nodejs-sdk-v5": "^2.12.4", "cross-env": "^7.0.3", "eslint": "^8.34.0", "eslint-plugin-prettier": "^3.1.0", "eslint-plugin-vue": "^8.7.1", "husky": "^8.0.3", "iconv-lite": "^0.6.3", "inquirer": "^7.3.3", "jest": "27.0.4", "jest-environment-jsdom": "^27.0.0", "lint-staged": "^13.1.2", "minimist": "^1.2.8", "node-ssh": "^13.1.0", "open": "^10.1.0", "ora": "5.1.0", "prettier": "^2.8.4", "sass": "^1.58.3", "ts-jest": "^27.1.5", "ts-node": "^10.9.1", "typescript": "^4.9.5", "unocss-preset-weapp": "^0.4.2", "unplugin-auto-import": "^0.11.5", "unplugin-vue-components": "^0.24.0", "vite": "^5.2.8", "vite-plugin-compression": "^0.5.1"}, "optionalDependencies": {"@fastify/autoload": "^5.7.1", "@fastify/sensible": "^5.2.0", "@types/node": "^18.14.0", "@types/tap": "^15.0.8", "concurrently": "^7.6.0", "fastify": "^4.13.0", "fastify-cli": "^5.7.1", "fastify-plugin": "^4.5.0", "fastify-tsconfig": "^1.0.1", "tap": "^16.3.4"}}