<template>
  <view
    :class="[
      'menu-item',
      { 'is-file': !item.children, 'is-folder': item.children },
      { 'top-level': isTopLevel, 'sub-level': !isTopLevel },
    ]"
  >
    <!-- 文件夹 -->
    <view v-if="item.children" class="folder-item" @click="toggleMenu">
      <view class="folder-icon">
        <view :class="['icon-folder', { 'is-open': isOpen }]"></view>
      </view>
      <text class="item-label">{{ item.label }}</text>
    </view>

    <!-- 文件 -->
    <view v-else class="file-item" @click="handleClick">
      <view class="file-icon">
        <view class="icon-file"></view>
      </view>
      <text class="item-label">{{ item.label }}</text>
    </view>

    <!-- 子菜单 -->
    <view v-if="item.children && item.children.length" class="submenu" :class="{ 'is-open': isOpen }">
      <z-menu-item v-for="(subItem, subIndex) in item.children" :key="subIndex" :item="subItem" :is-top-level="false" />
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'

// 组件属性
const props = defineProps({
  item: {
    type: Object,
    required: true,
  },
  isTopLevel: {
    type: Boolean,
    default: false,
  },
})

// 当前菜单项是否展开
const isOpen = ref(false)

// 切换菜单展开/收起状态
const toggleMenu = () => {
  isOpen.value = !isOpen.value
}

// 处理文件点击
const handleClick = () => {
  console.log('点击菜单项：', props.item)

  // 如果是文件项，跳转到笔记页面
  if (props.item.key) {
    uni.navigateTo({
      url: `/pages/speak/note?key=${encodeURIComponent(props.item.key)}&title=${encodeURIComponent(props.item.label)}`,
      fail: (err) => {
        console.error('页面跳转失败：', err)
        uni.showToast({
          title: '页面跳转失败',
          icon: 'none',
        })
      },
    })
  }
}
</script>

<style lang="scss" scoped>
.menu-item {
  margin-bottom: 8rpx;
  border-radius: 12rpx;
  overflow: hidden;
  transition: all 0.2s ease;

  &.top-level {
    margin-bottom: 12rpx;
  }

  &.is-file {
    .file-item {
      background-color: #ffffff;
      box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);

      &:active {
        background-color: #f9f9f9;
        box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.03);
      }
    }

    .file-icon {
      color: #5c87db;
    }
  }

  &.is-folder {
    &.top-level .folder-item {
      background-color: #f0f5ff;

      &:active {
        background-color: #e2edff;
      }

      .folder-icon {
        color: #2468f2;
      }

      .item-label {
        color: #2468f2;
        font-weight: 500;
      }
    }

    &.sub-level .folder-item {
      background-color: #f5f9ff;

      &:active {
        background-color: #edf4ff;
      }

      .folder-icon {
        color: #569aff;
      }

      .item-label {
        color: #3a7edc;
      }
    }
  }

  .folder-item,
  .file-item {
    padding: 20rpx 24rpx;
    display: flex;
    align-items: center;
    cursor: pointer;
    border-radius: 12rpx;
  }

  .folder-icon,
  .file-icon {
    width: 36rpx;
    height: 36rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 16rpx;
    position: relative;

    .icon-folder {
      width: 30rpx;
      height: 24rpx;
      border-radius: 4rpx 4rpx 2rpx 2rpx;
      border: 2rpx solid currentColor;
      border-top: 8rpx solid currentColor;
      position: relative;
      transition: all 0.3s ease;

      &.is-open {
        height: 20rpx;
      }

      &:before {
        content: '';
        position: absolute;
        width: 10rpx;
        height: 2rpx;
        background-color: currentColor;
        top: -5rpx;
        left: 10rpx;
      }
    }

    .icon-file {
      width: 26rpx;
      height: 32rpx;
      border: 2rpx solid currentColor;
      border-radius: 2rpx;
      position: relative;

      &:before {
        content: '';
        position: absolute;
        width: 16rpx;
        height: 2rpx;
        background-color: currentColor;
        top: 8rpx;
        left: 5rpx;
      }

      &:after {
        content: '';
        position: absolute;
        width: 16rpx;
        height: 2rpx;
        background-color: currentColor;
        top: 16rpx;
        left: 5rpx;
      }
    }
  }

  .item-label {
    font-size: 28rpx;
    color: #333;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .submenu {
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease-in-out;
    opacity: 0;
    transform: translateY(-5rpx);
    padding: 0 0 0 50rpx;

    &.is-open {
      max-height: 2000rpx;
      opacity: 1;
      transform: translateY(0);
      padding-top: 8rpx;
      padding-bottom: 8rpx;
    }
  }
}
</style>
