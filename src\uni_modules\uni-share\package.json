{"id": "uni-share", "displayName": "uni-share", "version": "2.0.2", "description": "底部弹出宫格图标式的分享菜单，可覆盖原生组件。", "keywords": ["分享菜单"], "repository": "", "engines": {"HBuilderX": "^3.1.0"}, "dcloudext": {"category": ["JS SDK", "通用 SDK"], "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": [], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"App": {"app-vue": "y", "app-nvue": "y"}, "H5-mobile": {"Safari": "n", "Android Browser": "n", "微信浏览器(Android)": "n", "QQ浏览器(Android)": "n"}, "H5-pc": {"Chrome": "n", "IE": "n", "Edge": "n", "Firefox": "n", "Safari": "n"}, "小程序": {"微信": "n", "阿里": "n", "百度": "n", "字节跳动": "n", "QQ": "n"}, "快应用": {"华为": "n", "联盟": "n"}, "Vue": {"vue2": "y", "vue3": "y"}}}}}