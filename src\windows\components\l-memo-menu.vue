<template>
  <view>
    <view class="page-margin">
      <view class="flex flex-1 h-80 justify-between items-center mb-4">
        <view class="text-45">日记</view>
      </view>
      <!-- <view class="p-2 rr bg-white cursor-pointer" @click="goToday">  </view> -->
    </view>
  </view>
</template>

<script setup>
onShow(() => {
  uni.switchTab({
    url: '/pages/memo/diary',
  })
})
</script>

<style lang="scss" scoped></style>
