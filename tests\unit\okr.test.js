/**
 * OKR 模块单元测试
 * 测试 OKR 相关 API 和功能
 */
import { describe, it, expect, beforeEach, jest } from '@jest/globals'
import * as okrApi from '@/api/okr'
import * as taskApi from '@/api/task'
import { calculateWeightedProgress, calculateKrProgress } from '@/utils/okrCalculationUtils'
import { createMockOkr, createMockKeyResult, mockDbTable, mockUniApi } from '../helpers'

// 模拟 API 调用
jest.mock('@/api/okr')
jest.mock('@/api/task')

describe('OKR 模块测试', () => {
  beforeEach(() => {
    // 清除所有 mock 的调用历史
    jest.clearAllMocks()
    // 模拟 uni-app API
    mockUniApi()
  })

  // ===== OKR 目标相关测试 =====
  describe('OKR 目标基础功能', () => {
    it('应能成功创建 OKR 目标', async () => {
      // 准备测试数据
      const mockOkr = createMockOkr()
      okrApi.addOkrApi.mockResolvedValue('new-okr-id')

      // 执行 API 调用
      const result = await okrApi.addOkrApi(mockOkr)

      // 断言结果
      expect(result).toBe('new-okr-id')
      expect(okrApi.addOkrApi).toHaveBeenCalledWith(mockOkr)
    })

    it('应能成功获取 OKR 目标详情', async () => {
      // 准备测试数据
      const mockOkr = createMockOkr()
      okrApi.getOkrApi.mockResolvedValue(mockOkr)

      // 执行 API 调用
      const result = await okrApi.getOkrApi('mock-okr-id')

      // 断言结果
      expect(result).toEqual(mockOkr)
      expect(okrApi.getOkrApi).toHaveBeenCalledWith('mock-okr-id')
    })

    it('应能成功更新 OKR 目标', async () => {
      // 准备测试数据
      const mockOkr = createMockOkr({ title: '更新后的目标标题' })
      okrApi.updateOkrApi.mockResolvedValue(undefined)

      // 执行 API 调用
      await okrApi.updateOkrApi('mock-okr-id', mockOkr)

      // 断言结果
      expect(okrApi.updateOkrApi).toHaveBeenCalledWith('mock-okr-id', mockOkr)
    })

    it('应能成功删除 OKR 目标', async () => {
      // 模拟删除函数
      okrApi.delOkrApi.mockResolvedValue(undefined)

      // 执行 API 调用
      await okrApi.delOkrApi('mock-okr-id')

      // 断言结果
      expect(okrApi.delOkrApi).toHaveBeenCalledWith('mock-okr-id')
    })

    it('应能获取 OKR 目标列表', async () => {
      // 准备测试数据
      const mockOkrList = [
        createMockOkr({ _id: 'okr-1', title: '目标 1' }),
        createMockOkr({ _id: 'okr-2', title: '目标 2' }),
      ]
      okrApi.getOkrListApi.mockResolvedValue(mockOkrList)

      // 执行 API 调用
      const result = await okrApi.getOkrListApi()

      // 断言结果
      expect(result).toEqual(mockOkrList)
      expect(result.length).toBe(2)
    })
  })

  // ===== 关键结果 (KR) 相关测试 =====
  describe('关键结果 (KR) 功能测试', () => {
    it('应能成功创建关键结果', async () => {
      // 准备测试数据
      const mockKr = createMockKeyResult()
      taskApi.addTaskApi.mockResolvedValue('new-kr-id')

      // 执行 API 调用
      const result = await taskApi.addTaskApi(mockKr)

      // 断言结果
      expect(result).toBe('new-kr-id')
      expect(taskApi.addTaskApi).toHaveBeenCalledWith(mockKr)
    })

    it('应能成功获取关键结果详情', async () => {
      // 准备测试数据
      const mockKr = createMockKeyResult()
      taskApi.getTaskApi.mockResolvedValue(mockKr)

      // 执行 API 调用
      const result = await taskApi.getTaskApi('mock-kr-id')

      // 断言结果
      expect(result).toEqual(mockKr)
      expect(taskApi.getTaskApi).toHaveBeenCalledWith('mock-kr-id')
    })

    it('应能成功更新关键结果', async () => {
      // 准备测试数据
      const mockKr = createMockKeyResult({ title: '更新后的关键结果标题' })
      taskApi.updateTaskApi.mockResolvedValue(undefined)

      // 执行 API 调用
      await taskApi.updateTaskApi('mock-kr-id', mockKr)

      // 断言结果
      expect(taskApi.updateTaskApi).toHaveBeenCalledWith('mock-kr-id', mockKr)
    })

    it('应能获取 OKR 关联的关键结果列表', async () => {
      // 准备测试数据
      const mockKrList = [
        createMockKeyResult({ _id: 'kr-1', title: '关键结果 1' }),
        createMockKeyResult({ _id: 'kr-2', title: '关键结果 2' }),
      ]
      taskApi.getTaskListApi.mockResolvedValue(mockKrList)

      // 执行 API 调用
      const result = await taskApi.getTaskListApi(`okrId == "mock-okr-id" && parentId == "" && type == "kr"`)

      // 断言结果
      expect(result).toEqual(mockKrList)
      expect(result.length).toBe(2)
    })
  })

  // ===== OKR 进度计算测试 =====
  describe('OKR 进度计算功能', () => {
    it('应能正确计算单个关键结果的进度百分比', () => {
      // 测试基本情况
      expect(calculateKrProgress(50, 100, 0)).toBe(50)

      // 测试超出范围的情况
      expect(calculateKrProgress(120, 100, 0)).toBe(100) // 超过 100% 应该被限制为 100%
      expect(calculateKrProgress(-10, 100, 0)).toBe(0) // 小于 0% 应该被限制为 0%

      // 测试有初始值的情况
      expect(calculateKrProgress(60, 100, 20)).toBe(50) // (60-20)/(100-20)=50%
    })

    it('应能正确计算加权平均的 OKR 整体进度', () => {
      // 准备测试数据
      const keyResults = [
        { curVal: 50, tgtVal: 100, weight: 2 }, // 进度 50%，权重 2
        { curVal: 75, tgtVal: 100, weight: 1 }, // 进度 75%，权重 1
      ]

      // 计算加权平均：(50*2 + 75*1) / (2+1) = 58.33，四舍五入为 58
      const progress = calculateWeightedProgress(keyResults)
      expect(progress).toBe(58)

      // 测试空数组情况
      expect(calculateWeightedProgress([])).toBe(0)

      // 测试默认权重情况
      const keyResultsNoWeight = [
        { curVal: 50, tgtVal: 100 }, // 默认权重为 1
        { curVal: 75, tgtVal: 100 }, // 默认权重为 1
      ]
      expect(calculateWeightedProgress(keyResultsNoWeight)).toBe(63) // (50+75)/2=62.5，四舍五入为 63
    })
  })

  // ===== 数据完整性和边界条件测试 =====
  describe('数据完整性和边界条件测试', () => {
    it('应处理空数据情况', async () => {
      // 模拟返回空结果
      okrApi.getOkrApi.mockResolvedValue(null)

      // 执行 API 调用
      const result = await okrApi.getOkrApi('non-existent-id')

      // 断言结果
      expect(result).toBeNull()
    })

    it('应处理删除不存在的 OKR', async () => {
      // 模拟删除函数返回正常结果
      okrApi.delOkrApi.mockResolvedValue(undefined)

      // 执行 API 调用，不应抛出异常
      await expect(okrApi.delOkrApi('non-existent-id')).resolves.not.toThrow()
    })

    it('应处理 API 异常情况', async () => {
      // 模拟 API 调用失败
      okrApi.getOkrApi.mockRejectedValue(new Error('API 调用失败'))

      // 执行 API 调用
      await expect(okrApi.getOkrApi('mock-okr-id')).rejects.toThrow('API 调用失败')
    })
  })
})
