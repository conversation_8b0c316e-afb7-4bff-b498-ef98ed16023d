<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>今日任务</title>
		<link
			href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css"
			rel="stylesheet"
		/>
		<link
			rel="stylesheet"
			href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
		/>
		<style>
			/* Shared variables from shared-components.html */
			:root {
				--color-primary: #5e6ad2; /* Default from shared, overridden by today-tasks specific below */
				--color-primary-light: #7c84e8;
				--color-primary-dark: #4549a9;
				--color-secondary: #4ea8de; /* Default from shared, overridden by today-tasks specific below */
				--color-secondary-light: #70c1e8;
				--color-secondary-dark: #2889c9;
				--color-accent: #ff7c7c; /* Default from shared, overridden by today-tasks specific below */
				--color-accent-light: #ff9e9e;
				--color-accent-dark: #ff5252;
				--color-success: #56c993;
				--color-warning: #ffae57;
				--color-danger: #ff6b6b;
				--color-bg: #f8f9ff;
				--color-white: #ffffff;
				--color-gray-100: #f5f7ff;
				--color-gray-200: #ebeefe;
				--color-gray-300: #d8dcef;
				--color-gray-400: #b0b5d1;
				--color-gray-500: #868aaf;
				--color-gray-600: #585d80;
				--color-gray-700: #424366;
				--color-gray-800: #2b2c44;
				--color-gray-900: #1a1b2e;
				--rounded-lg: 16px;
				--rounded-md: 12px;
				--rounded-sm: 8px;
				--shadow-sm: 0 2px 8px rgba(94, 106, 210, 0.08);
				--shadow-md: 0 4px 16px rgba(94, 106, 210, 0.12);
				--shadow-lg: 0 8px 24px rgba(94, 106, 210, 0.16);
			}

			/* today-tasks.html specific theme variables (takes precedence) */
			:root {
				--primary-color: #2196f3; /* today-tasks specific */
				--secondary-color: #00bcd4; /* today-tasks specific */
				--accent-color: #ff5722; /* today-tasks specific */
			}

			/* General body style from shared-components.html, adapted */
			body {
				font-family: "Poppins", "PingFang SC", "Helvetica Neue", Arial,
					sans-serif; /* Combined font stack */
				background: var(--color-bg); /* From shared */
				color: var(--color-gray-800); /* From shared */
				padding-bottom: 70px; /* From today-tasks */
				display: flex; /* Added for potential full-page layout needs */
				flex-direction: column; /* Added for potential full-page layout needs */
				min-height: 100vh; /* Ensure body takes full height */
			}

			/* Styles from today-tasks.html */
			.calendar-week {
				display: flex;
				justify-content: space-between;
			}
			.calendar-day {
				text-align: center;
				cursor: pointer;
				transition: all 0.2s ease;
			}
			.calendar-day:hover {
				background-color: #f9fafb;
			}
			.calendar-day.active .w-8 {
				/* Uses --primary-color from today-tasks */
				background-color: var(--primary-color);
				color: white;
			}
			.task-item {
				/* border-left removed as per original file comment */
			}

			.add-task-btn {
				position: fixed;
				right: 20px;
				bottom: 80px; /* Adjusted to be above new tab bar height */
				width: 56px;
				height: 56px;
				border-radius: 50%;
				background-color: var(--primary-color);
				color: white;
				display: flex;
				align-items: center;
				justify-content: center;
				box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
				z-index: 100;
				transition: all 0.2s ease;
			}
			.add-task-btn:active {
				transform: scale(0.95);
			}
			.add-task-btn:hover {
				box-shadow: 0 6px 8px rgba(0, 0, 0, 0.15);
				background-color: var(--secondary-color);
			}

			.week-overview-btn {
				padding: 5px 10px;
				background-color: var(--primary-color);
				color: white;
				border-radius: 16px;
				font-size: 12px;
				display: inline-flex;
				align-items: center;
				gap: 4px;
				transition: all 0.2s ease;
			}
			.week-overview-btn:hover {
				background-color: var(--secondary-color);
			}

			.week-overview-modal {
				display: none;
				position: fixed;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background-color: rgba(0, 0, 0, 0.5);
				z-index: 1000;
				align-items: center;
				justify-content: center;
			}
			.week-overview-content {
				background-color: white;
				border-radius: 12px;
				padding: 20px;
				width: 300px;
				max-width: 90%;
			}
			.overview-stats {
				display: flex;
				justify-content: space-around;
				margin: 15px 0;
				text-align: center;
			}
			.stat-item {
				display: flex;
				flex-direction: column;
				align-items: center;
			}
			.stat-value {
				/* Uses --primary-color from today-tasks */
				font-size: 24px;
				font-weight: bold;
				color: var(--primary-color);
			}
			.stat-label {
				font-size: 12px;
				color: #666;
			}
			.close-btn {
				display: block;
				width: 100%;
				padding: 8px;
				background-color: #f1f1f1;
				border: none;
				border-radius: 8px;
				margin-top: 15px;
				color: #333;
				font-weight: 500;
				text-align: center;
			}

			/* Styles for page content switching */
			.page-content {
				flex-grow: 1; /* Allow content to fill space */
				overflow-y: auto; /* Scroll if content overflows */
			}

			/* Shared component styles from shared-components.html (specific classes) */
			/* (Assuming ios-status-bar might not be directly used, but tab-bar styles are relevant) */

			/* Bottom Navigation Bar (adapted from .ios-tab-bar in shared-components.html and today-tasks nav) */
			.main-nav {
				position: fixed;
				bottom: 0;
				left: 0;
				right: 0;
				background: var(--color-white); /* from shared */
				display: flex;
				justify-content: space-around;
				align-items: center; /* Vertically center items */
				height: 65px; /* Consistent height */
				padding-top: 5px; /* Space for icon and text */
				padding-bottom: 5px; /* For safe area / visual padding */
				box-shadow: 0 -2px 10px rgba(94, 106, 210, 0.08); /* from shared */
				border-radius: 20px 20px 0 0; /* from shared, adjusted */
				z-index: 1000;
			}

			.main-nav-item {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center; /* Center content within item */
				flex: 1; /* Distribute space equally */
				padding: 5px 0;
				color: var(--color-gray-500); /* from shared .tab-item */
				font-size: 11px; /* Slightly smaller for compact look */
				font-weight: 500; /* from shared .tab-item */
				transition: all 0.3s ease;
				text-decoration: none;
				cursor: pointer;
			}

			.main-nav-item i {
				font-size: 20px; /* from today-tasks nav */
				margin-bottom: 3px; /* Space between icon and text */
			}

			.main-nav-item.active {
				color: var(
					--primary-color
				); /* Use today-tasks primary color for active */
			}
			.main-nav-item.active i {
				color: var(--primary-color); /* Ensure icon also uses primary color */
			}

			/* Additional styles from home.html, tasks.html, analytics.html, settings.html will be scoped or specific */
			/* home.html styles (example - if any unique root/body or major component styles) */
			/* For instance, .header-card from home.html if used */
			.header-card {
				/* From home.html */
				padding: 28px;
				position: relative;
				overflow: hidden;
				border-radius: var(--rounded-lg);
				background: linear-gradient(
					135deg,
					var(--color-primary-light),
					var(--color-primary)
				);
				border: none;
				box-shadow: var(--shadow-md);
				color: white;
			}
			.header-card h2 {
				font-size: 22px;
				font-weight: 600;
				margin-bottom: 8px;
			}
			.header-card p {
				opacity: 0.9;
				font-size: 14px;
				margin-bottom: 16px;
			}
			.header-card::before {
				content: "";
				position: absolute;
				top: -50px;
				right: -50px;
				width: 150px;
				height: 150px;
				border-radius: 50%;
				background: rgba(255, 255, 255, 0.1);
			}
			.header-card::after {
				content: "";
				position: absolute;
				bottom: -30px;
				left: -30px;
				width: 100px;
				height: 100px;
				border-radius: 50%;
				background: rgba(255, 255, 255, 0.08);
			}
			.app-header h1 {
				/* From home.html */
				font-size: 28px;
				font-weight: 700;
				color: var(--color-primary);
				letter-spacing: -0.5px;
				background: linear-gradient(
					120deg,
					var(--color-primary),
					var(--color-primary-dark)
				);
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
			}

			/* tasks.html specific styles (example) */
			.task-list-container {
				padding: 0 20px;
			} /* From tasks.html .task-list */
			.task-section {
				margin-top: 20px;
				margin-bottom: 24px;
			}
			.section-header {
				margin-bottom: 12px;
				font-weight: 600;
				font-size: 16px;
				color: var(--color-gray-700);
				display: flex;
				align-items: center;
				justify-content: space-between;
			}
			.task-card {
				/* from tasks.html */
				background: var(--color-white);
				border: 1px solid var(--color-gray-200);
				border-radius: var(--rounded-lg);
				margin-bottom: 12px;
				transition: all 0.2s ease;
				position: relative;
				overflow: hidden;
			}
			.task-card:hover {
				border-color: var(--color-gray-300);
			}
			.task-card.overdue {
				border-left: 4px solid var(--color-danger);
			}
			.task-card.today {
				border-left: 4px solid var(--color-warning);
			}
			.task-card.upcoming {
				border-left: 4px solid var(--color-success);
			}
			.task-card.no-date {
				border-left: 4px solid var(--color-gray-400);
			}
			.task-content {
				padding: 16px;
			}
			.task-header {
				display: flex;
				align-items: flex-start;
				margin-bottom: 10px;
			}
			.task-checkbox {
				/* from tasks.html - note: today-tasks.html also has checkbox styling. Ensure no conflict or decide precedence */
				width: 22px;
				height: 22px;
				border: 2px solid var(--color-gray-300);
				border-radius: var(--rounded-sm);
				margin-right: 12px;
				flex-shrink: 0;
				position: relative;
				transition: all 0.2s ease;
				cursor: pointer;
			}
			.task-checkbox:hover {
				border-color: var(--color-primary);
			}
			.task-checkbox.checked {
				background-color: var(--color-primary);
				border-color: var(--color-primary);
			}
			.task-checkbox.checked::after {
				content: "";
				position: absolute;
				top: 4px;
				left: 7px;
				width: 6px;
				height: 10px;
				border: solid white;
				border-width: 0 2px 2px 0;
				transform: rotate(45deg);
			}
			.task-title {
				font-weight: 500;
				font-size: 16px;
				color: var(--color-gray-800);
				margin-right: auto;
				line-height: 1.4;
			}
			.tab-bar {
				/* From tasks.html */
				display: flex;
				background-color: var(--color-gray-100);
				border-radius: var(--rounded-md);
				padding: 2px;
				margin-bottom: 16px;
				border: 1px solid var(--color-gray-200);
			}
			.tab-item {
				/* From tasks.html - potentially conflicts with .tab-item from shared-components for nav */
				flex: 1;
				text-align: center;
				padding: 10px 0;
				font-size: 14px;
				border-radius: calc(var(--rounded-md) - 2px);
				transition: all 0.2s ease;
				font-weight: 500;
				color: var(--color-gray-600);
			}
			.tab-item.active {
				background: var(--color-primary);
				color: white;
			}

			/* analytics.html specific styles (example) */
			.segment-control {
				/* From analytics.html */
				display: flex;
				background-color: var(--color-gray-100);
				border-radius: var(--rounded-lg);
				padding: 4px;
				margin-bottom: 20px;
				box-shadow: var(--shadow-sm);
				border: none;
			}
			.segment-item {
				/* From analytics.html */
				flex: 1;
				text-align: center;
				padding: 12px 0;
				font-size: 14px;
				border-radius: calc(var(--rounded-lg) - 4px);
				transition: all 0.3s ease;
				font-weight: 500;
				color: var(--color-gray-600);
			}
			.segment-item.active {
				background: linear-gradient(
					120deg,
					var(--color-primary),
					var(--color-primary-dark)
				);
				color: white;
				box-shadow: 0 4px 10px rgba(94, 106, 210, 0.25);
				transform: translateY(-1px);
			}
			.section-card {
				/* From analytics.html */
				background: var(--color-white);
				border-radius: var(--rounded-lg);
				margin: 0 20px 20px;
				padding: 22px;
				box-shadow: var(--shadow-sm);
				transition: all 0.3s ease;
			}
			.section-card:hover {
				transform: translateY(-3px);
				box-shadow: var(--shadow-md);
			}
			.section-title {
				/* From analytics.html */
				font-size: 18px;
				font-weight: 600;
				color: var(--color-gray-800);
				margin-bottom: 16px;
				display: flex;
				align-items: center;
			}
			.section-title i {
				margin-right: 10px;
				color: var(--color-primary);
				font-size: 20px;
			}

			/* settings.html specific styles (example) */
			.glass-card {
				/* from settings.html and shared-components */
				background: var(
					--card-glass,
					rgba(255, 255, 255, 0.6)
				); /* Provide fallback for --card-glass */
				backdrop-filter: blur(10px);
				-webkit-backdrop-filter: blur(10px);
				border-radius: var(--rounded-lg);
				border: 1px solid rgba(255, 255, 255, 0.6);
				box-shadow: var(--shadow-md);
				transition: all 0.3s ease;
				overflow: hidden;
				margin-bottom: 20px;
			}
			.glass-card:hover {
				transform: translateY(-3px);
				box-shadow: var(--shadow-lg);
			}
			.profile-card {
				padding: 24px;
				position: relative;
				overflow: hidden;
				margin-top: 20px;
			}
			.profile-avatar {
				width: 80px;
				height: 80px;
				border-radius: 24px;
				overflow: hidden;
				box-shadow: var(--shadow-md);
				border: 3px solid white;
				position: relative;
			}
			.avatar-image {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}
			.profile-name {
				font-size: 24px;
				font-weight: 700;
				margin-bottom: 4px;
				background: var(
					--gradient-primary,
					linear-gradient(
						to right,
						var(--color-primary),
						var(--color-primary-dark)
					)
				); /* Fallback for gradient */
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
				background-clip: text;
			}

			/* Ensure content area padding accounts for fixed header if any page uses one */
			.content-area {
				/* from shared-components.html, but use as general page content wrapper */
				flex: 1;
				overflow-y: auto;
				/* padding-top: 44px; /* If a global fixed header like ios-status-bar is used */
				padding-bottom: 80px; /* Space for the main navigation bar */
				width: 100%;
				max-width: 800px; /* Optional: for wider screens */
				margin: 0 auto; /* Center content */
			}
			/* Add a general class for main content within each page section to apply common padding */
			.page-main-content {
				padding: 16px;
			}
		</style>
	</head>
	<body class="bg-gray-100">
		<!-- This class might be overridden by a page's own body class if more specific -->

		<!-- This header is from the original today-tasks.html, kept for the 'Today' page -->
		<header class="bg-white p-4 shadow" id="today-tasks-header">
			<!-- Removed h1 as per original comment -->
		</header>

		<!-- Main content wrapper for page switching -->
		<main id="app-main-content" class="flex-grow overflow-auto">
			<!-- Today Tasks Page Content (Original Content) -->
			<div id="page-today-tasks" class="page-content active-page">
				<!-- 日历周视图 -->
				<section class="bg-white p-4 mt-2 shadow-sm rounded-lg">
					<div class="flex justify-between items-center mb-4">
						<div class="flex items-center">
							<span class="text-gray-800 font-medium">2023 年 11 月</span>
							<button
								class="ml-2 bg-gray-100 hover:bg-gray-200 rounded-full w-6 h-6 flex items-center justify-center text-gray-600"
								title="查看月历"
							>
								<i class="fas fa-calendar-alt text-xs"></i>
							</button>
						</div>
						<div class="flex gap-2 items-center">
							<button class="week-overview-btn" id="weekOverviewBtn">
								<i class="fas fa-chart-pie text-xs"></i>
								<span>周概览</span>
							</button>
							<button
								class="bg-gray-100 hover:bg-gray-200 rounded-full w-8 h-8 flex items-center justify-center text-gray-600"
								id="prev-week"
							>
								<i class="fas fa-chevron-left"></i>
							</button>
							<button
								class="bg-gray-100 hover:bg-gray-200 rounded-full w-8 h-8 flex items-center justify-center text-gray-600"
								id="next-week"
							>
								<i class="fas fa-chevron-right"></i>
							</button>
						</div>
					</div>
					<div class="calendar-week py-2 flex justify-between">
						<div
							class="calendar-day rounded-lg hover:bg-gray-50 p-2 transition cursor-pointer"
						>
							<div class="text-xs font-medium text-gray-500 mb-1">周一</div>
							<div
								class="flex items-center justify-center w-8 h-8 mx-auto rounded-full"
							>
								13
							</div>
						</div>
						<div
							class="calendar-day rounded-lg hover:bg-gray-50 p-2 transition cursor-pointer"
						>
							<div class="text-xs font-medium text-gray-500 mb-1">周二</div>
							<div
								class="flex items-center justify-center w-8 h-8 mx-auto rounded-full"
							>
								14
							</div>
						</div>
						<div
							class="calendar-day rounded-lg hover:bg-gray-50 p-2 transition cursor-pointer"
						>
							<div class="text-xs font-medium text-gray-500 mb-1">周三</div>
							<div
								class="flex items-center justify-center w-8 h-8 mx-auto rounded-full"
							>
								15
							</div>
						</div>
						<div
							class="calendar-day active rounded-lg hover:bg-gray-50 p-2 transition cursor-pointer"
						>
							<div class="text-xs font-medium text-gray-500 mb-1">周四</div>
							<div
								class="flex items-center justify-center w-8 h-8 mx-auto rounded-full bg-blue-500 text-white"
							>
								16
							</div>
						</div>
						<div
							class="calendar-day rounded-lg hover:bg-gray-50 p-2 transition cursor-pointer"
						>
							<div class="text-xs font-medium text-gray-500 mb-1">周五</div>
							<div
								class="flex items-center justify-center w-8 h-8 mx-auto rounded-full"
							>
								17
							</div>
						</div>
						<div
							class="calendar-day rounded-lg hover:bg-gray-50 p-2 transition cursor-pointer"
						>
							<div class="text-xs font-medium text-gray-500 mb-1">周六</div>
							<div
								class="flex items-center justify-center w-8 h-8 mx-auto rounded-full text-blue-400"
							>
								18
							</div>
						</div>
						<div
							class="calendar-day rounded-lg hover:bg-gray-50 p-2 transition cursor-pointer"
						>
							<div class="text-xs font-medium text-gray-500 mb-1">周日</div>
							<div
								class="flex items-center justify-center w-8 h-8 mx-auto rounded-full text-blue-400"
							>
								19
							</div>
						</div>
					</div>
					<div class="mt-2 flex justify-center">
						<span
							class="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full"
						>
							<i class="fas fa-check-circle mr-1 text-green-500"></i>今日任务：5
							项待办
						</span>
					</div>
				</section>
				<!-- 今日任务 -->
				<section class="mt-4 px-4">
					<h2 class="text-lg font-semibold mb-3">今日待办 (5)</h2>
					<div class="mb-6">
						<div class="flex items-center mb-2">
							<div class="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
							<span class="text-sm font-medium text-gray-700"
								>提高团队协作效率</span
							>
						</div>
						<div class="space-y-3">
							<div class="task-item bg-white p-3 rounded-lg shadow-sm">
								<div class="flex justify-between items-start">
									<div class="flex-1">
										<div class="flex items-center">
											<input
												type="checkbox"
												class="mr-2 h-5 w-5 text-blue-500"
											/>
											<span class="font-medium">完成团队周报汇总</span>
										</div>
										<div class="mt-1 ml-7 text-sm text-gray-500">
											<span
												class="bg-blue-100 text-blue-700 rounded px-2 py-0.5 text-xs"
												>KR: 每周按时提交团队报告</span
											>
										</div>
										<div class="flex items-center mt-2 ml-7">
											<i class="far fa-clock text-xs text-gray-400 mr-1"></i>
											<span class="text-xs text-gray-500"
												>截止：今天 17:00</span
											>
											<i
												class="ml-3 fas fa-arrow-up text-xs text-blue-500"
												title="贡献进度 +20%"
											></i>
										</div>
									</div>
								</div>
							</div>
							<div class="task-item bg-white p-3 rounded-lg shadow-sm">
								<div class="flex justify-between items-start">
									<div class="flex-1">
										<div class="flex items-center">
											<input
												type="checkbox"
												class="mr-2 h-5 w-5 text-blue-500"
											/>
											<span class="font-medium">安排下周冲刺计划会议</span>
										</div>
										<div class="mt-1 ml-7 text-sm text-gray-500">
											<span
												class="bg-blue-100 text-blue-700 rounded px-2 py-0.5 text-xs"
												>KR: 建立高效会议机制</span
											>
										</div>
										<div class="flex items-center mt-2 ml-7">
											<i class="far fa-clock text-xs text-gray-400 mr-1"></i>
											<span class="text-xs text-gray-500"
												>截止：今天 15:30</span
											>
											<i
												class="ml-3 fas fa-arrow-up text-xs text-blue-500"
												title="贡献进度 +10%"
											></i>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="mb-6">
						<div class="flex items-center mb-2">
							<div class="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
							<span class="text-sm font-medium text-gray-700"
								>优化产品用户体验</span
							>
						</div>
						<div class="space-y-3">
							<div class="task-item bg-white p-3 rounded-lg shadow-sm">
								<div class="flex justify-between items-start">
									<div class="flex-1">
										<div class="flex items-center">
											<input
												type="checkbox"
												class="mr-2 h-5 w-5 text-green-500"
											/>
											<span class="font-medium">分析用户反馈数据</span>
										</div>
										<div class="mt-1 ml-7 text-sm text-gray-500">
											<span
												class="bg-green-100 text-green-700 rounded px-2 py-0.5 text-xs"
												>KR: 提高用户满意度指标</span
											>
										</div>
										<div class="flex items-center mt-2 ml-7">
											<i class="far fa-clock text-xs text-gray-400 mr-1"></i>
											<span class="text-xs text-gray-500"
												>截止：今天 14:00</span
											>
											<i
												class="ml-3 fas fa-arrow-up text-xs text-green-500"
												title="贡献进度 +15%"
											></i>
										</div>
									</div>
								</div>
							</div>
							<div class="task-item bg-white p-3 rounded-lg shadow-sm">
								<div class="flex justify-between items-start">
									<div class="flex-1">
										<div class="flex items-center">
											<input
												type="checkbox"
												class="mr-2 h-5 w-5 text-green-500"
											/>
											<span class="font-medium">完成新功能原型设计</span>
										</div>
										<div class="mt-1 ml-7 text-sm text-gray-500">
											<span
												class="bg-green-100 text-green-700 rounded px-2 py-0.5 text-xs"
												>KR: 开发 3 个创新功能</span
											>
										</div>
										<div class="flex items-center mt-2 ml-7">
											<i class="far fa-clock text-xs text-gray-400 mr-1"></i>
											<span class="text-xs text-gray-500"
												>截止：今天 16:00</span
											>
											<i
												class="ml-3 fas fa-arrow-up text-xs text-green-500"
												title="贡献进度 +25%"
											></i>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="mb-6">
						<div class="flex items-center mb-2">
							<div class="w-3 h-3 rounded-full bg-orange-500 mr-2"></div>
							<span class="text-sm font-medium text-gray-700"
								>拓展业务渠道</span
							>
						</div>
						<div class="space-y-3">
							<div class="task-item bg-white p-3 rounded-lg shadow-sm">
								<div class="flex justify-between items-start">
									<div class="flex-1">
										<div class="flex items-center">
											<input
												type="checkbox"
												class="mr-2 h-5 w-5 text-orange-500"
											/>
											<span class="font-medium">联系潜在合作伙伴</span>
										</div>
										<div class="mt-1 ml-7 text-sm text-gray-500">
											<span
												class="bg-orange-100 text-orange-700 rounded px-2 py-0.5 text-xs"
												>KR: 建立 5 个新合作伙伴关系</span
											>
										</div>
										<div class="flex items-center mt-2 ml-7">
											<i class="far fa-clock text-xs text-gray-400 mr-1"></i>
											<span class="text-xs text-gray-500"
												>截止：今天 12:00</span
											>
											<i
												class="ml-3 fas fa-arrow-up text-xs text-orange-500"
												title="贡献进度 +20%"
											></i>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</section>
			</div>

			<!-- Home Page Content (Extracted from home.html) -->
			<div id="page-home" class="page-content" style="display: none">
				<div class="content-area page-main-content">
					<!-- content-area from home.html -->
					<div class="app-header">
						<!-- from home.html -->
						<h1>OKR Dashboard</h1>
						<!-- Removed user/notification icons for simplicity in merged view -->
					</div>
					<div class="header-card">
						<!-- from home.html -->
						<h2>下午好, [用户名称]!</h2>
						<p>今天是充满机遇的一天，专注于你的目标，让每一刻都富有成效。</p>
						<a
							href="javascript:void(0);"
							class="inline-block bg-white text-sm text-blue-600 font-semibold px-6 py-3 rounded-lg shadow hover:bg-gray-100 transition"
							>查看本周计划</a
						>
					</div>
					<!-- Placeholder for other home content, e.g., OKR summaries -->
					<div class="mt-6">
						<h3 class="text-xl font-semibold text-gray-700 mb-4">
							我的目标概览
						</h3>
						<div class="grid grid-cols-1 md:grid-cols-2 gap-6">
							<!-- Example OKR card (structure based on typical dashboard items) -->
							<div
								class="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow"
							>
								<div class="flex items-center justify-between mb-3">
									<span
										class="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-pink-600 bg-pink-200"
										>Q4 2023</span
									>
									<span class="text-sm font-medium text-gray-500">75%</span>
								</div>
								<h4 class="text-lg font-semibold text-gray-800 mb-1">
									提升产品用户体验
								</h4>
								<p class="text-sm text-gray-600 mb-3">
									通过收集用户反馈和迭代设计，显著提高用户满意度。
								</p>
								<div
									class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700"
								>
									<div
										class="bg-pink-500 h-2.5 rounded-full"
										style="width: 75%"
									></div>
								</div>
							</div>
							<div
								class="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow"
							>
								<div class="flex items-center justify-between mb-3">
									<span
										class="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-green-600 bg-green-200"
										>Q4 2023</span
									>
									<span class="text-sm font-medium text-gray-500">60%</span>
								</div>
								<h4 class="text-lg font-semibold text-gray-800 mb-1">
									拓展营销渠道
								</h4>
								<p class="text-sm text-gray-600 mb-3">
									开发新的合作伙伴关系，并优化现有渠道的转化率。
								</p>
								<div
									class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700"
								>
									<div
										class="bg-green-500 h-2.5 rounded-full"
										style="width: 60%"
									></div>
								</div>
							</div>
						</div>
					</div>
					<div class="mt-8 text-center">
						<p class="text-gray-600">Home Page Content Placeholder</p>
					</div>
				</div>
			</div>

			<!-- Tasks Page Content (Extracted from tasks.html) -->
			<div id="page-tasks" class="page-content" style="display: none">
				<div class="content-area page-main-content">
					<!-- content-area from tasks.html -->
					<div
						class="app-header sticky top-0 bg-white z-10 py-4 px-5 shadow-sm"
					>
						<!-- Simplified app-header -->
						<h1 class="page-title text-2xl font-bold text-gray-800">
							任务管理
						</h1>
						<!-- page-title from tasks.html -->
					</div>
					<div class="tab-bar mt-4 mb-4">
						<!-- tab-bar from tasks.html -->
						<button class="tab-item active" data-tab="all">全部任务</button>
						<button class="tab-item" data-tab="todo">待处理</button>
						<button class="tab-item" data-tab="completed">已完成</button>
					</div>
					<div class="task-list-container">
						<!-- task-list from tasks.html -->
						<!-- Example Task Section -->
						<div class="task-section">
							<div class="section-header">
								<span>今日任务 (3)</span>
								<button
									class="text-sm text-blue-500 hover:text-blue-700 font-medium"
								>
									查看全部
								</button>
							</div>
							<!-- Example Task Card (structure from tasks.html) -->
							<div class="task-card today">
								<div class="task-content">
									<div class="task-header">
										<div class="task-checkbox"></div>
										<span class="task-title">完成项目A的需求文档 V2</span>
										<span class="task-priority priority-high ml-2">高</span>
									</div>
									<div class="text-xs text-gray-500 ml-9">
										关联目标: Q4 产品发布
									</div>
									<div class="text-xs text-red-500 ml-9 mt-1">
										截止日期: 今天 18:00
									</div>
								</div>
							</div>
							<div class="task-card today">
								<div class="task-content">
									<div class="task-header">
										<div class="task-checkbox checked"></div>
										<span
											class="task-title"
											style="text-decoration: line-through; color: grey"
											>准备周会演示稿</span
										>
									</div>
									<div class="text-xs text-gray-500 ml-9">
										关联KR: 提升团队沟通效率
									</div>
									<div class="text-xs text-gray-500 ml-9 mt-1">已完成</div>
								</div>
							</div>
						</div>
						<div class="mt-8 text-center">
							<p class="text-gray-600">Tasks Page Content Placeholder</p>
						</div>
					</div>
				</div>
			</div>

			<!-- Analytics Page Content (Extracted from analytics.html) -->
			<div id="page-analytics" class="page-content" style="display: none">
				<div class="content-area page-main-content">
					<!-- content-area from analytics.html -->
					<div
						class="app-header sticky top-0 bg-white z-10 py-4 px-5 shadow-sm"
					>
						<!-- Simplified app-header -->
						<h1 class="page-title text-2xl font-bold text-gray-800">
							数据分析
						</h1>
						<!-- page-title from analytics.html -->
					</div>
					<div class="segment-control my-4">
						<!-- segment-control from analytics.html -->
						<button class="segment-item active">本周</button>
						<button class="segment-item">本月</button>
						<button class="segment-item">本季度</button>
					</div>
					<!-- Example Section Card (structure from analytics.html) -->
					<div class="section-card">
						<div class="section-title">
							<i class="fas fa-tasks mr-2"></i>任务完成度
						</div>
						<p class="text-gray-600 text-sm mb-3">
							本周共完成 <strong>25</strong> 个任务，平均每日完成
							<strong>3.5</strong> 个。
						</p>
						<!-- Placeholder for a chart -->
						<div
							class="bg-gray-100 p-4 rounded-lg h-40 flex items-center justify-center"
						>
							<span class="text-gray-500">图表区域 (任务趋势)</span>
						</div>
					</div>
					<div class="section-card">
						<div class="section-title">
							<i class="fas fa-bullseye mr-2"></i>目标进度
						</div>
						<p class="text-gray-600 text-sm mb-3">
							当前季度目标平均进度为 <strong>68%</strong>。
						</p>
						<!-- Placeholder for another chart or stats -->
						<div
							class="bg-gray-100 p-4 rounded-lg h-40 flex items-center justify-center"
						>
							<span class="text-gray-500">图表区域 (目标分解)</span>
						</div>
					</div>
					<div class="mt-8 text-center">
						<p class="text-gray-600">Analytics Page Content Placeholder</p>
					</div>
				</div>
			</div>

			<!-- Settings Page Content (Extracted from settings.html) -->
			<div id="page-settings" class="page-content" style="display: none">
				<div class="content-area page-main-content">
					<!-- content-area from settings.html -->
					<!-- Profile Card Example (structure from settings.html) -->
					<div class="profile-card glass-card mt-5">
						<div class="profile-header">
							<div class="profile-avatar">
								<img
									src="https://via.placeholder.com/80"
									alt="User Avatar"
									class="avatar-image"
								/>
							</div>
							<div class="profile-info ml-4">
								<h2 class="profile-name">用户名称</h2>
								<p class="text-sm text-gray-600">保持专注，持续进步。</p>
							</div>
							<button
								class="ml-auto bg-blue-500 text-white px-3 py-1.5 rounded-lg text-sm font-medium hover:bg-blue-600"
							>
								编辑资料
							</button>
						</div>
					</div>
					<!-- Settings List Example -->
					<div class="glass-card mt-5">
						<ul class="divide-y divide-gray-200">
							<li
								class="p-4 flex justify-between items-center hover:bg-gray-50 cursor-pointer"
							>
								<div>
									<h3 class="text-md font-semibold text-gray-700">账户安全</h3>
									<p class="text-sm text-gray-500">修改密码，两步验证</p>
								</div>
								<i class="fas fa-chevron-right text-gray-400"></i>
							</li>
							<li
								class="p-4 flex justify-between items-center hover:bg-gray-50 cursor-pointer"
							>
								<div>
									<h3 class="text-md font-semibold text-gray-700">通知设置</h3>
									<p class="text-sm text-gray-500">管理应用通知偏好</p>
								</div>
								<i class="fas fa-chevron-right text-gray-400"></i>
							</li>
							<li
								class="p-4 flex justify-between items-center hover:bg-gray-50 cursor-pointer"
							>
								<div>
									<h3 class="text-md font-semibold text-gray-700">
										主题与外观
									</h3>
									<p class="text-sm text-gray-500">选择应用主题色</p>
								</div>
								<i class="fas fa-chevron-right text-gray-400"></i>
							</li>
							<li
								class="p-4 flex justify-between items-center hover:bg-gray-50 cursor-pointer text-red-500"
							>
								<div>
									<h3 class="text-md font-semibold">退出登录</h3>
								</div>
								<i class="fas fa-sign-out-alt"></i>
							</li>
						</ul>
					</div>
					<div class="mt-8 text-center">
						<p class="text-gray-600">Settings Page Content Placeholder</p>
					</div>
				</div>
			</div>
		</main>

		<!-- 底部导航栏 (adapted from today-tasks.html and shared-components.html) -->
		<nav class="main-nav">
			<a
				href="javascript:void(0);"
				data-page-id="page-home"
				class="main-nav-item"
			>
				<i class="fas fa-home"></i>
				<span class="text-xs mt-1">首页</span>
			</a>
			<a
				href="javascript:void(0);"
				data-page-id="page-today-tasks"
				class="main-nav-item active"
			>
				<!-- Default active page -->
				<i class="fas fa-calendar-day"></i>
				<span class="text-xs mt-1">今日</span>
			</a>
			<a
				href="javascript:void(0);"
				data-page-id="page-tasks"
				class="main-nav-item"
			>
				<i class="fas fa-tasks"></i>
				<span class="text-xs mt-1">任务</span>
			</a>
			<a
				href="javascript:void(0);"
				data-page-id="page-analytics"
				class="main-nav-item"
			>
				<i class="fas fa-chart-line"></i>
				<span class="text-xs mt-1">分析</span>
			</a>
			<a
				href="javascript:void(0);"
				data-page-id="page-settings"
				class="main-nav-item"
			>
				<i class="fas fa-user"></i>
				<span class="text-xs mt-1">我的</span>
			</a>
		</nav>

		<!-- 添加任务按钮 (from today-tasks.html) -->
		<a href="javascript:void(0)" class="add-task-btn" id="addTaskBtnGlobal">
			<!-- Changed ID to avoid conflict if any page had same -->
			<i class="fas fa-plus"></i>
		</a>

		<!-- 周任务概览弹窗 (from today-tasks.html, keep as is, should be globally accessible) -->
		<div class="week-overview-modal" id="weekOverviewModalGlobal">
			<!-- Changed ID for clarity -->
			<div class="week-overview-content">
				<h3 class="text-lg font-bold text-center">本周任务概览</h3>
				<p class="text-sm text-gray-600 text-center mt-1">
					2023年11月13日 - 11月19日
				</p>
				<div class="overview-stats">
					<div class="stat-item">
						<div class="stat-value">12</div>
						<div class="stat-label">总任务</div>
					</div>
					<div class="stat-item">
						<div class="stat-value">5</div>
						<div class="stat-label">已完成</div>
					</div>
					<div class="stat-item">
						<div class="stat-value">7</div>
						<div class="stat-label">待完成</div>
					</div>
				</div>
				<div class="mt-3">
					<h4 class="text-sm font-medium">按目标分类</h4>
					<div class="mt-2 space-y-2">
						<!-- Static example, dynamic data would be better -->
						<div class="flex items-center justify-between">
							<div class="flex items-center">
								<div class="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
								<span class="text-sm">提高团队协作效率</span>
							</div>
							<span class="text-sm">4 项</span>
						</div>
						<div class="flex items-center justify-between">
							<div class="flex items-center">
								<div class="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
								<span class="text-sm">优化产品用户体验</span>
							</div>
							<span class="text-sm">5 项</span>
						</div>
						<div class="flex items-center justify-between">
							<div class="flex items-center">
								<div class="w-3 h-3 rounded-full bg-orange-500 mr-2"></div>
								<span class="text-sm">拓展业务渠道</span>
							</div>
							<span class="text-sm">3 项</span>
						</div>
					</div>
				</div>
				<button class="close-btn" id="closeOverviewBtnGlobal">关闭</button>
				<!-- Changed ID -->
			</div>
		</div>

		<script>
			// --- Start of Consolidated & Adapted JavaScript ---

			// Helper function to switch pages
			function showPage(pageIdToShow) {
				const pages = document.querySelectorAll(".page-content");
				pages.forEach((page) => {
					page.style.display = "none"; // Hide all pages
					page.classList.remove("active-page");
				});

				const pageToShow = document.getElementById(pageIdToShow);
				if (pageToShow) {
					pageToShow.style.display = "block"; // Show the target page
					pageToShow.classList.add("active-page");

					// If a specific header exists for today-tasks, manage its visibility
					const todayHeader = document.getElementById("today-tasks-header");
					if (todayHeader) {
						todayHeader.style.display =
							pageIdToShow === "page-today-tasks" ? "block" : "none";
					}
				} else {
					console.warn("Page with ID " + pageIdToShow + " not found.");
				}

				// Update active state in navigation
				const navItems = document.querySelectorAll(".main-nav-item");
				navItems.forEach((item) => {
					item.classList.remove("active");
					if (item.dataset.pageId === pageIdToShow) {
						item.classList.add("active");
					}
				});
				// Update theme for active nav item
				applyThemeToActiveNavItem();
			}

			function applyThemeToActiveNavItem() {
				const currentPrimaryColor = getComputedStyle(document.documentElement)
					.getPropertyValue("--primary-color")
					.trim();
				const activeNavItem = document.querySelector(".main-nav-item.active");
				if (activeNavItem && currentPrimaryColor) {
					activeNavItem.style.color = currentPrimaryColor;
					const icon = activeNavItem.querySelector("i");
					if (icon) {
						icon.style.color = currentPrimaryColor;
					}
				}
			}

			// Theme Change Listener (from original today-tasks.html)
			window.addEventListener("message", function (event) {
				if (event.data && event.data.type === "THEME_CHANGE") {
					const theme = event.data.theme;
					document.documentElement.style.setProperty(
						"--primary-color",
						theme.primary
					);
					document.documentElement.style.setProperty(
						"--secondary-color",
						theme.secondary
					);
					document.documentElement.style.setProperty(
						"--accent-color",
						theme.accent
					);

					// Update elements based on the new theme's --primary-color
					const primaryColor = theme.primary;

					// Active calendar day
					const activeCalendarDay = document.querySelector(
						".calendar-day.active .w-8"
					);
					if (activeCalendarDay) {
						activeCalendarDay.style.backgroundColor = primaryColor;
					}

					// Active navigation item (already handled by applyThemeToActiveNavItem, but can be called here too)
					applyThemeToActiveNavItem();

					// Add Task Button
					const addTaskBtn = document.getElementById("addTaskBtnGlobal");
					if (addTaskBtn) {
						addTaskBtn.style.backgroundColor = primaryColor;
					}

					// Week Overview Button
					const weekOverviewBtn = document.getElementById("weekOverviewBtn"); // Assumes this is on today-tasks page
					if (weekOverviewBtn) {
						weekOverviewBtn.style.backgroundColor = primaryColor;
					}

					// Stat values in overview modal
					document
						.querySelectorAll("#weekOverviewModalGlobal .stat-value")
						.forEach((item) => {
							item.style.color = primaryColor;
						});

					// Potentially other theme-sensitive elements across all integrated pages would go here
					// For example, if other pages have elements that should use --primary-color directly:
					// document.querySelectorAll('.some-class-on-other-pages').forEach(el => el.style.color = primaryColor);
				}
			});

			// DOMContentLoaded - Main event listener setup
			document.addEventListener("DOMContentLoaded", function () {
				// Initial page display (show 'Today Tasks' by default)
				showPage("page-today-tasks");

				// Navigation click handlers
				const navItems = document.querySelectorAll(".main-nav-item");
				navItems.forEach((item) => {
					item.addEventListener("click", function (e) {
						e.preventDefault();
						const pageId = this.dataset.pageId;
						if (pageId) {
							showPage(pageId);
						}
					});
				});

				// --- Event listeners specific to #page-today-tasks ---
				const todayTasksPage = document.getElementById("page-today-tasks");
				if (todayTasksPage) {
					const prevWeekBtn = todayTasksPage.querySelector("#prev-week");
					const nextWeekBtn = todayTasksPage.querySelector("#next-week");
					if (prevWeekBtn)
						prevWeekBtn.addEventListener("click", () => alert("切换到上一周"));
					if (nextWeekBtn)
						nextWeekBtn.addEventListener("click", () => alert("切换到下一周"));

					const calendarDays = todayTasksPage.querySelectorAll(".calendar-day");
					calendarDays.forEach((day) => {
						day.addEventListener("click", function () {
							calendarDays.forEach((d) => {
								d.classList.remove("active");
								const dateCircle = d.querySelector(".w-8");
								if (dateCircle) {
									dateCircle.classList.remove("bg-blue-500", "text-white"); // Tailwind classes
									dateCircle.style.backgroundColor = ""; // Reset inline style
									dateCircle.style.color = ""; // Reset inline style
								}
							});
							this.classList.add("active");
							const dateCircle = this.querySelector(".w-8");
							if (dateCircle) {
								// Apply theme color or default if theme not set
								const primaryColor = getComputedStyle(document.documentElement)
									.getPropertyValue("--primary-color")
									.trim();
								dateCircle.style.backgroundColor = primaryColor || "#2196F3"; // Fallback
								dateCircle.style.color = "white";
							}
							const date = this.querySelector(".w-8").textContent;
							const taskStatusEl = todayTasksPage.querySelector(
								".mt-2 .text-xs .fa-check-circle"
							);
							if (taskStatusEl) {
								taskStatusEl.parentNode.innerHTML = `<i class="fas fa-check-circle mr-1 text-green-500"></i>${date}日任务：加载中...`;
								setTimeout(() => {
									const count = Math.floor(Math.random() * 8) + 1;
									taskStatusEl.parentNode.innerHTML = `<i class="fas fa-check-circle mr-1 text-green-500"></i>${date}日任务：${count} 项待办`;
								}, 500);
							}
						});
					});

					const taskCheckboxes = todayTasksPage.querySelectorAll(
						'.task-item input[type="checkbox"]'
					);
					taskCheckboxes.forEach((checkbox) => {
						checkbox.addEventListener("change", function () {
							const taskText = this.nextElementSibling;
							if (this.checked) {
								taskText.style.textDecoration = "line-through";
								taskText.style.color = "#9CA3AF";
							} else {
								taskText.style.textDecoration = "none";
								taskText.style.color = "";
							}
						});
					});
					// Week Overview Modal on Today page
					const weekOverviewBtn =
						todayTasksPage.querySelector("#weekOverviewBtn");
					const weekOverviewModal = document.getElementById(
						"weekOverviewModalGlobal"
					); // Use global ID
					const closeOverviewBtn = document.getElementById(
						"closeOverviewBtnGlobal"
					); // Use global ID

					if (weekOverviewBtn) {
						weekOverviewBtn.addEventListener("click", () => {
							if (weekOverviewModal) weekOverviewModal.style.display = "flex";
						});
					}
					if (closeOverviewBtn) {
						closeOverviewBtn.addEventListener("click", () => {
							if (weekOverviewModal) weekOverviewModal.style.display = "none";
						});
					}
					if (weekOverviewModal) {
						weekOverviewModal.addEventListener("click", function (event) {
							if (event.target === weekOverviewModal) {
								weekOverviewModal.style.display = "none";
							}
						});
					}
				}
				// --- End of #page-today-tasks specific listeners ---

				// Global Add Task Button
				const addTaskBtnGlobal = document.getElementById("addTaskBtnGlobal");
				if (addTaskBtnGlobal) {
					addTaskBtnGlobal.addEventListener("click", () =>
						alert("即将添加新任务 (全局按钮)")
					);
				}
				// Apply initial theme to active nav item after DOM is loaded and default page is shown
				applyThemeToActiveNavItem();
			});

			// --- End of Consolidated & Adapted JavaScript ---
		</script>
	</body>
</html>
