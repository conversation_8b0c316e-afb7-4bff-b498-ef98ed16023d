import dayjs from 'dayjs'
import weekOfYear from 'dayjs/plugin/weekOfYear'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'
import isoWeek from 'dayjs/plugin/isoWeek'
import updateLocale from 'dayjs/plugin/updateLocale'
import 'dayjs/locale/zh-cn'

dayjs.extend(weekOfYear)
dayjs.extend(isSameOrBefore)
dayjs.extend(isSameOrAfter)
dayjs.extend(isoWeek)
dayjs.extend(updateLocale)

// 使用中文语言包
dayjs.locale('zh-cn')

// 设置周一为一周的起始日
dayjs.updateLocale('zh-cn', {
  weekStart: 1,
})

export type RuleType =
  | 'DAILY' // 每天
  | 'WEEKLY' // 每周几
  | 'MONTHLY' // 每月几号
  | 'INTERVAL_DAILY' // 每隔几天
  | 'WEEKLY_N_TIMES' // 每周几天
  | 'MONTHLY_N_TIMES' // 每月几天
  | 'N_DAYS' // 每几天
  | 'N_WEEKS' // 每几个周
  | 'N_MONTHS' // 每几个月

// 星期缩写类型定义
export type WeekdayCode = 'MO' | 'TU' | 'WE' | 'TH' | 'FR' | 'SA' | 'SU'

// 星期缩写与数字之间的映射
export const weekdayToNumber: Record<WeekdayCode, number> = {
  SU: 0, // 周日
  MO: 1, // 周一
  TU: 2, // 周二
  WE: 3, // 周三
  TH: 4, // 周四
  FR: 5, // 周五
  SA: 6, // 周六
}

// 星期缩写与周几的中文名称映射
export const weekdayToChinese: Record<WeekdayCode, string> = {
  SU: '周日',
  MO: '周一',
  TU: '周二',
  WE: '周三',
  TH: '周四',
  FR: '周五',
  SA: '周六',
}

export const numberToWeekday: Record<number, WeekdayCode> = {
  0: 'SU', // 周日
  1: 'MO', // 周一
  2: 'TU', // 周二
  3: 'WE', // 周三
  4: 'TH', // 周四
  5: 'FR', // 周五
  6: 'SA', // 周六
}

// 循环规则接口定义
export interface Rule {
  type: RuleType
  byweekday?: WeekdayCode[] // 指定星期几 ['MO', 'TU', 'WE', 'TH', 'FR', 'SA', 'SU']
  bymonthday?: number[] // 指定日期 [1-31]
  interval?: number // 间隔天数/周数/月数
  count?: number // 每周/月需要完成的天数
  startDate?: string // 开始日期 YYYY-MM-DD
}

/**
 * 将字符串格式的规则解析为对象
 * @param rule 规则字符串或对象
 * @returns 规则对象
 */
export const parseRule = (rule: string | Rule): Rule => {
  if (typeof rule !== 'string') {
    return rule
  }

  const result: Rule = { type: 'DAILY' } // 默认类型

  // 解析字符串形式的规则，如：type=DAILY;count=1
  const parts = rule.split(';')
  for (const part of parts) {
    const [key, value] = part.split('=')
    if (!key || !value) continue

    switch (key.trim()) {
      case 'type':
        result.type = value.trim() as RuleType
        break
      case 'byweekday':
        // 处理如 byweekday=MO,TU,WE 的格式
        result.byweekday = value.split(',').map((v) => {
          const trimmed = v.trim()
          // 如果是数字字符串，则转换为星期缩写
          if (/^\d+$/.test(trimmed)) {
            const num = parseInt(trimmed, 10)
            return numberToWeekday[num] || 'MO'
          }
          return trimmed as WeekdayCode
        })
        break
      case 'bymonthday':
        // 处理如 bymonthday=1,15,30 的格式
        result.bymonthday = value.split(',').map((v) => parseInt(v.trim(), 10))
        break
      case 'interval':
        result.interval = parseInt(value.trim(), 10)
        break
      case 'count':
        result.count = parseInt(value.trim(), 10)
        break
      case 'startDate':
        result.startDate = value.trim()
        break
      default:
        // 处理其他可能的键值
        ;(result as any)[key.trim()] = value.trim()
        break
    }
  }

  return result
}

// 任务进度数据接口
export interface TaskProgressData {
  totalOccurrences: number // 总发生次数
  completedOccurrences: number // 已完成次数
  progress: number // 进度百分比
  isOccurrenceToday: boolean // 今天是否为任务发生日
  completedToday: number // 今天已完成的数量
  isCompletedToday: boolean // 今天是否已完成任务
  // 以下字段根据循环类型可能存在
  completedDays?: number // 本周期已完成的天数
  totalDays?: number // 本周期总共要完成的天数
  completedCount?: number // 本周期已经完成的次数
  totalCount?: number // 本周期需要完成的总次数
  dayOfPeriod?: number // 当前是本周期的第几天
  weekOfPeriod?: number // 当前处于周期的第几周
  monthOfPeriod?: number // 当前处于周期的第几月
  totalWeeks?: number // 本周期的总周数
  totalMonths?: number // 本周期的总月数
}

// 新增返回类型接口定义
export interface OccurrencesResult {
  dates: string[] // 符合规则的日期数组
  count: number // 根据规则计算的总次数
  groupedDates: string[][] // 按周期分组的日期数组
}

/**
 * 获取指定日期范围内的所有重复日期
 * @param rule 循环规则或规则字符串
 * @param startDate 开始日期（YYYY-MM-DD 格式）
 * @param endDate 结束日期（YYYY-MM-DD 格式）
 * @returns 符合规则的日期和计数结果
 */
export const getOccurrences = (rule: string | Rule, startDate: string, endDate: string): OccurrencesResult => {
  const ruleObj = parseRule(rule)
  const dates: string[] = []
  let groupedDates: string[][] = []
  let count = 0

  const start = dayjs(startDate)
  const end = dayjs(endDate)

  // 如果结束日期早于开始日期，返回空结果
  if (end.isBefore(start)) {
    return { dates: [], count: 0, groupedDates: [] }
  }

  switch (ruleObj.type) {
    case 'DAILY': {
      // 每天类型：从起始日期到结束日期的每一天
      const diffDays = end.diff(start, 'day') + 1 // 包含起始和结束日期
      let current = start.clone()

      for (let i = 0; i < diffDays; i++) {
        dates.push(current.format('YYYY-MM-DD'))
        current = current.add(1, 'day')
      }
      groupedDates = dates.map((date) => [date]) // 每天都是一个独立的周期

      count = diffDays
      break
    }

    case 'WEEKLY': {
      // 每周几类型：只包含指定的星期几
      if (!ruleObj.byweekday || ruleObj.byweekday.length === 0) {
        return { dates: [], count: 0, groupedDates: [] }
      }

      const weekdayCodes = ruleObj.byweekday.map((code) => weekdayToNumber[code])
      let current = start.clone()

      while (current.isSameOrBefore(end)) {
        const currentWeekday = current.day()

        if (weekdayCodes.includes(currentWeekday)) {
          dates.push(current.format('YYYY-MM-DD'))
          count++
        }

        current = current.add(1, 'day')
      }

      // 按周分组
      const groupedByWeek: { [week: string]: string[] } = {}
      dates.forEach((date) => {
        const weekKey = dayjs(date).startOf('isoWeek').format('YYYY-MM-DD')
        if (!groupedByWeek[weekKey]) {
          groupedByWeek[weekKey] = []
        }
        groupedByWeek[weekKey].push(date)
      })
      groupedDates = Object.values(groupedByWeek)
      break
    }

    case 'MONTHLY': {
      // 每月几号类型：只包含指定日期
      if (!ruleObj.bymonthday || ruleObj.bymonthday.length === 0) {
        return { dates: [], count: 0, groupedDates: [] }
      }

      let current = start.clone()

      while (current.isSameOrBefore(end)) {
        const currentDay = current.date()

        if (ruleObj.bymonthday.includes(currentDay)) {
          dates.push(current.format('YYYY-MM-DD'))
          count++
        }

        current = current.add(1, 'day')
      }

      // 按月分组
      const groupedByMonth: { [month: string]: string[] } = {}
      dates.forEach((date) => {
        const monthKey = dayjs(date).startOf('month').format('YYYY-MM')
        if (!groupedByMonth[monthKey]) {
          groupedByMonth[monthKey] = []
        }
        groupedByMonth[monthKey].push(date)
      })
      groupedDates = Object.values(groupedByMonth)
      break
    }

    case 'INTERVAL_DAILY': {
      // 每隔几天类型
      if (!ruleObj.interval || ruleObj.interval < 1 || !ruleObj.startDate) {
        return { dates: [], count: 0, groupedDates: [] }
      }

      // 确定计算起点（规则开始日期或函数参数开始日期，取较晚者）
      const ruleStart = dayjs(ruleObj.startDate)
      const calcStart = ruleStart.isAfter(start) ? ruleStart : start

      // 计算从规则开始日期到计算起点的天数差
      const daysSinceRuleStart = calcStart.diff(ruleStart, 'day')

      // 计算第一个匹配日期
      let offset = daysSinceRuleStart % ruleObj.interval
      offset = offset === 0 ? 0 : ruleObj.interval - offset
      let current = calcStart.add(offset, 'day')

      // 从第一个匹配日期开始，每隔指定天数生成一个日期
      while (current.isSameOrBefore(end)) {
        if (current.isSameOrAfter(start)) {
          dates.push(current.format('YYYY-MM-DD'))
          count++
        }
        current = current.add(ruleObj.interval, 'day')
      }
      groupedDates = dates.map((date) => [date]) // 每个发生的日期都是一个独立的周期
      break
    }

    case 'WEEKLY_N_TIMES': {
      // 每周几天类型：周期内的每一天都添加，计算 count
      if (!ruleObj.count || ruleObj.count < 1) {
        return { dates: [], count: 0, groupedDates: [] }
      }

      // 将周期内的每一天都添加到日期数组中
      let current = start.clone()
      while (current.isSameOrBefore(end)) {
        dates.push(current.format('YYYY-MM-DD'))
        current = current.add(1, 'day')
      }

      // 按周分组
      const groupedByWeek: { [week: string]: string[] } = {}
      dates.forEach((date) => {
        const weekKey = dayjs(date).startOf('isoWeek').format('YYYY-MM-DD')
        if (!groupedByWeek[weekKey]) {
          groupedByWeek[weekKey] = []
        }
        groupedByWeek[weekKey].push(date)
      })
      groupedDates = Object.values(groupedByWeek)

      // 计算起始日期所在周的开始和结束（使用 isoWeek）
      const firstWeekStart = start.startOf('isoWeek')
      const firstWeekEnd = start.endOf('isoWeek')

      // 计算结束日期所在周的开始和结束（使用 isoWeek）
      const lastWeekStart = end.startOf('isoWeek')
      const lastWeekEnd = end.endOf('isoWeek')

      // 计算总周数
      const totalWeeks = lastWeekStart.diff(firstWeekStart, 'week') + 1

      // 计算第一周可用的天数（调整计算逻辑以适应 isoWeek）
      const firstWeekAvailableDays = Math.min(
        7 - (start.isoWeekday() - 1), // 从开始日期到周末的天数
        end.diff(start, 'day') + 1 // 确保不超过总范围
      )

      // 计算最后一周可用的天数（如果与第一周不同）
      const lastWeekAvailableDays =
        totalWeeks > 1
          ? Math.min(end.isoWeekday(), 7) // 从周开始到结束日期的天数
          : firstWeekAvailableDays

      // 计算 count
      count = 0

      // 完整的周
      const fullWeeks = Math.max(0, totalWeeks - (totalWeeks > 1 ? 2 : 0))
      count += fullWeeks * ruleObj.count

      // 第一周
      if (totalWeeks > 1) {
        count += Math.min(firstWeekAvailableDays, ruleObj.count)
      }

      // 最后一周（如果不是完整周且与第一周不同）
      if (totalWeeks > 1) {
        count += Math.min(lastWeekAvailableDays, ruleObj.count)
      } else if (totalWeeks === 1) {
        // 当只有一个周时，单独计算
        count = Math.min(firstWeekAvailableDays, ruleObj.count)
      }
      break
    }

    case 'MONTHLY_N_TIMES': {
      // 每月几天类型：周期内的每一天都添加，计算 count
      if (!ruleObj.count || ruleObj.count < 1) {
        return { dates: [], count: 0, groupedDates: [] }
      }

      // 将周期内的每一天都添加到日期数组中
      let current = start.clone()
      while (current.isSameOrBefore(end)) {
        dates.push(current.format('YYYY-MM-DD'))
        current = current.add(1, 'day')
      }

      // 按月分组
      const groupedByMonth: { [month: string]: string[] } = {}
      dates.forEach((date) => {
        const monthKey = dayjs(date).startOf('month').format('YYYY-MM')
        if (!groupedByMonth[monthKey]) {
          groupedByMonth[monthKey] = []
        }
        groupedByMonth[monthKey].push(date)
      })
      groupedDates = Object.values(groupedByMonth)

      // 计算起始日期所在月的开始和结束
      const firstMonthStart = start.startOf('month')
      const firstMonthEnd = start.endOf('month')

      // 计算结束日期所在月的开始和结束
      const lastMonthStart = end.startOf('month')
      const lastMonthEnd = end.endOf('month')

      // 计算总月数
      const totalMonths = lastMonthStart.diff(firstMonthStart, 'month') + 1

      // 计算第一个月可用的天数
      const firstMonthAvailableDays = Math.min(
        firstMonthEnd.date() - start.date() + 1, // 从开始日期到月底的天数
        end.diff(start, 'day') + 1 // 确保不超过总范围
      )

      // 计算最后一个月可用的天数（如果与第一个月不同）
      const lastMonthAvailableDays =
        totalMonths > 1
          ? Math.min(end.date(), end.daysInMonth()) // 从月开始到结束日期的天数
          : firstMonthAvailableDays

      // 计算 count
      count = 0

      // 完整的月
      const fullMonths = Math.max(0, totalMonths - (totalMonths > 1 ? 2 : 0))
      count += fullMonths * ruleObj.count

      // 第一个月
      if (totalMonths > 1) {
        count += Math.min(firstMonthAvailableDays, ruleObj.count)
      }

      // 最后一个月（如果与第一个月不同）
      if (totalMonths > 1) {
        count += Math.min(lastMonthAvailableDays, ruleObj.count)
      } else if (totalMonths === 1) {
        count = Math.min(firstMonthAvailableDays, ruleObj.count)
      }
      break
    }

    case 'N_DAYS': {
      // 每几天类型：周期内的每一天都添加，计算 count
      if (!ruleObj.interval || ruleObj.interval < 1) {
        return { dates: [], count: 0, groupedDates: [] }
      }

      // 将周期内的每一天都添加到日期数组中
      let current = start.clone()
      while (current.isSameOrBefore(end)) {
        dates.push(current.format('YYYY-MM-DD'))
        current = current.add(1, 'day')
      }

      // 按 N 天分组
      if (ruleObj.interval) {
        for (let i = 0; i < dates.length; i += ruleObj.interval) {
          groupedDates.push(dates.slice(i, i + ruleObj.interval))
        }
      }

      // 计算总天数
      const totalDays = end.diff(start, 'day') + 1

      // 计算周期数
      count = Math.ceil(totalDays / ruleObj.interval)
      break
    }

    case 'N_WEEKS': {
      // 每几个周类型：周期内的每一天都添加，计算count
      if (!ruleObj.interval || ruleObj.interval < 1) {
        return { dates: [], count: 0, groupedDates: [] }
      }

      // 将周期内的每一天都添加到日期数组中
      let current = start.clone()
      while (current.isSameOrBefore(end)) {
        dates.push(current.format('YYYY-MM-DD'))
        current = current.add(1, 'day')
      }

      // 按 N 周分组
      if (ruleObj.interval) {
        const periodStart = start.startOf('isoWeek')
        let currentPeriodStart = periodStart
        while (currentPeriodStart.isSameOrBefore(end)) {
          const currentPeriodEnd = currentPeriodStart.add(ruleObj.interval, 'week').subtract(1, 'day')
          const group = dates.filter((date) => {
            const d = dayjs(date)
            return d.isSameOrAfter(currentPeriodStart, 'day') && d.isSameOrBefore(currentPeriodEnd, 'day')
          })
          if (group.length > 0) {
            groupedDates.push(group)
          }
          currentPeriodStart = currentPeriodStart.add(ruleObj.interval, 'week')
        }
      }

      // 确保使用 ISO 周标准（周一为一周的第一天）
      const startIso = start.startOf('isoWeek')
      const endIso = end.startOf('isoWeek')

      // 从起始日期到结束日期的总周数（使用 ISO 周标准）
      const totalWeeks = Math.ceil(endIso.diff(startIso, 'week', true))

      // 计算周期数
      count = Math.ceil(totalWeeks / ruleObj.interval)
      break
    }

    case 'N_MONTHS': {
      // 每几个月类型：周期内的每一天都添加，计算 count
      if (!ruleObj.interval || ruleObj.interval < 1) {
        return { dates: [], count: 0, groupedDates: [] }
      }

      // 将周期内的每一天都添加到日期数组中
      let current = start.clone()
      while (current.isSameOrBefore(end)) {
        dates.push(current.format('YYYY-MM-DD'))
        current = current.add(1, 'day')
      }

      // 按 N 个月分组
      if (ruleObj.interval) {
        const periodStart = start.startOf('month')
        let currentPeriodStart = periodStart
        while (currentPeriodStart.isSameOrBefore(end)) {
          const currentPeriodEnd = currentPeriodStart.add(ruleObj.interval, 'month').subtract(1, 'day')
          const group = dates.filter((date) => {
            const d = dayjs(date)
            return d.isSameOrAfter(currentPeriodStart, 'day') && d.isSameOrBefore(currentPeriodEnd, 'day')
          })
          if (group.length > 0) {
            groupedDates.push(group)
          }
          currentPeriodStart = currentPeriodStart.add(ruleObj.interval, 'month')
        }
      }

      // 计算总月数
      const totalMonths =
        (end.year() - start.year()) * 12 + end.month() - start.month() + (end.date() >= start.date() ? 1 : 0) // 如果结束日期的日比开始日期的日大或相等，则加 1

      // 计算周期数
      count = Math.ceil(totalMonths / ruleObj.interval)
      break
    }

    default:
      console.error('getOccurrences 函数错误，未匹配到规则类型', ruleObj.type)
  }

  return { dates, count, groupedDates }
}

/**
 * 获取指定日期、指定任务的进度数据
 * @param date 当前日期（YYYY-MM-DD 格式）
 * @param rule 循环规则或规则字符串
 * @param progressRecords 进度记录数组
 * @param options 可选配置项
 * @returns 任务进度数据
 */
export const getTaskProgressData = (
  date: string,
  rule: string | Rule,
  progressRecords: DB.RecList[],
  options: {
    startDate: string // 开始日期（YYYY-MM-DD 格式）
    endDate: string // 结束日期（YYYY-MM-DD 格式）
    totalRequired?: number // 需要完成的总数
  }
): TaskProgressData => {
  const ruleObj = parseRule(rule)
  const today = dayjs(date).format('YYYY-MM-DD')
  const totalRequired = options?.totalRequired || 1
  // 优先使用传入的开始日期和结束日期
  const effectiveStartDate = options.startDate || ruleObj.startDate
  const effectiveEndDate = options.endDate

  // 获取今天的完成记录
  const todayRecords = progressRecords.filter((record) => dayjs(record.recTime).format('YYYY-MM-DD') === today)
  const completedToday = todayRecords.reduce((sum, record) => sum + (record.val || 0), 0)

  // 根据不同的循环类型进行处理
  switch (ruleObj.type) {
    case 'DAILY': {
      // 每天类型
      return {
        totalOccurrences: 1,
        completedOccurrences: completedToday >= totalRequired ? 1 : 0,
        progress: completedToday >= totalRequired ? 100 : (completedToday / totalRequired) * 100,
        isOccurrenceToday: true,
        completedToday,
        isCompletedToday: completedToday >= totalRequired,
      }
    }

    case 'WEEKLY': {
      // 每周几类型
      const todayObj = dayjs(date)
      const todayWeekday = numberToWeekday[todayObj.day()]
      const isOccurrenceToday = ruleObj.byweekday?.includes(todayWeekday) || false

      return {
        totalOccurrences: 1,
        completedOccurrences: completedToday >= totalRequired ? 1 : 0,
        progress: completedToday >= totalRequired ? 100 : (completedToday / totalRequired) * 100,
        isOccurrenceToday,
        completedToday,
        isCompletedToday: completedToday >= totalRequired,
      }
    }

    case 'MONTHLY': {
      // 每月几号类型
      const todayObj = dayjs(date)
      const todayDay = todayObj.date()
      const isOccurrenceToday = ruleObj.bymonthday?.includes(todayDay) || false

      return {
        totalOccurrences: 1,
        completedOccurrences: completedToday >= totalRequired ? 1 : 0,
        progress: completedToday >= totalRequired ? 100 : (completedToday / totalRequired) * 100,
        isOccurrenceToday,
        completedToday,
        isCompletedToday: completedToday >= totalRequired,
      }
    }

    case 'INTERVAL_DAILY': {
      // 每隔几天类型
      if (!ruleObj.interval || !effectiveStartDate) {
        return {
          totalOccurrences: 0,
          completedOccurrences: 0,
          progress: 0,
          isOccurrenceToday: false,
          completedToday: 0,
          isCompletedToday: false,
        }
      }

      const startDate = dayjs(effectiveStartDate)
      const todayObj = dayjs(date)
      const diffDays = todayObj.diff(startDate, 'day')
      const isOccurrenceToday = diffDays >= 0 && diffDays % ruleObj.interval === 0

      return {
        totalOccurrences: 1,
        completedOccurrences: completedToday >= totalRequired ? 1 : 0,
        progress: completedToday >= totalRequired ? 100 : (completedToday / totalRequired) * 100,
        isOccurrenceToday,
        completedToday: isOccurrenceToday ? completedToday : 0,
        isCompletedToday: completedToday >= totalRequired,
      }
    }

    case 'WEEKLY_N_TIMES': {
      // 每周几天类型
      if (!ruleObj.count) {
        return {
          totalOccurrences: 0,
          completedOccurrences: 0,
          progress: 0,
          isOccurrenceToday: false,
          completedToday: 0,
          isCompletedToday: false,
        }
      }

      const todayObj = dayjs(date)
      // 计算本周的起始日期和结束日期
      const weekStart = todayObj.startOf('isoWeek') // 使用 isoWeek 确保周一为起始日
      const weekEnd = todayObj.endOf('isoWeek') // 使用 isoWeek 确保周日为结束日
      console.log('[WEEKLY_N_TIMES] 今天日期：', todayObj.format('YYYY-MM-DD'), '星期几：', todayObj.day())

      // 计算规则开始日期是否在本周内
      let totalDays = ruleObj.count
      console.log('[WEEKLY_N_TIMES] 初始 totalDays:', totalDays)
      console.log(
        '[WEEKLY_N_TIMES] 当前日期：',
        date,
        '周开始：',
        weekStart.format('YYYY-MM-DD'),
        '周结束：',
        weekEnd.format('YYYY-MM-DD')
      )

      if (effectiveStartDate) {
        const startDate = dayjs(effectiveStartDate)
        console.log('[WEEKLY_N_TIMES] 规则开始日期：', startDate.format('YYYY-MM-DD'), '星期几：', startDate.day())

        const isAfterWeekStart = startDate.isAfter(weekStart)
        const isBeforeWeekEnd = startDate.isBefore(weekEnd)
        console.log(
          '[WEEKLY_N_TIMES] 开始日期是否在本周内：',
          isAfterWeekStart && isBeforeWeekEnd,
          '(isAfterWeekStart:',
          isAfterWeekStart,
          ', isBeforeWeekEnd:',
          isBeforeWeekEnd,
          ')'
        )

        if (isAfterWeekStart && isBeforeWeekEnd) {
          // 如果开始日期在本周内，则计算从开始日期到周末的天数
          const startDay = startDate.day()
          const adjustedStartDay = startDate.day() === 0 ? 7 : startDate.day()
          const daysLeftInWeek = 7 - adjustedStartDay + 1 // 调整计算逻辑以适应 isoWeek
          console.log('[WEEKLY_N_TIMES] 开始日期星期几 (原始):', startDay, '调整后：', adjustedStartDay)
          console.log('[WEEKLY_N_TIMES] 本周剩余天数计算：7 -', adjustedStartDay, '+ 1 =', daysLeftInWeek)

          const oldTotalDays = totalDays
          totalDays = Math.min(ruleObj.count, daysLeftInWeek)
          console.log(
            '[WEEKLY_N_TIMES] 调整后的 totalDays: Math.min(',
            ruleObj.count,
            ',',
            daysLeftInWeek,
            ') =',
            totalDays,
            '(原值：',
            oldTotalDays,
            ')'
          )
        }
      }

      // 筛选出本周的记录
      const weekRecords = progressRecords.filter((record) => {
        const recordDate = dayjs(record.recTime)
        // 修改为包含周开始和结束日期
        const isInWeek = recordDate.isSameOrAfter(weekStart) && recordDate.isSameOrBefore(weekEnd)
        return isInWeek
      })
      console.log(
        '[WEEKLY_N_TIMES] 本周记录数量：',
        weekRecords.length,
        '周开始：',
        weekStart.format('YYYY-MM-DD'),
        '周结束：',
        weekEnd.format('YYYY-MM-DD')
      )

      // 按日期分组统计记录
      const dailyRecords = new Map<string, number>()
      weekRecords.forEach((record) => {
        const date = record.recTime
        dailyRecords.set(date, (dailyRecords.get(date) || 0) + (record.val || 0))
      })

      // 计算已完成的天数
      const completedDays = Array.from(dailyRecords.values()).filter((count) => count >= totalRequired).length

      console.log(
        '[WEEKLY_N_TIMES] 每日记录：',
        Array.from(dailyRecords.entries()).map(([date, count]) => `${date}: ${count}`)
      )
      console.log(
        '[WEEKLY_N_TIMES] 已完成天数：',
        completedDays,
        '目标完成天数：',
        totalDays,
        '每日目标值：',
        totalRequired
      )

      // 判断今天是否应该显示
      let isOccurrenceToday = completedDays < totalDays
      if (completedToday > 0) {
        isOccurrenceToday = true
      }
      console.log(
        '[WEEKLY_N_TIMES] 今天是否显示：',
        isOccurrenceToday,
        '(完成天数 < 目标天数：',
        completedDays < totalDays,
        ', 今天完成数:',
        completedToday,
        ')'
      )
      return {
        totalOccurrences: totalDays,
        completedOccurrences: completedDays,
        progress: totalDays > 0 ? (completedDays / totalDays) * 100 : 0,
        isOccurrenceToday,
        completedToday,
        completedDays,
        totalDays,
        isCompletedToday: completedToday >= totalRequired,
      }
    }

    case 'MONTHLY_N_TIMES': {
      // 每月几天类型
      if (!ruleObj.count) {
        return {
          totalOccurrences: 0,
          completedOccurrences: 0,
          progress: 0,
          isOccurrenceToday: false,
          completedToday: 0,
          isCompletedToday: false,
        }
      }

      const todayObj = dayjs(date)
      // 计算本月的起始日期和结束日期
      const monthStart = todayObj.startOf('month')
      const monthEnd = todayObj.endOf('month')

      // 计算规则开始日期是否在本月内
      let totalDays = ruleObj.count
      if (effectiveStartDate) {
        const startDate = dayjs(effectiveStartDate)
        if (startDate.isAfter(monthStart) && startDate.isBefore(monthEnd)) {
          // 如果开始日期在本月内，则计算从开始日期到月底的天数
          const daysLeftInMonth = monthEnd.date() - startDate.date() + 1
          totalDays = Math.min(ruleObj.count, daysLeftInMonth)
        }
      }

      // 筛选出本月的记录
      const monthRecords = progressRecords.filter((record) => {
        const recordDate = dayjs(record.recTime)
        return recordDate.isAfter(monthStart) && recordDate.isBefore(monthEnd)
      })

      // 按日期分组统计记录
      const dailyRecords = new Map<string, number>()
      monthRecords.forEach((record) => {
        const date = record.recTime
        dailyRecords.set(date, (dailyRecords.get(date) || 0) + (record.val || 0))
      })

      // 计算已完成的天数
      const completedDays = Array.from(dailyRecords.values()).filter((count) => count >= totalRequired).length

      // 判断今天是否应该显示
      let isOccurrenceToday = completedDays < totalDays
      if (completedToday > 0) {
        isOccurrenceToday = true
      }

      return {
        totalOccurrences: totalDays,
        completedOccurrences: completedDays,
        progress: totalDays > 0 ? (completedDays / totalDays) * 100 : 0,
        isOccurrenceToday,
        completedToday,
        completedDays,
        totalDays,
        isCompletedToday: completedToday >= totalRequired,
      }
    }

    case 'N_DAYS': {
      // 每几天类型
      if (!ruleObj.interval || !effectiveStartDate) {
        return {
          totalOccurrences: 0,
          completedOccurrences: 0,
          progress: 0,
          isOccurrenceToday: false,
          completedToday: 0,
          isCompletedToday: false,
        }
      }

      const startDate = dayjs(effectiveStartDate)
      const todayObj = dayjs(date)
      const diffDays = todayObj.diff(startDate, 'day')

      // 计算当前是第几个周期
      const periodNumber = Math.floor(diffDays / ruleObj.interval)

      // 计算当前周期的起始日期和结束日期
      const periodStart = startDate.add(periodNumber * ruleObj.interval, 'day')
      const periodEnd = periodStart.add(ruleObj.interval - 1, 'day')

      // 计算当前是周期内的第几天
      const dayOfPeriod = todayObj.diff(periodStart, 'day') + 1

      // 筛选出本周期内的记录
      const periodRecords = progressRecords.filter((record) => {
        const recordDate = dayjs(record.recTime).startOf('day')
        const periodStartDay = periodStart.startOf('day')
        const periodEndDay = periodEnd.startOf('day')
        return recordDate.isSameOrAfter(periodStartDay) && recordDate.isSameOrBefore(periodEndDay)
      })

      // 计算已完成的总次数
      const completedCount = periodRecords.reduce((sum, record) => sum + (record.val || 0), 0)
      // 判断今天是否应该显示
      let isOccurrenceToday = completedCount < totalRequired
      if (completedToday > 0) {
        isOccurrenceToday = true
      }

      return {
        totalOccurrences: 1,
        completedOccurrences: completedCount >= totalRequired ? 1 : 0,
        progress: (completedCount / totalRequired) * 100,
        isOccurrenceToday,
        completedToday,
        completedCount,
        totalCount: totalRequired,
        dayOfPeriod,
        totalDays: ruleObj.interval,
        isCompletedToday: completedCount >= totalRequired,
      }
    }

    case 'N_WEEKS': {
      // 每几个周类型
      if (!ruleObj.interval || !effectiveStartDate) {
        return {
          totalOccurrences: 0,
          completedOccurrences: 0,
          progress: 0,
          isOccurrenceToday: false,
          completedToday: 0,
          isCompletedToday: false,
        }
      }

      const startDate = dayjs(effectiveStartDate).startOf('isoWeek') // 确保从周一开始
      const todayObj = dayjs(date)

      // 计算周数差异（使用 isoWeek）
      const diffWeeks = Math.floor(todayObj.diff(startDate, 'week'))

      // 计算当前是第几个周期
      const periodNumber = Math.floor(diffWeeks / ruleObj.interval)

      // 计算当前周期的起始日期和结束日期（确保从周一开始）
      const periodStart = startDate.add(periodNumber * ruleObj.interval, 'week').startOf('isoWeek')
      console.log('periodStart', periodStart.format('YYYY-MM-DD'), periodStart.day()) // 应该是周一 (1)
      const periodEnd = periodStart.add(ruleObj.interval, 'week').subtract(1, 'day')
      console.log('periodEnd', periodEnd.format('YYYY-MM-DD'), periodEnd.day()) // 应该是周日 (0)

      // 计算当前是周期内的第几周
      const weekOfPeriod = Math.floor(todayObj.diff(periodStart, 'week')) + 1

      // 筛选出本周期内的记录
      const periodRecords = progressRecords.filter((record) => {
        const recordDate = dayjs(record.recTime)
        return recordDate.isSameOrAfter(periodStart) && recordDate.isSameOrBefore(periodEnd)
      })

      // 计算已完成的总次数
      const completedCount = periodRecords.reduce((sum, record) => sum + (record.val || 0), 0)

      // 判断今天是否应该显示
      let isOccurrenceToday = completedCount < totalRequired
      if (completedToday > 0) {
        isOccurrenceToday = true
      }

      return {
        totalOccurrences: 1,
        completedOccurrences: completedCount >= totalRequired ? 1 : 0,
        progress: (completedCount / totalRequired) * 100,
        isOccurrenceToday,
        completedToday,
        completedCount,
        totalCount: totalRequired,
        weekOfPeriod,
        totalWeeks: ruleObj.interval,
        isCompletedToday: completedCount >= totalRequired,
      }
    }

    case 'N_MONTHS': {
      // 每几个月类型
      if (!ruleObj.interval || !effectiveStartDate) {
        return {
          totalOccurrences: 0,
          completedOccurrences: 0,
          progress: 0,
          isOccurrenceToday: false,
          completedToday: 0,
          isCompletedToday: false,
        }
      }

      const startDate = dayjs(effectiveStartDate)
      const todayObj = dayjs(date)

      // 计算月份差异
      const diffMonths = (todayObj.year() - startDate.year()) * 12 + (todayObj.month() - startDate.month())

      // 计算当前是第几个周期
      const periodNumber = Math.floor(diffMonths / ruleObj.interval)

      // 计算当前周期的起始日期和结束日期
      const periodStart = startDate.add(periodNumber * ruleObj.interval, 'month')
      const periodEnd = periodStart.add(ruleObj.interval, 'month').subtract(1, 'day')

      // 生成周期标识符
      const periodKey = `${periodStart.format('YYYY-MM')}-${ruleObj.interval}M`

      // 计算当前是周期内的第几月
      const monthOfPeriod = ((todayObj.month() - periodStart.month() + 12) % 12) + 1

      // 筛选出本周期内的记录
      const periodRecords = progressRecords.filter((record) => {
        const recordDate = dayjs(record.recTime)
        return recordDate.isSameOrAfter(periodStart) && recordDate.isSameOrBefore(periodEnd)
      })

      // 计算已完成的总次数
      const completedCount = periodRecords.reduce((sum, record) => sum + (record.val || 0), 0)

      // 判断今天是否应该显示
      let isOccurrenceToday = completedCount < totalRequired
      if (completedToday > 0) {
        isOccurrenceToday = true
      }

      return {
        totalOccurrences: 1,
        completedOccurrences: completedCount >= totalRequired ? 1 : 0,
        progress: (completedCount / totalRequired) * 100,
        isOccurrenceToday,
        completedToday,
        completedCount,
        totalCount: totalRequired,
        monthOfPeriod,
        totalMonths: ruleObj.interval,
        isCompletedToday: completedCount >= totalRequired,
      }
    }

    default:
      console.error('getTaskProgressData 函数错误，未匹配到规则类型')
      return {
        totalOccurrences: 0,
        completedOccurrences: 0,
        progress: 0,
        isOccurrenceToday: false,
        completedToday: 0,
        isCompletedToday: false,
      }
  }
}

// 导出其他函数
/**
 * 将规则转换为中文描述
 * @param rule 循环规则或规则字符串
 * @returns 中文描述
 */
export const toText = (rule: string | Rule): string => {
  const ruleObj = parseRule(rule)
  let weekdays: string[]
  let days: string

  switch (ruleObj.type) {
    case 'DAILY':
      return '每天'

    case 'WEEKLY':
      if (!ruleObj.byweekday || ruleObj.byweekday.length === 0) return '每周'
      days = ruleObj.byweekday.map((code) => weekdayToChinese[code]).join('、')
      return `每周${days}`

    case 'MONTHLY':
      if (!ruleObj.bymonthday || ruleObj.bymonthday.length === 0) return '每月'
      return `每月${ruleObj.bymonthday.join('、')}号`

    case 'INTERVAL_DAILY':
      return `每隔${ruleObj.interval || 0}天`

    case 'WEEKLY_N_TIMES':
      return `每周${ruleObj.count || 0}天`

    case 'MONTHLY_N_TIMES':
      return `每月${ruleObj.count || 0}天`

    case 'N_DAYS':
      return `每${ruleObj.interval || 0}天`

    case 'N_WEEKS':
      return `每${ruleObj.interval || 0}周`

    case 'N_MONTHS':
      return `每${ruleObj.interval || 0}个月`

    default:
      return ''
  }
}

/**
 * 获取下一个重复日期
 * @param rule 循环规则或规则字符串
 * @param after 起始日期（YYYY-MM-DD 格式）
 * @returns 下一个重复日期
 */
export const getNextOccurrence = (rule: string | Rule, after: string): string | null => {
  const ruleObj = parseRule(rule)
  const startDate = new Date(after)
  console.error('getNextOccurrence 函数还未实现')
  return null
}
