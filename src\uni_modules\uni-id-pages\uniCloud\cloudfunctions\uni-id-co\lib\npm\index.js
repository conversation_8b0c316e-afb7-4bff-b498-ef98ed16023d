"use strict";var e=require("buffer"),r=require("stream"),t=require("util"),n=require("crypto"),o="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function i(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var a={},s={exports:{}};
/*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> */
!function(r,t){var n=e,o=n.Buffer;function i(e,r){for(var t in e)r[t]=e[t]}function a(e,r,t){return o(e,r,t)}o.from&&o.alloc&&o.allocUnsafe&&o.allocUnsafeSlow?r.exports=n:(i(n,t),t.Buffer=a),a.prototype=Object.create(o.prototype),i(o,a),a.from=function(e,r,t){if("number"==typeof e)throw new TypeError("Argument must not be a number");return o(e,r,t)},a.alloc=function(e,r,t){if("number"!=typeof e)throw new TypeError("Argument must be a number");var n=o(e);return void 0!==r?"string"==typeof t?n.fill(r,t):n.fill(r):n.fill(0),n},a.allocUnsafe=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return o(e)},a.allocUnsafeSlow=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return n.SlowBuffer(e)}}(s,s.exports);var u=s.exports,c=u.Buffer,f=r;function l(e){if(this.buffer=null,this.writable=!0,this.readable=!0,!e)return this.buffer=c.alloc(0),this;if("function"==typeof e.pipe)return this.buffer=c.alloc(0),e.pipe(this),this;if(e.length||"object"==typeof e)return this.buffer=e,this.writable=!1,process.nextTick(function(){this.emit("end",e),this.readable=!1,this.emit("close")}.bind(this)),this;throw new TypeError("Unexpected data type ("+typeof e+")")}t.inherits(l,f),l.prototype.write=function(e){this.buffer=c.concat([this.buffer,c.from(e)]),this.emit("data",e)},l.prototype.end=function(e){e&&this.write(e),this.emit("end",e),this.emit("close"),this.writable=!1,this.readable=!1};var p=l,h=e.Buffer,v=e.SlowBuffer,d=y;function y(e,r){if(!h.isBuffer(e)||!h.isBuffer(r))return!1;if(e.length!==r.length)return!1;for(var t=0,n=0;n<e.length;n++)t|=e[n]^r[n];return 0===t}y.install=function(){h.prototype.equal=v.prototype.equal=function(e){return y(this,e)}};var m=h.prototype.equal,g=v.prototype.equal;function b(e){return(e/8|0)+(e%8==0?0:1)}y.restore=function(){h.prototype.equal=m,v.prototype.equal=g};var w={ES256:b(256),ES384:b(384),ES512:b(521)};var j=function(e){var r=w[e];if(r)return r;throw new Error('Unknown algorithm "'+e+'"')},S=u.Buffer,_=j,E=128;function x(e){if(S.isBuffer(e))return e;if("string"==typeof e)return S.from(e,"base64");throw new TypeError("ECDSA signature must be a Base64 string or a Buffer")}function O(e,r,t){for(var n=0;r+n<t&&0===e[r+n];)++n;return e[r+n]>=E&&--n,n}var A={derToJose:function(e,r){e=x(e);var t=_(r),n=t+1,o=e.length,i=0;if(48!==e[i++])throw new Error('Could not find expected "seq"');var a=e[i++];if(a===(1|E)&&(a=e[i++]),o-i<a)throw new Error('"seq" specified length of "'+a+'", only "'+(o-i)+'" remaining');if(2!==e[i++])throw new Error('Could not find expected "int" for "r"');var s=e[i++];if(o-i-2<s)throw new Error('"r" specified length of "'+s+'", only "'+(o-i-2)+'" available');if(n<s)throw new Error('"r" specified length of "'+s+'", max of "'+n+'" is acceptable');var u=i;if(i+=s,2!==e[i++])throw new Error('Could not find expected "int" for "s"');var c=e[i++];if(o-i!==c)throw new Error('"s" specified length of "'+c+'", expected "'+(o-i)+'"');if(n<c)throw new Error('"s" specified length of "'+c+'", max of "'+n+'" is acceptable');var f=i;if((i+=c)!==o)throw new Error('Expected to consume entire buffer, but "'+(o-i)+'" bytes remain');var l=t-s,p=t-c,h=S.allocUnsafe(l+s+p+c);for(i=0;i<l;++i)h[i]=0;e.copy(h,i,u+Math.max(-l,0),u+s);for(var v=i=t;i<v+p;++i)h[i]=0;return e.copy(h,i,f+Math.max(-p,0),f+c),h=(h=h.toString("base64")).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")},joseToDer:function(e,r){e=x(e);var t=_(r),n=e.length;if(n!==2*t)throw new TypeError('"'+r+'" signatures must be "'+2*t+'" bytes, saw "'+n+'"');var o=O(e,0,t),i=O(e,t,e.length),a=t-o,s=t-i,u=2+a+1+1+s,c=u<E,f=S.allocUnsafe((c?2:3)+u),l=0;return f[l++]=48,c?f[l++]=u:(f[l++]=1|E,f[l++]=255&u),f[l++]=2,f[l++]=a,o<0?(f[l++]=0,l+=e.copy(f,l,0,t)):l+=e.copy(f,l,o,t),f[l++]=2,f[l++]=s,i<0?(f[l++]=0,e.copy(f,l,t)):e.copy(f,l,t+i),f}},T=d,k=u.Buffer,P=n,R=A,B=t,$="secret must be a string or buffer",I="key must be a string or a buffer",V="key must be a string, a buffer or an object",N="function"==typeof P.createPublicKey;function M(e){if(!k.isBuffer(e)&&"string"!=typeof e){if(!N)throw C(I);if("object"!=typeof e)throw C(I);if("string"!=typeof e.type)throw C(I);if("string"!=typeof e.asymmetricKeyType)throw C(I);if("function"!=typeof e.export)throw C(I)}}function z(e){if(!k.isBuffer(e)&&"string"!=typeof e&&"object"!=typeof e)throw C(V)}function D(e){return e.replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function q(e){var r=4-(e=e.toString()).length%4;if(4!==r)for(var t=0;t<r;++t)e+="=";return e.replace(/\-/g,"+").replace(/_/g,"/")}function C(e){var r=[].slice.call(arguments,1),t=B.format.bind(B,e).apply(null,r);return new TypeError(t)}function H(e){var r;return r=e,k.isBuffer(r)||"string"==typeof r||(e=JSON.stringify(e)),e}function U(e){return function(r,t){!function(e){if(!k.isBuffer(e)){if("string"==typeof e)return e;if(!N)throw C($);if("object"!=typeof e)throw C($);if("secret"!==e.type)throw C($);if("function"!=typeof e.export)throw C($)}}(t),r=H(r);var n=P.createHmac("sha"+e,t);return D((n.update(r),n.digest("base64")))}}function L(e){return function(r,t,n){var o=U(e)(r,n);return T(k.from(t),k.from(o))}}function G(e){return function(r,t){z(t),r=H(r);var n=P.createSign("RSA-SHA"+e);return D((n.update(r),n.sign(t,"base64")))}}function K(e){return function(r,t,n){M(n),r=H(r),t=q(t);var o=P.createVerify("RSA-SHA"+e);return o.update(r),o.verify(n,t,"base64")}}function F(e){return function(r,t){z(t),r=H(r);var n=P.createSign("RSA-SHA"+e);return D((n.update(r),n.sign({key:t,padding:P.constants.RSA_PKCS1_PSS_PADDING,saltLength:P.constants.RSA_PSS_SALTLEN_DIGEST},"base64")))}}function J(e){return function(r,t,n){M(n),r=H(r),t=q(t);var o=P.createVerify("RSA-SHA"+e);return o.update(r),o.verify({key:n,padding:P.constants.RSA_PKCS1_PSS_PADDING,saltLength:P.constants.RSA_PSS_SALTLEN_DIGEST},t,"base64")}}function W(e){var r=G(e);return function(){var t=r.apply(null,arguments);return t=R.derToJose(t,"ES"+e)}}function Z(e){var r=K(e);return function(t,n,o){return n=R.joseToDer(n,"ES"+e).toString("base64"),r(t,n,o)}}function X(){return function(){return""}}function Y(){return function(e,r){return""===r}}N&&(I+=" or a KeyObject",$+="or a KeyObject");var Q=function(e){var r={hs:U,rs:G,ps:F,es:W,none:X},t={hs:L,rs:K,ps:J,es:Z,none:Y},n=e.match(/^(RS|PS|ES|HS)(256|384|512)$|^(none)$/i);if(!n)throw C('"%s" is not a valid algorithm.\n  Supported algorithms are:\n  "HS256", "HS384", "HS512", "RS256", "RS384", "RS512", "PS256", "PS384", "PS512", "ES256", "ES384", "ES512" and "none".',e);var o=(n[1]||n[3]).toLowerCase(),i=n[2];return{sign:r[o](i),verify:t[o](i)}},ee=e.Buffer,re=function(e){return"string"==typeof e?e:"number"==typeof e||ee.isBuffer(e)?e.toString():JSON.stringify(e)},te=u.Buffer,ne=p,oe=Q,ie=r,ae=re,se=t;function ue(e,r){return te.from(e,r).toString("base64").replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function ce(e){var r=e.header,t=e.payload,n=e.secret||e.privateKey,o=e.encoding,i=oe(r.alg),a=function(e,r,t){t=t||"utf8";var n=ue(ae(e),"binary"),o=ue(ae(r),t);return se.format("%s.%s",n,o)}(r,t,o),s=i.sign(a,n);return se.format("%s.%s",a,s)}function fe(e){var r=e.secret||e.privateKey||e.key,t=new ne(r);this.readable=!0,this.header=e.header,this.encoding=e.encoding,this.secret=this.privateKey=this.key=t,this.payload=new ne(e.payload),this.secret.once("close",function(){!this.payload.writable&&this.readable&&this.sign()}.bind(this)),this.payload.once("close",function(){!this.secret.writable&&this.readable&&this.sign()}.bind(this))}se.inherits(fe,ie),fe.prototype.sign=function(){try{var e=ce({header:this.header,payload:this.payload.buffer,secret:this.secret.buffer,encoding:this.encoding});return this.emit("done",e),this.emit("data",e),this.emit("end"),this.readable=!1,e}catch(e){this.readable=!1,this.emit("error",e),this.emit("close")}},fe.sign=ce;var le=fe,pe=u.Buffer,he=p,ve=Q,de=r,ye=re,me=/^[a-zA-Z0-9\-_]+?\.[a-zA-Z0-9\-_]+?\.([a-zA-Z0-9\-_]+)?$/;function ge(e){if(function(e){return"[object Object]"===Object.prototype.toString.call(e)}(e))return e;try{return JSON.parse(e)}catch(e){return}}function be(e){var r=e.split(".",1)[0];return ge(pe.from(r,"base64").toString("binary"))}function we(e){return e.split(".")[2]}function je(e){return me.test(e)&&!!be(e)}function Se(e,r,t){if(!r){var n=new Error("Missing algorithm parameter for jws.verify");throw n.code="MISSING_ALGORITHM",n}var o=we(e=ye(e)),i=function(e){return e.split(".",2).join(".")}(e);return ve(r).verify(i,o,t)}function _e(e,r){if(r=r||{},!je(e=ye(e)))return null;var t=be(e);if(!t)return null;var n=function(e,r){r=r||"utf8";var t=e.split(".")[1];return pe.from(t,"base64").toString(r)}(e);return("JWT"===t.typ||r.json)&&(n=JSON.parse(n,r.encoding)),{header:t,payload:n,signature:we(e)}}function Ee(e){var r=(e=e||{}).secret||e.publicKey||e.key,t=new he(r);this.readable=!0,this.algorithm=e.algorithm,this.encoding=e.encoding,this.secret=this.publicKey=this.key=t,this.signature=new he(e.signature),this.secret.once("close",function(){!this.signature.writable&&this.readable&&this.verify()}.bind(this)),this.signature.once("close",function(){!this.secret.writable&&this.readable&&this.verify()}.bind(this))}t.inherits(Ee,de),Ee.prototype.verify=function(){try{var e=Se(this.signature.buffer,this.algorithm,this.key.buffer),r=_e(this.signature.buffer,this.encoding);return this.emit("done",e,r),this.emit("data",e),this.emit("end"),this.readable=!1,e}catch(e){this.readable=!1,this.emit("error",e),this.emit("close")}},Ee.decode=_e,Ee.isValid=je,Ee.verify=Se;var xe=le,Oe=Ee;a.ALGORITHMS=["HS256","HS384","HS512","RS256","RS384","RS512","PS256","PS384","PS512","ES256","ES384","ES512"],a.sign=xe.sign,a.verify=Oe.verify,a.decode=Oe.decode,a.isValid=Oe.isValid,a.createSign=function(e){return new xe(e)},a.createVerify=function(e){return new Oe(e)};var Ae=a,Te=function(e,r){r=r||{};var t=Ae.decode(e,r);if(!t)return null;var n=t.payload;if("string"==typeof n)try{var o=JSON.parse(n);null!==o&&"object"==typeof o&&(n=o)}catch(e){}return!0===r.complete?{header:t.header,payload:n,signature:t.signature}:n},ke=function(e,r){Error.call(this,e),Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor),this.name="JsonWebTokenError",this.message=e,r&&(this.inner=r)};(ke.prototype=Object.create(Error.prototype)).constructor=ke;var Pe=ke,Re=Pe,Be=function(e,r){Re.call(this,e),this.name="NotBeforeError",this.date=r};(Be.prototype=Object.create(Re.prototype)).constructor=Be;var $e=Be,Ie=Pe,Ve=function(e,r){Ie.call(this,e),this.name="TokenExpiredError",this.expiredAt=r};(Ve.prototype=Object.create(Ie.prototype)).constructor=Ve;var Ne=Ve,Me=1e3,ze=60*Me,De=60*ze,qe=24*De,Ce=7*qe,He=365.25*qe;function Ue(e,r,t,n){var o=r>=1.5*t;return Math.round(e/t)+" "+n+(o?"s":"")}var Le=function(e,r){r=r||{};var t=typeof e;if("string"===t&&e.length>0)return function(e){if((e=String(e)).length>100)return;var r=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(!r)return;var t=parseFloat(r[1]);switch((r[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return t*He;case"weeks":case"week":case"w":return t*Ce;case"days":case"day":case"d":return t*qe;case"hours":case"hour":case"hrs":case"hr":case"h":return t*De;case"minutes":case"minute":case"mins":case"min":case"m":return t*ze;case"seconds":case"second":case"secs":case"sec":case"s":return t*Me;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return t;default:return}}(e);if("number"===t&&isFinite(e))return r.long?function(e){var r=Math.abs(e);if(r>=qe)return Ue(e,r,qe,"day");if(r>=De)return Ue(e,r,De,"hour");if(r>=ze)return Ue(e,r,ze,"minute");if(r>=Me)return Ue(e,r,Me,"second");return e+" ms"}(e):function(e){var r=Math.abs(e);if(r>=qe)return Math.round(e/qe)+"d";if(r>=De)return Math.round(e/De)+"h";if(r>=ze)return Math.round(e/ze)+"m";if(r>=Me)return Math.round(e/Me)+"s";return e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))},Ge=function(e,r){var t=r||Math.floor(Date.now()/1e3);if("string"==typeof e){var n=Le(e);if(void 0===n)return;return Math.floor(t+n/1e3)}return"number"==typeof e?t+e:void 0},Ke={exports:{}};!function(e,r){var t;r=Ke.exports=Y,t="object"==typeof process&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?function(){var e=Array.prototype.slice.call(arguments,0);e.unshift("SEMVER"),console.log.apply(console,e)}:function(){},r.SEMVER_SPEC_VERSION="2.0.0";var n=256,o=Number.MAX_SAFE_INTEGER||9007199254740991,i=n-6,a=r.re=[],s=r.safeRe=[],u=r.src=[],c=0,f="[a-zA-Z0-9-]",l=[["\\s",1],["\\d",n],[f,i]];function p(e){for(var r=0;r<l.length;r++){var t=l[r][0],n=l[r][1];e=e.split(t+"*").join(t+"{0,"+n+"}").split(t+"+").join(t+"{1,"+n+"}")}return e}var h=c++;u[h]="0|[1-9]\\d*";var v=c++;u[v]="\\d+";var d=c++;u[d]="\\d*[a-zA-Z-]"+f+"*";var y=c++;u[y]="("+u[h]+")\\.("+u[h]+")\\.("+u[h]+")";var m=c++;u[m]="("+u[v]+")\\.("+u[v]+")\\.("+u[v]+")";var g=c++;u[g]="(?:"+u[h]+"|"+u[d]+")";var b=c++;u[b]="(?:"+u[v]+"|"+u[d]+")";var w=c++;u[w]="(?:-("+u[g]+"(?:\\."+u[g]+")*))";var j=c++;u[j]="(?:-?("+u[b]+"(?:\\."+u[b]+")*))";var S=c++;u[S]=f+"+";var _=c++;u[_]="(?:\\+("+u[S]+"(?:\\."+u[S]+")*))";var E=c++,x="v?"+u[y]+u[w]+"?"+u[_]+"?";u[E]="^"+x+"$";var O="[v=\\s]*"+u[m]+u[j]+"?"+u[_]+"?",A=c++;u[A]="^"+O+"$";var T=c++;u[T]="((?:<|>)?=?)";var k=c++;u[k]=u[v]+"|x|X|\\*";var P=c++;u[P]=u[h]+"|x|X|\\*";var R=c++;u[R]="[v=\\s]*("+u[P]+")(?:\\.("+u[P]+")(?:\\.("+u[P]+")(?:"+u[w]+")?"+u[_]+"?)?)?";var B=c++;u[B]="[v=\\s]*("+u[k]+")(?:\\.("+u[k]+")(?:\\.("+u[k]+")(?:"+u[j]+")?"+u[_]+"?)?)?";var $=c++;u[$]="^"+u[T]+"\\s*"+u[R]+"$";var I=c++;u[I]="^"+u[T]+"\\s*"+u[B]+"$";var V=c++;u[V]="(?:^|[^\\d])(\\d{1,16})(?:\\.(\\d{1,16}))?(?:\\.(\\d{1,16}))?(?:$|[^\\d])";var N=c++;u[N]="(?:~>?)";var M=c++;u[M]="(\\s*)"+u[N]+"\\s+",a[M]=new RegExp(u[M],"g"),s[M]=new RegExp(p(u[M]),"g");var z=c++;u[z]="^"+u[N]+u[R]+"$";var D=c++;u[D]="^"+u[N]+u[B]+"$";var q=c++;u[q]="(?:\\^)";var C=c++;u[C]="(\\s*)"+u[q]+"\\s+",a[C]=new RegExp(u[C],"g"),s[C]=new RegExp(p(u[C]),"g");var H=c++;u[H]="^"+u[q]+u[R]+"$";var U=c++;u[U]="^"+u[q]+u[B]+"$";var L=c++;u[L]="^"+u[T]+"\\s*("+O+")$|^$";var G=c++;u[G]="^"+u[T]+"\\s*("+x+")$|^$";var K=c++;u[K]="(\\s*)"+u[T]+"\\s*("+O+"|"+u[R]+")",a[K]=new RegExp(u[K],"g"),s[K]=new RegExp(p(u[K]),"g");var F=c++;u[F]="^\\s*("+u[R]+")\\s+-\\s+("+u[R]+")\\s*$";var J=c++;u[J]="^\\s*("+u[B]+")\\s+-\\s+("+u[B]+")\\s*$";var W=c++;u[W]="(<|>)?=?\\s*\\*";for(var Z=0;Z<35;Z++)t(Z,u[Z]),a[Z]||(a[Z]=new RegExp(u[Z]),s[Z]=new RegExp(p(u[Z])));function X(e,r){if(r&&"object"==typeof r||(r={loose:!!r,includePrerelease:!1}),e instanceof Y)return e;if("string"!=typeof e)return null;if(e.length>n)return null;if(!(r.loose?s[A]:s[E]).test(e))return null;try{return new Y(e,r)}catch(e){return null}}function Y(e,r){if(r&&"object"==typeof r||(r={loose:!!r,includePrerelease:!1}),e instanceof Y){if(e.loose===r.loose)return e;e=e.version}else if("string"!=typeof e)throw new TypeError("Invalid Version: "+e);if(e.length>n)throw new TypeError("version is longer than "+n+" characters");if(!(this instanceof Y))return new Y(e,r);t("SemVer",e,r),this.options=r,this.loose=!!r.loose;var i=e.trim().match(r.loose?s[A]:s[E]);if(!i)throw new TypeError("Invalid Version: "+e);if(this.raw=e,this.major=+i[1],this.minor=+i[2],this.patch=+i[3],this.major>o||this.major<0)throw new TypeError("Invalid major version");if(this.minor>o||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>o||this.patch<0)throw new TypeError("Invalid patch version");i[4]?this.prerelease=i[4].split(".").map((function(e){if(/^[0-9]+$/.test(e)){var r=+e;if(r>=0&&r<o)return r}return e})):this.prerelease=[],this.build=i[5]?i[5].split("."):[],this.format()}r.parse=X,r.valid=function(e,r){var t=X(e,r);return t?t.version:null},r.clean=function(e,r){var t=X(e.trim().replace(/^[=v]+/,""),r);return t?t.version:null},r.SemVer=Y,Y.prototype.format=function(){return this.version=this.major+"."+this.minor+"."+this.patch,this.prerelease.length&&(this.version+="-"+this.prerelease.join(".")),this.version},Y.prototype.toString=function(){return this.version},Y.prototype.compare=function(e){return t("SemVer.compare",this.version,this.options,e),e instanceof Y||(e=new Y(e,this.options)),this.compareMain(e)||this.comparePre(e)},Y.prototype.compareMain=function(e){return e instanceof Y||(e=new Y(e,this.options)),ee(this.major,e.major)||ee(this.minor,e.minor)||ee(this.patch,e.patch)},Y.prototype.comparePre=function(e){if(e instanceof Y||(e=new Y(e,this.options)),this.prerelease.length&&!e.prerelease.length)return-1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;var r=0;do{var n=this.prerelease[r],o=e.prerelease[r];if(t("prerelease compare",r,n,o),void 0===n&&void 0===o)return 0;if(void 0===o)return 1;if(void 0===n)return-1;if(n!==o)return ee(n,o)}while(++r)},Y.prototype.inc=function(e,r){switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",r);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",r);break;case"prepatch":this.prerelease.length=0,this.inc("patch",r),this.inc("pre",r);break;case"prerelease":0===this.prerelease.length&&this.inc("patch",r),this.inc("pre",r);break;case"major":0===this.minor&&0===this.patch&&0!==this.prerelease.length||this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":0===this.patch&&0!==this.prerelease.length||this.minor++,this.patch=0,this.prerelease=[];break;case"patch":0===this.prerelease.length&&this.patch++,this.prerelease=[];break;case"pre":if(0===this.prerelease.length)this.prerelease=[0];else{for(var t=this.prerelease.length;--t>=0;)"number"==typeof this.prerelease[t]&&(this.prerelease[t]++,t=-2);-1===t&&this.prerelease.push(0)}r&&(this.prerelease[0]===r?isNaN(this.prerelease[1])&&(this.prerelease=[r,0]):this.prerelease=[r,0]);break;default:throw new Error("invalid increment argument: "+e)}return this.format(),this.raw=this.version,this},r.inc=function(e,r,t,n){"string"==typeof t&&(n=t,t=void 0);try{return new Y(e,t).inc(r,n).version}catch(e){return null}},r.diff=function(e,r){if(oe(e,r))return null;var t=X(e),n=X(r),o="";if(t.prerelease.length||n.prerelease.length){o="pre";var i="prerelease"}for(var a in t)if(("major"===a||"minor"===a||"patch"===a)&&t[a]!==n[a])return o+a;return i},r.compareIdentifiers=ee;var Q=/^[0-9]+$/;function ee(e,r){var t=Q.test(e),n=Q.test(r);return t&&n&&(e=+e,r=+r),e===r?0:t&&!n?-1:n&&!t?1:e<r?-1:1}function re(e,r,t){return new Y(e,t).compare(new Y(r,t))}function te(e,r,t){return re(e,r,t)>0}function ne(e,r,t){return re(e,r,t)<0}function oe(e,r,t){return 0===re(e,r,t)}function ie(e,r,t){return 0!==re(e,r,t)}function ae(e,r,t){return re(e,r,t)>=0}function se(e,r,t){return re(e,r,t)<=0}function ue(e,r,t,n){switch(r){case"===":return"object"==typeof e&&(e=e.version),"object"==typeof t&&(t=t.version),e===t;case"!==":return"object"==typeof e&&(e=e.version),"object"==typeof t&&(t=t.version),e!==t;case"":case"=":case"==":return oe(e,t,n);case"!=":return ie(e,t,n);case">":return te(e,t,n);case">=":return ae(e,t,n);case"<":return ne(e,t,n);case"<=":return se(e,t,n);default:throw new TypeError("Invalid operator: "+r)}}function ce(e,r){if(r&&"object"==typeof r||(r={loose:!!r,includePrerelease:!1}),e instanceof ce){if(e.loose===!!r.loose)return e;e=e.value}if(!(this instanceof ce))return new ce(e,r);e=e.trim().split(/\s+/).join(" "),t("comparator",e,r),this.options=r,this.loose=!!r.loose,this.parse(e),this.semver===fe?this.value="":this.value=this.operator+this.semver.version,t("comp",this)}r.rcompareIdentifiers=function(e,r){return ee(r,e)},r.major=function(e,r){return new Y(e,r).major},r.minor=function(e,r){return new Y(e,r).minor},r.patch=function(e,r){return new Y(e,r).patch},r.compare=re,r.compareLoose=function(e,r){return re(e,r,!0)},r.rcompare=function(e,r,t){return re(r,e,t)},r.sort=function(e,t){return e.sort((function(e,n){return r.compare(e,n,t)}))},r.rsort=function(e,t){return e.sort((function(e,n){return r.rcompare(e,n,t)}))},r.gt=te,r.lt=ne,r.eq=oe,r.neq=ie,r.gte=ae,r.lte=se,r.cmp=ue,r.Comparator=ce;var fe={};function le(e,r){if(r&&"object"==typeof r||(r={loose:!!r,includePrerelease:!1}),e instanceof le)return e.loose===!!r.loose&&e.includePrerelease===!!r.includePrerelease?e:new le(e.raw,r);if(e instanceof ce)return new le(e.value,r);if(!(this instanceof le))return new le(e,r);if(this.options=r,this.loose=!!r.loose,this.includePrerelease=!!r.includePrerelease,this.raw=e.trim().split(/\s+/).join(" "),this.set=this.raw.split("||").map((function(e){return this.parseRange(e.trim())}),this).filter((function(e){return e.length})),!this.set.length)throw new TypeError("Invalid SemVer Range: "+this.raw);this.format()}function pe(e){return!e||"x"===e.toLowerCase()||"*"===e}function he(e,r,t,n,o,i,a,s,u,c,f,l,p){return((r=pe(t)?"":pe(n)?">="+t+".0.0":pe(o)?">="+t+"."+n+".0":">="+r)+" "+(s=pe(u)?"":pe(c)?"<"+(+u+1)+".0.0":pe(f)?"<"+u+"."+(+c+1)+".0":l?"<="+u+"."+c+"."+f+"-"+l:"<="+s)).trim()}function ve(e,r,n){for(var o=0;o<e.length;o++)if(!e[o].test(r))return!1;if(r.prerelease.length&&!n.includePrerelease){for(o=0;o<e.length;o++)if(t(e[o].semver),e[o].semver!==fe&&e[o].semver.prerelease.length>0){var i=e[o].semver;if(i.major===r.major&&i.minor===r.minor&&i.patch===r.patch)return!0}return!1}return!0}function de(e,r,t){try{r=new le(r,t)}catch(e){return!1}return r.test(e)}function ye(e,r,t,n){var o,i,a,s,u;switch(e=new Y(e,n),r=new le(r,n),t){case">":o=te,i=se,a=ne,s=">",u=">=";break;case"<":o=ne,i=ae,a=te,s="<",u="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(de(e,r,n))return!1;for(var c=0;c<r.set.length;++c){var f=r.set[c],l=null,p=null;if(f.forEach((function(e){e.semver===fe&&(e=new ce(">=0.0.0")),l=l||e,p=p||e,o(e.semver,l.semver,n)?l=e:a(e.semver,p.semver,n)&&(p=e)})),l.operator===s||l.operator===u)return!1;if((!p.operator||p.operator===s)&&i(e,p.semver))return!1;if(p.operator===u&&a(e,p.semver))return!1}return!0}ce.prototype.parse=function(e){var r=this.options.loose?s[L]:s[G],t=e.match(r);if(!t)throw new TypeError("Invalid comparator: "+e);this.operator=t[1],"="===this.operator&&(this.operator=""),t[2]?this.semver=new Y(t[2],this.options.loose):this.semver=fe},ce.prototype.toString=function(){return this.value},ce.prototype.test=function(e){return t("Comparator.test",e,this.options.loose),this.semver===fe||("string"==typeof e&&(e=new Y(e,this.options)),ue(e,this.operator,this.semver,this.options))},ce.prototype.intersects=function(e,r){if(!(e instanceof ce))throw new TypeError("a Comparator is required");var t;if(r&&"object"==typeof r||(r={loose:!!r,includePrerelease:!1}),""===this.operator)return t=new le(e.value,r),de(this.value,t,r);if(""===e.operator)return t=new le(this.value,r),de(e.semver,t,r);var n=!(">="!==this.operator&&">"!==this.operator||">="!==e.operator&&">"!==e.operator),o=!("<="!==this.operator&&"<"!==this.operator||"<="!==e.operator&&"<"!==e.operator),i=this.semver.version===e.semver.version,a=!(">="!==this.operator&&"<="!==this.operator||">="!==e.operator&&"<="!==e.operator),s=ue(this.semver,"<",e.semver,r)&&(">="===this.operator||">"===this.operator)&&("<="===e.operator||"<"===e.operator),u=ue(this.semver,">",e.semver,r)&&("<="===this.operator||"<"===this.operator)&&(">="===e.operator||">"===e.operator);return n||o||i&&a||s||u},r.Range=le,le.prototype.format=function(){return this.range=this.set.map((function(e){return e.join(" ").trim()})).join("||").trim(),this.range},le.prototype.toString=function(){return this.range},le.prototype.parseRange=function(e){var r=this.options.loose,n=r?s[J]:s[F];e=e.replace(n,he),t("hyphen replace",e),e=e.replace(s[K],"$1$2$3"),t("comparator trim",e,s[K]),e=(e=e.replace(s[M],"$1~")).replace(s[C],"$1^");var o=r?s[L]:s[G],i=e.split(" ").map((function(e){return function(e,r){return t("comp",e,r),e=function(e,r){return e.trim().split(/\s+/).map((function(e){return function(e,r){t("caret",e,r);var n=r.loose?s[U]:s[H];return e.replace(n,(function(r,n,o,i,a){var s;return t("caret",e,r,n,o,i,a),pe(n)?s="":pe(o)?s=">="+n+".0.0 <"+(+n+1)+".0.0":pe(i)?s="0"===n?">="+n+"."+o+".0 <"+n+"."+(+o+1)+".0":">="+n+"."+o+".0 <"+(+n+1)+".0.0":a?(t("replaceCaret pr",a),s="0"===n?"0"===o?">="+n+"."+o+"."+i+"-"+a+" <"+n+"."+o+"."+(+i+1):">="+n+"."+o+"."+i+"-"+a+" <"+n+"."+(+o+1)+".0":">="+n+"."+o+"."+i+"-"+a+" <"+(+n+1)+".0.0"):(t("no pr"),s="0"===n?"0"===o?">="+n+"."+o+"."+i+" <"+n+"."+o+"."+(+i+1):">="+n+"."+o+"."+i+" <"+n+"."+(+o+1)+".0":">="+n+"."+o+"."+i+" <"+(+n+1)+".0.0"),t("caret return",s),s}))}(e,r)})).join(" ")}(e,r),t("caret",e),e=function(e,r){return e.trim().split(/\s+/).map((function(e){return function(e,r){var n=r.loose?s[D]:s[z];return e.replace(n,(function(r,n,o,i,a){var s;return t("tilde",e,r,n,o,i,a),pe(n)?s="":pe(o)?s=">="+n+".0.0 <"+(+n+1)+".0.0":pe(i)?s=">="+n+"."+o+".0 <"+n+"."+(+o+1)+".0":a?(t("replaceTilde pr",a),s=">="+n+"."+o+"."+i+"-"+a+" <"+n+"."+(+o+1)+".0"):s=">="+n+"."+o+"."+i+" <"+n+"."+(+o+1)+".0",t("tilde return",s),s}))}(e,r)})).join(" ")}(e,r),t("tildes",e),e=function(e,r){return t("replaceXRanges",e,r),e.split(/\s+/).map((function(e){return function(e,r){e=e.trim();var n=r.loose?s[I]:s[$];return e.replace(n,(function(r,n,o,i,a,s){t("xRange",e,r,n,o,i,a,s);var u=pe(o),c=u||pe(i),f=c||pe(a);return"="===n&&f&&(n=""),u?r=">"===n||"<"===n?"<0.0.0":"*":n&&f?(c&&(i=0),a=0,">"===n?(n=">=",c?(o=+o+1,i=0,a=0):(i=+i+1,a=0)):"<="===n&&(n="<",c?o=+o+1:i=+i+1),r=n+o+"."+i+"."+a):c?r=">="+o+".0.0 <"+(+o+1)+".0.0":f&&(r=">="+o+"."+i+".0 <"+o+"."+(+i+1)+".0"),t("xRange return",r),r}))}(e,r)})).join(" ")}(e,r),t("xrange",e),e=function(e,r){return t("replaceStars",e,r),e.trim().replace(s[W],"")}(e,r),t("stars",e),e}(e,this.options)}),this).join(" ").split(/\s+/);return this.options.loose&&(i=i.filter((function(e){return!!e.match(o)}))),i=i.map((function(e){return new ce(e,this.options)}),this)},le.prototype.intersects=function(e,r){if(!(e instanceof le))throw new TypeError("a Range is required");return this.set.some((function(t){return t.every((function(t){return e.set.some((function(e){return e.every((function(e){return t.intersects(e,r)}))}))}))}))},r.toComparators=function(e,r){return new le(e,r).set.map((function(e){return e.map((function(e){return e.value})).join(" ").trim().split(" ")}))},le.prototype.test=function(e){if(!e)return!1;"string"==typeof e&&(e=new Y(e,this.options));for(var r=0;r<this.set.length;r++)if(ve(this.set[r],e,this.options))return!0;return!1},r.satisfies=de,r.maxSatisfying=function(e,r,t){var n=null,o=null;try{var i=new le(r,t)}catch(e){return null}return e.forEach((function(e){i.test(e)&&(n&&-1!==o.compare(e)||(o=new Y(n=e,t)))})),n},r.minSatisfying=function(e,r,t){var n=null,o=null;try{var i=new le(r,t)}catch(e){return null}return e.forEach((function(e){i.test(e)&&(n&&1!==o.compare(e)||(o=new Y(n=e,t)))})),n},r.minVersion=function(e,r){e=new le(e,r);var t=new Y("0.0.0");if(e.test(t))return t;if(t=new Y("0.0.0-0"),e.test(t))return t;t=null;for(var n=0;n<e.set.length;++n){e.set[n].forEach((function(e){var r=new Y(e.semver.version);switch(e.operator){case">":0===r.prerelease.length?r.patch++:r.prerelease.push(0),r.raw=r.format();case"":case">=":t&&!te(t,r)||(t=r);break;case"<":case"<=":break;default:throw new Error("Unexpected operation: "+e.operator)}}))}if(t&&e.test(t))return t;return null},r.validRange=function(e,r){try{return new le(e,r).range||"*"}catch(e){return null}},r.ltr=function(e,r,t){return ye(e,r,"<",t)},r.gtr=function(e,r,t){return ye(e,r,">",t)},r.outside=ye,r.prerelease=function(e,r){var t=X(e,r);return t&&t.prerelease.length?t.prerelease:null},r.intersects=function(e,r,t){return e=new le(e,t),r=new le(r,t),e.intersects(r)},r.coerce=function(e){if(e instanceof Y)return e;if("string"!=typeof e)return null;var r=e.match(s[V]);if(null==r)return null;return X(r[1]+"."+(r[2]||"0")+"."+(r[3]||"0"))}}(0,Ke.exports);var Fe=Ke.exports.satisfies(process.version,"^6.12.0 || >=8.0.0"),Je=Pe,We=$e,Ze=Ne,Xe=Te,Ye=Ge,Qe=a,er=["RS256","RS384","RS512","ES256","ES384","ES512"],rr=["RS256","RS384","RS512"],tr=["HS256","HS384","HS512"];Fe&&(er.splice(3,0,"PS256","PS384","PS512"),rr.splice(3,0,"PS256","PS384","PS512"));var nr=1/0,or=9007199254740991,ir=17976931348623157e292,ar=NaN,sr="[object Arguments]",ur="[object Function]",cr="[object GeneratorFunction]",fr="[object String]",lr="[object Symbol]",pr=/^\s+|\s+$/g,hr=/^[-+]0x[0-9a-f]+$/i,vr=/^0b[01]+$/i,dr=/^0o[0-7]+$/i,yr=/^(?:0|[1-9]\d*)$/,mr=parseInt;function gr(e){return e!=e}function br(e,r){return function(e,r){for(var t=-1,n=e?e.length:0,o=Array(n);++t<n;)o[t]=r(e[t],t,e);return o}(r,(function(r){return e[r]}))}var wr,jr,Sr=Object.prototype,_r=Sr.hasOwnProperty,Er=Sr.toString,xr=Sr.propertyIsEnumerable,Or=(wr=Object.keys,jr=Object,function(e){return wr(jr(e))}),Ar=Math.max;function Tr(e,r){var t=Rr(e)||function(e){return function(e){return Ir(e)&&Br(e)}(e)&&_r.call(e,"callee")&&(!xr.call(e,"callee")||Er.call(e)==sr)}(e)?function(e,r){for(var t=-1,n=Array(e);++t<e;)n[t]=r(t);return n}(e.length,String):[],n=t.length,o=!!n;for(var i in e)!r&&!_r.call(e,i)||o&&("length"==i||Pr(i,n))||t.push(i);return t}function kr(e){if(t=(r=e)&&r.constructor,n="function"==typeof t&&t.prototype||Sr,r!==n)return Or(e);var r,t,n,o=[];for(var i in Object(e))_r.call(e,i)&&"constructor"!=i&&o.push(i);return o}function Pr(e,r){return!!(r=null==r?or:r)&&("number"==typeof e||yr.test(e))&&e>-1&&e%1==0&&e<r}var Rr=Array.isArray;function Br(e){return null!=e&&function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=or}(e.length)&&!function(e){var r=$r(e)?Er.call(e):"";return r==ur||r==cr}(e)}function $r(e){var r=typeof e;return!!e&&("object"==r||"function"==r)}function Ir(e){return!!e&&"object"==typeof e}var Vr=function(e,r,t,n){var o;e=Br(e)?e:(o=e)?br(o,function(e){return Br(e)?Tr(e):kr(e)}(o)):[],t=t&&!n?function(e){var r=function(e){if(!e)return 0===e?e:0;if(e=function(e){if("number"==typeof e)return e;if(function(e){return"symbol"==typeof e||Ir(e)&&Er.call(e)==lr}(e))return ar;if($r(e)){var r="function"==typeof e.valueOf?e.valueOf():e;e=$r(r)?r+"":r}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(pr,"");var t=vr.test(e);return t||dr.test(e)?mr(e.slice(2),t?2:8):hr.test(e)?ar:+e}(e),e===nr||e===-nr){return(e<0?-1:1)*ir}return e==e?e:0}(e),t=r%1;return r==r?t?r-t:r:0}(t):0;var i=e.length;return t<0&&(t=Ar(i+t,0)),function(e){return"string"==typeof e||!Rr(e)&&Ir(e)&&Er.call(e)==fr}(e)?t<=i&&e.indexOf(r,t)>-1:!!i&&function(e,r,t){if(r!=r)return function(e,r,t,n){for(var o=e.length,i=t+(n?1:-1);n?i--:++i<o;)if(r(e[i],i,e))return i;return-1}(e,gr,t);for(var n=t-1,o=e.length;++n<o;)if(e[n]===r)return n;return-1}(e,r,t)>-1},Nr=Object.prototype.toString;var Mr=function(e){return!0===e||!1===e||function(e){return!!e&&"object"==typeof e}(e)&&"[object Boolean]"==Nr.call(e)},zr=1/0,Dr=17976931348623157e292,qr=NaN,Cr="[object Symbol]",Hr=/^\s+|\s+$/g,Ur=/^[-+]0x[0-9a-f]+$/i,Lr=/^0b[01]+$/i,Gr=/^0o[0-7]+$/i,Kr=parseInt,Fr=Object.prototype.toString;function Jr(e){var r=typeof e;return!!e&&("object"==r||"function"==r)}var Wr=function(e){return"number"==typeof e&&e==function(e){var r=function(e){if(!e)return 0===e?e:0;if(e=function(e){if("number"==typeof e)return e;if(function(e){return"symbol"==typeof e||function(e){return!!e&&"object"==typeof e}(e)&&Fr.call(e)==Cr}(e))return qr;if(Jr(e)){var r="function"==typeof e.valueOf?e.valueOf():e;e=Jr(r)?r+"":r}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(Hr,"");var t=Lr.test(e);return t||Gr.test(e)?Kr(e.slice(2),t?2:8):Ur.test(e)?qr:+e}(e),e===zr||e===-zr){return(e<0?-1:1)*Dr}return e==e?e:0}(e),t=r%1;return r==r?t?r-t:r:0}(e)},Zr=Object.prototype.toString;var Xr=function(e){return"number"==typeof e||function(e){return!!e&&"object"==typeof e}(e)&&"[object Number]"==Zr.call(e)};var Yr=Function.prototype,Qr=Object.prototype,et=Yr.toString,rt=Qr.hasOwnProperty,tt=et.call(Object),nt=Qr.toString,ot=function(e,r){return function(t){return e(r(t))}}(Object.getPrototypeOf,Object);var it=function(e){if(!function(e){return!!e&&"object"==typeof e}(e)||"[object Object]"!=nt.call(e)||function(e){var r=!1;if(null!=e&&"function"!=typeof e.toString)try{r=!!(e+"")}catch(e){}return r}(e))return!1;var r=ot(e);if(null===r)return!0;var t=rt.call(r,"constructor")&&r.constructor;return"function"==typeof t&&t instanceof t&&et.call(t)==tt},at=Object.prototype.toString,st=Array.isArray;var ut=function(e){return"string"==typeof e||!st(e)&&function(e){return!!e&&"object"==typeof e}(e)&&"[object String]"==at.call(e)},ct="Expected a function",ft=1/0,lt=17976931348623157e292,pt=NaN,ht="[object Symbol]",vt=/^\s+|\s+$/g,dt=/^[-+]0x[0-9a-f]+$/i,yt=/^0b[01]+$/i,mt=/^0o[0-7]+$/i,gt=parseInt,bt=Object.prototype.toString;function wt(e,r){var t;if("function"!=typeof r)throw new TypeError(ct);return e=function(e){var r=function(e){if(!e)return 0===e?e:0;if(e=function(e){if("number"==typeof e)return e;if(function(e){return"symbol"==typeof e||function(e){return!!e&&"object"==typeof e}(e)&&bt.call(e)==ht}(e))return pt;if(jt(e)){var r="function"==typeof e.valueOf?e.valueOf():e;e=jt(r)?r+"":r}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(vt,"");var t=yt.test(e);return t||mt.test(e)?gt(e.slice(2),t?2:8):dt.test(e)?pt:+e}(e),e===ft||e===-ft){return(e<0?-1:1)*lt}return e==e?e:0}(e),t=r%1;return r==r?t?r-t:r:0}(e),function(){return--e>0&&(t=r.apply(this,arguments)),e<=1&&(r=void 0),t}}function jt(e){var r=typeof e;return!!e&&("object"==r||"function"==r)}var St=function(e){return wt(2,e)},_t=Ge,Et=a,xt=Vr,Ot=Mr,At=Wr,Tt=Xr,kt=it,Pt=ut,Rt=St,Bt=["RS256","RS384","RS512","ES256","ES384","ES512","HS256","HS384","HS512","none"];Fe&&Bt.splice(3,0,"PS256","PS384","PS512");var $t={expiresIn:{isValid:function(e){return At(e)||Pt(e)&&e},message:'"expiresIn" should be a number of seconds or string representing a timespan'},notBefore:{isValid:function(e){return At(e)||Pt(e)&&e},message:'"notBefore" should be a number of seconds or string representing a timespan'},audience:{isValid:function(e){return Pt(e)||Array.isArray(e)},message:'"audience" must be a string or array'},algorithm:{isValid:xt.bind(null,Bt),message:'"algorithm" must be a valid string enum value'},header:{isValid:kt,message:'"header" must be an object'},encoding:{isValid:Pt,message:'"encoding" must be a string'},issuer:{isValid:Pt,message:'"issuer" must be a string'},subject:{isValid:Pt,message:'"subject" must be a string'},jwtid:{isValid:Pt,message:'"jwtid" must be a string'},noTimestamp:{isValid:Ot,message:'"noTimestamp" must be a boolean'},keyid:{isValid:Pt,message:'"keyid" must be a string'},mutatePayload:{isValid:Ot,message:'"mutatePayload" must be a boolean'}},It={iat:{isValid:Tt,message:'"iat" should be a number of seconds'},exp:{isValid:Tt,message:'"exp" should be a number of seconds'},nbf:{isValid:Tt,message:'"nbf" should be a number of seconds'}};function Vt(e,r,t,n){if(!kt(t))throw new Error('Expected "'+n+'" to be a plain object.');Object.keys(t).forEach((function(o){var i=e[o];if(i){if(!i.isValid(t[o]))throw new Error(i.message)}else if(!r)throw new Error('"'+o+'" is not allowed in "'+n+'"')}))}var Nt={audience:"aud",issuer:"iss",subject:"sub",jwtid:"jti"},Mt=["expiresIn","notBefore","noTimestamp","audience","issuer","subject","jwtid"],zt={decode:Te,verify:function(e,r,t,n){var o;if("function"!=typeof t||n||(n=t,t={}),t||(t={}),t=Object.assign({},t),o=n||function(e,r){if(e)throw e;return r},t.clockTimestamp&&"number"!=typeof t.clockTimestamp)return o(new Je("clockTimestamp must be a number"));if(void 0!==t.nonce&&("string"!=typeof t.nonce||""===t.nonce.trim()))return o(new Je("nonce must be a non-empty string"));var i=t.clockTimestamp||Math.floor(Date.now()/1e3);if(!e)return o(new Je("jwt must be provided"));if("string"!=typeof e)return o(new Je("jwt must be a string"));var a,s=e.split(".");if(3!==s.length)return o(new Je("jwt malformed"));try{a=Xe(e,{complete:!0})}catch(e){return o(e)}if(!a)return o(new Je("invalid token"));var u,c=a.header;if("function"==typeof r){if(!n)return o(new Je("verify must be called asynchronous if secret or public key is provided as a callback"));u=r}else u=function(e,t){return t(null,r)};return u(c,(function(r,n){if(r)return o(new Je("error in secret or public key callback: "+r.message));var u,f=""!==s[2].trim();if(!f&&n)return o(new Je("jwt signature is required"));if(f&&!n)return o(new Je("secret or public key must be provided"));if(f||t.algorithms||(t.algorithms=["none"]),t.algorithms||(t.algorithms=~n.toString().indexOf("BEGIN CERTIFICATE")||~n.toString().indexOf("BEGIN PUBLIC KEY")?er:~n.toString().indexOf("BEGIN RSA PUBLIC KEY")?rr:tr),!~t.algorithms.indexOf(a.header.alg))return o(new Je("invalid algorithm"));try{u=Qe.verify(e,a.header.alg,n)}catch(e){return o(e)}if(!u)return o(new Je("invalid signature"));var l=a.payload;if(void 0!==l.nbf&&!t.ignoreNotBefore){if("number"!=typeof l.nbf)return o(new Je("invalid nbf value"));if(l.nbf>i+(t.clockTolerance||0))return o(new We("jwt not active",new Date(1e3*l.nbf)))}if(void 0!==l.exp&&!t.ignoreExpiration){if("number"!=typeof l.exp)return o(new Je("invalid exp value"));if(i>=l.exp+(t.clockTolerance||0))return o(new Ze("jwt expired",new Date(1e3*l.exp)))}if(t.audience){var p=Array.isArray(t.audience)?t.audience:[t.audience];if(!(Array.isArray(l.aud)?l.aud:[l.aud]).some((function(e){return p.some((function(r){return r instanceof RegExp?r.test(e):r===e}))})))return o(new Je("jwt audience invalid. expected: "+p.join(" or ")))}if(t.issuer&&("string"==typeof t.issuer&&l.iss!==t.issuer||Array.isArray(t.issuer)&&-1===t.issuer.indexOf(l.iss)))return o(new Je("jwt issuer invalid. expected: "+t.issuer));if(t.subject&&l.sub!==t.subject)return o(new Je("jwt subject invalid. expected: "+t.subject));if(t.jwtid&&l.jti!==t.jwtid)return o(new Je("jwt jwtid invalid. expected: "+t.jwtid));if(t.nonce&&l.nonce!==t.nonce)return o(new Je("jwt nonce invalid. expected: "+t.nonce));if(t.maxAge){if("number"!=typeof l.iat)return o(new Je("iat required when maxAge is specified"));var h=Ye(t.maxAge,l.iat);if(void 0===h)return o(new Je('"maxAge" should be a number of seconds or string representing a timespan eg: "1d", "20h", 60'));if(i>=h+(t.clockTolerance||0))return o(new Ze("maxAge exceeded",new Date(1e3*h)))}if(!0===t.complete){var v=a.signature;return o(null,{header:c,payload:l,signature:v})}return o(null,l)}))},sign:function(e,r,t,n){"function"==typeof t?(n=t,t={}):t=t||{};var o="object"==typeof e&&!Buffer.isBuffer(e),i=Object.assign({alg:t.algorithm||"HS256",typ:o?"JWT":void 0,kid:t.keyid},t.header);function a(e){if(n)return n(e);throw e}if(!r&&"none"!==t.algorithm)return a(new Error("secretOrPrivateKey must have a value"));if(void 0===e)return a(new Error("payload is required"));if(o){try{!function(e){Vt(It,!0,e,"payload")}(e)}catch(e){return a(e)}t.mutatePayload||(e=Object.assign({},e))}else{var s=Mt.filter((function(e){return void 0!==t[e]}));if(s.length>0)return a(new Error("invalid "+s.join(",")+" option for "+typeof e+" payload"))}if(void 0!==e.exp&&void 0!==t.expiresIn)return a(new Error('Bad "options.expiresIn" option the payload already has an "exp" property.'));if(void 0!==e.nbf&&void 0!==t.notBefore)return a(new Error('Bad "options.notBefore" option the payload already has an "nbf" property.'));try{!function(e){Vt($t,!1,e,"options")}(t)}catch(e){return a(e)}var u=e.iat||Math.floor(Date.now()/1e3);if(t.noTimestamp?delete e.iat:o&&(e.iat=u),void 0!==t.notBefore){try{e.nbf=_t(t.notBefore,u)}catch(e){return a(e)}if(void 0===e.nbf)return a(new Error('"notBefore" should be a number of seconds or string representing a timespan eg: "1d", "20h", 60'))}if(void 0!==t.expiresIn&&"object"==typeof e){try{e.exp=_t(t.expiresIn,u)}catch(e){return a(e)}if(void 0===e.exp)return a(new Error('"expiresIn" should be a number of seconds or string representing a timespan eg: "1d", "20h", 60'))}Object.keys(Nt).forEach((function(r){var n=Nt[r];if(void 0!==t[r]){if(void 0!==e[n])return a(new Error('Bad "options.'+r+'" option. The payload already has an "'+n+'" property.'));e[n]=t[r]}}));var c=t.encoding||"utf8";if("function"!=typeof n)return Et.sign({header:i,payload:e,secret:r,encoding:c});n=n&&Rt(n),Et.createSign({header:i,privateKey:r,payload:e,encoding:c}).once("error",n).once("done",(function(e){n(null,e)}))},JsonWebTokenError:Pe,NotBeforeError:$e,TokenExpiredError:Ne},Dt={exports:{}};!function(e,r){var t="__lodash_hash_undefined__",n=9007199254740991,i="[object Arguments]",a="[object AsyncFunction]",s="[object Function]",u="[object GeneratorFunction]",c="[object Null]",f="[object Object]",l="[object Proxy]",p="[object Undefined]",h=/^\[object .+?Constructor\]$/,v=/^(?:0|[1-9]\d*)$/,d={};d["[object Float32Array]"]=d["[object Float64Array]"]=d["[object Int8Array]"]=d["[object Int16Array]"]=d["[object Int32Array]"]=d["[object Uint8Array]"]=d["[object Uint8ClampedArray]"]=d["[object Uint16Array]"]=d["[object Uint32Array]"]=!0,d[i]=d["[object Array]"]=d["[object ArrayBuffer]"]=d["[object Boolean]"]=d["[object DataView]"]=d["[object Date]"]=d["[object Error]"]=d[s]=d["[object Map]"]=d["[object Number]"]=d[f]=d["[object RegExp]"]=d["[object Set]"]=d["[object String]"]=d["[object WeakMap]"]=!1;var y="object"==typeof o&&o&&o.Object===Object&&o,m="object"==typeof self&&self&&self.Object===Object&&self,g=y||m||Function("return this")(),b=r&&!r.nodeType&&r,w=b&&e&&!e.nodeType&&e,j=w&&w.exports===b,S=j&&y.process,_=function(){try{var e=w&&w.require&&w.require("util").types;return e||S&&S.binding&&S.binding("util")}catch(e){}}(),E=_&&_.isTypedArray;var x,O=Array.prototype,A=Function.prototype,T=Object.prototype,k=g["__core-js_shared__"],P=A.toString,R=T.hasOwnProperty,B=(x=/[^.]+$/.exec(k&&k.keys&&k.keys.IE_PROTO||""))?"Symbol(src)_1."+x:"",$=T.toString,I=P.call(Object),V=RegExp("^"+P.call(R).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),N=j?g.Buffer:void 0,M=g.Symbol,z=g.Uint8Array,D=N?N.allocUnsafe:void 0,q=function(e,r){return function(t){return e(r(t))}}(Object.getPrototypeOf,Object),C=Object.create,H=T.propertyIsEnumerable,U=O.splice,L=M?M.toStringTag:void 0,G=function(){try{var e=me(Object,"defineProperty");return e({},"",{}),e}catch(e){}}(),K=N?N.isBuffer:void 0,F=Math.max,J=Date.now,W=me(g,"Map"),Z=me(Object,"create"),X=function(){function e(){}return function(r){if(!ke(r))return{};if(C)return C(r);e.prototype=r;var t=new e;return e.prototype=void 0,t}}();function Y(e){var r=-1,t=null==e?0:e.length;for(this.clear();++r<t;){var n=e[r];this.set(n[0],n[1])}}function Q(e){var r=-1,t=null==e?0:e.length;for(this.clear();++r<t;){var n=e[r];this.set(n[0],n[1])}}function ee(e){var r=-1,t=null==e?0:e.length;for(this.clear();++r<t;){var n=e[r];this.set(n[0],n[1])}}function re(e){var r=this.__data__=new Q(e);this.size=r.size}function te(e,r){var t=Ee(e),n=!t&&_e(e),o=!t&&!n&&Oe(e),i=!t&&!n&&!o&&Re(e),a=t||n||o||i,s=a?function(e,r){for(var t=-1,n=Array(e);++t<e;)n[t]=r(t);return n}(e.length,String):[],u=s.length;for(var c in e)!r&&!R.call(e,c)||a&&("length"==c||o&&("offset"==c||"parent"==c)||i&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||ge(c,u))||s.push(c);return s}function ne(e,r,t){(void 0!==t&&!Se(e[r],t)||void 0===t&&!(r in e))&&ae(e,r,t)}function oe(e,r,t){var n=e[r];R.call(e,r)&&Se(n,t)&&(void 0!==t||r in e)||ae(e,r,t)}function ie(e,r){for(var t=e.length;t--;)if(Se(e[t][0],r))return t;return-1}function ae(e,r,t){"__proto__"==r&&G?G(e,r,{configurable:!0,enumerable:!0,value:t,writable:!0}):e[r]=t}Y.prototype.clear=function(){this.__data__=Z?Z(null):{},this.size=0},Y.prototype.delete=function(e){var r=this.has(e)&&delete this.__data__[e];return this.size-=r?1:0,r},Y.prototype.get=function(e){var r=this.__data__;if(Z){var n=r[e];return n===t?void 0:n}return R.call(r,e)?r[e]:void 0},Y.prototype.has=function(e){var r=this.__data__;return Z?void 0!==r[e]:R.call(r,e)},Y.prototype.set=function(e,r){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=Z&&void 0===r?t:r,this},Q.prototype.clear=function(){this.__data__=[],this.size=0},Q.prototype.delete=function(e){var r=this.__data__,t=ie(r,e);return!(t<0)&&(t==r.length-1?r.pop():U.call(r,t,1),--this.size,!0)},Q.prototype.get=function(e){var r=this.__data__,t=ie(r,e);return t<0?void 0:r[t][1]},Q.prototype.has=function(e){return ie(this.__data__,e)>-1},Q.prototype.set=function(e,r){var t=this.__data__,n=ie(t,e);return n<0?(++this.size,t.push([e,r])):t[n][1]=r,this},ee.prototype.clear=function(){this.size=0,this.__data__={hash:new Y,map:new(W||Q),string:new Y}},ee.prototype.delete=function(e){var r=ye(this,e).delete(e);return this.size-=r?1:0,r},ee.prototype.get=function(e){return ye(this,e).get(e)},ee.prototype.has=function(e){return ye(this,e).has(e)},ee.prototype.set=function(e,r){var t=ye(this,e),n=t.size;return t.set(e,r),this.size+=t.size==n?0:1,this},re.prototype.clear=function(){this.__data__=new Q,this.size=0},re.prototype.delete=function(e){var r=this.__data__,t=r.delete(e);return this.size=r.size,t},re.prototype.get=function(e){return this.__data__.get(e)},re.prototype.has=function(e){return this.__data__.has(e)},re.prototype.set=function(e,r){var t=this.__data__;if(t instanceof Q){var n=t.__data__;if(!W||n.length<199)return n.push([e,r]),this.size=++t.size,this;t=this.__data__=new ee(n)}return t.set(e,r),this.size=t.size,this};var se,ue=function(e,r,t){for(var n=-1,o=Object(e),i=t(e),a=i.length;a--;){var s=i[se?a:++n];if(!1===r(o[s],s,o))break}return e};function ce(e){return null==e?void 0===e?p:c:L&&L in Object(e)?function(e){var r=R.call(e,L),t=e[L];try{e[L]=void 0;var n=!0}catch(e){}var o=$.call(e);n&&(r?e[L]=t:delete e[L]);return o}(e):function(e){return $.call(e)}(e)}function fe(e){return Pe(e)&&ce(e)==i}function le(e){return!(!ke(e)||function(e){return!!B&&B in e}(e))&&(Ae(e)?V:h).test(function(e){if(null!=e){try{return P.call(e)}catch(e){}try{return e+""}catch(e){}}return""}(e))}function pe(e){if(!ke(e))return function(e){var r=[];if(null!=e)for(var t in Object(e))r.push(t);return r}(e);var r=be(e),t=[];for(var n in e)("constructor"!=n||!r&&R.call(e,n))&&t.push(n);return t}function he(e,r,t,n,o){e!==r&&ue(r,(function(i,a){if(o||(o=new re),ke(i))!function(e,r,t,n,o,i,a){var s=we(e,t),u=we(r,t),c=a.get(u);if(c)return void ne(e,t,c);var l=i?i(s,u,t+"",e,r,a):void 0,p=void 0===l;if(p){var h=Ee(u),v=!h&&Oe(u),d=!h&&!v&&Re(u);l=u,h||v||d?Ee(s)?l=s:Pe(w=s)&&xe(w)?l=function(e,r){var t=-1,n=e.length;r||(r=Array(n));for(;++t<n;)r[t]=e[t];return r}(s):v?(p=!1,l=function(e,r){if(r)return e.slice();var t=e.length,n=D?D(t):new e.constructor(t);return e.copy(n),n}(u,!0)):d?(p=!1,y=u,m=!0?(g=y.buffer,b=new g.constructor(g.byteLength),new z(b).set(new z(g)),b):y.buffer,l=new y.constructor(m,y.byteOffset,y.length)):l=[]:function(e){if(!Pe(e)||ce(e)!=f)return!1;var r=q(e);if(null===r)return!0;var t=R.call(r,"constructor")&&r.constructor;return"function"==typeof t&&t instanceof t&&P.call(t)==I}(u)||_e(u)?(l=s,_e(s)?l=function(e){return function(e,r,t,n){var o=!t;t||(t={});var i=-1,a=r.length;for(;++i<a;){var s=r[i],u=n?n(t[s],e[s],s,t,e):void 0;void 0===u&&(u=e[s]),o?ae(t,s,u):oe(t,s,u)}return t}(e,Be(e))}(s):ke(s)&&!Ae(s)||(l=function(e){return"function"!=typeof e.constructor||be(e)?{}:X(q(e))}(u))):p=!1}var y,m,g,b;var w;p&&(a.set(u,l),o(l,u,n,i,a),a.delete(u));ne(e,t,l)}(e,r,a,t,he,n,o);else{var s=n?n(we(e,a),i,a+"",e,r,o):void 0;void 0===s&&(s=i),ne(e,a,s)}}),Be)}function ve(e,r){return je(function(e,r,t){return r=F(void 0===r?e.length-1:r,0),function(){for(var n=arguments,o=-1,i=F(n.length-r,0),a=Array(i);++o<i;)a[o]=n[r+o];o=-1;for(var s=Array(r+1);++o<r;)s[o]=n[o];return s[r]=t(a),function(e,r,t){switch(t.length){case 0:return e.call(r);case 1:return e.call(r,t[0]);case 2:return e.call(r,t[0],t[1]);case 3:return e.call(r,t[0],t[1],t[2])}return e.apply(r,t)}(e,this,s)}}(e,r,Ve),e+"")}var de=G?function(e,r){return G(e,"toString",{configurable:!0,enumerable:!1,value:(t=r,function(){return t}),writable:!0});var t}:Ve;function ye(e,r){var t,n,o=e.__data__;return("string"==(n=typeof(t=r))||"number"==n||"symbol"==n||"boolean"==n?"__proto__"!==t:null===t)?o["string"==typeof r?"string":"hash"]:o.map}function me(e,r){var t=function(e,r){return null==e?void 0:e[r]}(e,r);return le(t)?t:void 0}function ge(e,r){var t=typeof e;return!!(r=null==r?n:r)&&("number"==t||"symbol"!=t&&v.test(e))&&e>-1&&e%1==0&&e<r}function be(e){var r=e&&e.constructor;return e===("function"==typeof r&&r.prototype||T)}function we(e,r){if(("constructor"!==r||"function"!=typeof e[r])&&"__proto__"!=r)return e[r]}var je=function(e){var r=0,t=0;return function(){var n=J(),o=16-(n-t);if(t=n,o>0){if(++r>=800)return arguments[0]}else r=0;return e.apply(void 0,arguments)}}(de);function Se(e,r){return e===r||e!=e&&r!=r}var _e=fe(function(){return arguments}())?fe:function(e){return Pe(e)&&R.call(e,"callee")&&!H.call(e,"callee")},Ee=Array.isArray;function xe(e){return null!=e&&Te(e.length)&&!Ae(e)}var Oe=K||function(){return!1};function Ae(e){if(!ke(e))return!1;var r=ce(e);return r==s||r==u||r==a||r==l}function Te(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=n}function ke(e){var r=typeof e;return null!=e&&("object"==r||"function"==r)}function Pe(e){return null!=e&&"object"==typeof e}var Re=E?function(e){return function(r){return e(r)}}(E):function(e){return Pe(e)&&Te(e.length)&&!!d[ce(e)]};function Be(e){return xe(e)?te(e,!0):pe(e)}var $e,Ie=($e=function(e,r,t){he(e,r,t)},ve((function(e,r){var t=-1,n=r.length,o=n>1?r[n-1]:void 0,i=n>2?r[2]:void 0;for(o=$e.length>3&&"function"==typeof o?(n--,o):void 0,i&&function(e,r,t){if(!ke(t))return!1;var n=typeof r;return!!("number"==n?xe(t)&&ge(r,t.length):"string"==n&&r in t)&&Se(t[r],e)}(r[0],r[1],i)&&(o=n<3?void 0:o,n=1),e=Object(e);++t<n;){var a=r[t];a&&$e(e,a,t,o)}return e})));function Ve(e){return e}e.exports=Ie}(Dt,Dt.exports);var qt={jwtVerify:zt.verify,merge:i(Dt.exports)};module.exports=qt;
