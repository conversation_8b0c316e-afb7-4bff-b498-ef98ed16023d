// 云对象教程：https://uniapp.dcloud.net.cn/uniCloud/cloud-obj
const { log } = require('console')
const uniID = require('uni-id-common')

const getFlow = async () =>{
	  // 调用登录接口
	  const loginRes = await uniCloud.httpclient.request(`https://cc05.fastlink.lat/auth/login`, {
	    method: 'POST',
	    data: {
	      email: '<EMAIL>',
	      passwd: 'zzz111222',
	    },
	    contentType: 'json',
	    dataType: 'json',
	  })
	  
	  // 获取 set-cookie 头
	  const setCookieHeader = loginRes.headers['set-cookie']
	  if (!setCookieHeader) {
	    console.log('找不到cookie')
	    return null
	  }
	  
	  // 将 set-cookie 头转换为字符串
	  const cookies = setCookieHeader.join('; ')
	  
	  const res = await uniCloud.httpclient.request(`https://cc05.fastlink.lat/user`, {
	    method: 'GET',
	    //   contentType: 'json',
	    //   dataType: 'json',
	    //   timeout: 30000,
	    headers: {
	  	Cookie: cookies,
	    },
	  })
	  const pageContent = res.data.toString('utf-8')
	  
	  // 使用正则表达式匹配所需的数据
	  const unusedTrafficRegex = /"Unused_Traffic",\s*"(\d+(\.\d+)?GB)"/
	  // const usedTodayRegex = /今日已用:\s*(\d+(\.\d+)?GB)/
	  
	  const unusedTrafficMatch = pageContent.match(unusedTrafficRegex)
	  // const usedTodayMatch = pageContent.match(usedTodayRegex)
	  
	  const result = {}
	  
	  if (unusedTrafficMatch) {
	    result.usedFlow = unusedTrafficMatch[1]
	    console.log(`Unused Traffic: ${result.usedFlow}`)
	  } else {
	    console.log('No match found for Unused Traffic')
	  }
	  
	  // if (usedTodayMatch) {
	  //   result.usedToday = usedTodayMatch[1]
	  //   console.log(`Used Today: ${result.usedToday}`)
	  // } else {
	  //   console.log('No match found for Used Today')
	  // }
	  
	  return result.usedFlow
  }
 
const getLocalTime = () =>{
 	const utcDate = new Date()
 	utcDate.setUTCHours(utcDate.getUTCHours() + 8);
 	return utcDate
 }

// jsdoc 语法提示教程：https://ask.dcloud.net.cn/docs/#//ask.dcloud.net.cn/article/129
module.exports = {
  _before: function () {
    // 通用预处理器
    const clientInfo = this.getClientInfo()
    this.uniID = uniID.createInstance({
      // 创建 uni-id 实例，其上方法同 uniID
      clientInfo,
    })
  },
 /**
   * 
   */
  async get() {
	  try{
	  	// 获取使用的流量
	  	const flow = await getFlow()
	  	
	  	const db = uniCloud.database()
	  	const time = new Date().getTime()
	  	await db
	  	  .collection('flow')
	  	  .add({
	  	    time,
	  		flow: 20
	  	  })
	  	console.log('cccccccc')
		return flow
	  }catch(e){
	  	//TODO handle the exception
		console.log(e)
	  }
	
	
	
  },
}
