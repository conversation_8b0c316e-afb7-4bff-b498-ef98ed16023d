<template>
  <view class="sidebar-container">
    <view class="logo-container" @click="toggleCollapse">
      <image src="/static/logo.png" class="logo" />
    </view>
    <view class="menu">
      <view
        v-for="item in menuItems"
        :key="item.id"
        class="menu-item"
        :class="{ active: activeMenu === item.id }"
        @click="handleTabChange(item)"
      >
        <i :class="item.icon"></i>
        <view class="tooltip">{{ item.name }}</view>
      </view>
    </view>
    <view class="footer-menu">
      <view
        v-for="item in footerItems"
        :key="item.id"
        class="menu-item"
        :class="{ rotating: isRefreshing && item.id === 'sync' }"
        @click="handleTabChange(item)"
      >
        <i :class="item.icon"></i>
        <view class="tooltip">{{ item.name }}</view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { router } from '@/utils/tools' // 引入路由工具

const isRefreshing = ref(false)

const emit = defineEmits(['menu-change', 'toggle'])
const activeMenu = ref('okr')

const menuItems = ref([
  { id: 'okr', name: 'OKR', icon: 'fas fa-bullseye', collapsed: false, route: '/pages/okr/okrEdit' },
  { id: 'diary', name: '日记', icon: 'fas fa-book', collapsed: false, route: '/pages/diary/index' },
  { id: 'speak', name: '表达', icon: 'fas fa-comment-dots', collapsed: false, route: '/pages/speak/index' },
])

const footerItems = ref([
  { id: 'sync', name: '同步', icon: 'fas fa-sync-alt', collapsed: false, route: '' },
  { id: 'setting', name: '设置', icon: 'fas fa-cog', collapsed: true, route: '/pages/setting/setting' },
])

const toggleCollapse = () => {
  const allItems = [...menuItems.value, ...footerItems.value]
  const currentItem = allItems.find((item) => item.id === activeMenu.value)
  if (currentItem) {
    currentItem.collapsed = !currentItem.collapsed
    emit('toggle', currentItem.collapsed)
  }
}

const handleTabChange = (item) => {
  if (item.id === 'sync') {
    onRefresh()
    return
  }

  activeMenu.value = item.id
  emit('menu-change', { id: item.id, collapsed: item.collapsed })

  if (item.route) {
    router.push(item.route)
  }
}

const onRefresh = async () => {
  isRefreshing.value = true
  // syncToServer() is likely a global function or defined elsewhere
  // as it's not imported in the original file.
  // We assume it's available in the execution context.
  if (typeof syncToServer === 'function') {
    await syncToServer()
  }
  setTimeout(() => {
    isRefreshing.value = false
  }, 3000)
}
</script>

<style scoped>
.sidebar-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #fff;
  border-right: 1px solid #e8e8e8;
  align-items: center;
  width: 60px;
}
.logo-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 14px 0;
  width: 100%;
  border-bottom: 1px solid #e8e8e8;
}
.logo {
  width: 32px;
  height: 32px;
}
.menu {
  flex-grow: 1;
  padding: 12px 8px;
  width: 100%;
  box-sizing: border-box;
}
.footer-menu {
  padding: 12px 8px;
  width: 100%;
  box-sizing: border-box;
  border-top: 1px solid #e8e8e8;
}
.menu-item {
  position: relative; /* 为 tooltip 定位 */
  display: flex;
  justify-content: center;
  align-items: center;
  height: 44px;
  width: 44px;
  margin: 0 auto 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  color: #606266;
}
.menu-item:last-child {
  margin-bottom: 0;
}
.menu-item:hover {
  background-color: #f2f6fc;
  color: var(--color-primary);
}
.menu-item.active {
  background-color: var(--color-primary);
  color: #fff;
  box-shadow: 0 4px 10px -2px rgba(var(--color-primary-rgb), 0.4);
  border-right: none;
}
.menu-item i {
  font-size: 20px;
  width: 24px;
  text-align: center;
  transition: color 0.2s ease-in-out;
}

.tooltip {
  position: absolute;
  left: 56px; /* 将提示框定位在侧边栏右侧 */
  top: 50%;
  transform: translateY(-50%);
  background-color: #303133;
  color: #fff;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  white-space: nowrap;
  pointer-events: none; /* 确保 tooltip 不干扰鼠标事件 */
  visibility: hidden;
  opacity: 0;
  /* HIDE transition: 鼠标移开时，快速消失 */
  transition: opacity 0.2s ease, visibility 0s linear 0.2s;
  z-index: 10;
}

.tooltip::after {
  content: '';
  position: absolute;
  top: 50%;
  right: 100%; /* 将箭头定位到 tooltip 的左边 */
  margin-top: -6px; /* 垂直居中箭头 */
  border-width: 6px;
  border-style: solid;
  border-color: transparent #303133 transparent transparent;
}

.menu-item:hover .tooltip {
  visibility: visible;
  opacity: 1;
  /* SHOW transition: 鼠标悬浮时，延迟 500 毫秒显示 */
  transition: opacity 0.2s ease 500ms, visibility 0s linear 500ms;
}

/* 点击后（即 active 状态），悬浮时不再显示 tooltip */
.menu-item.active:hover .tooltip {
  visibility: hidden;
  opacity: 0;
  transition: none;
}

.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
