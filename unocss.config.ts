import presetWeapp from 'unocss-preset-weapp'
import { transformerAttributify, transformerClass } from 'unocss-preset-weapp/transformer'
import { defineConfig } from 'unocss'
import { transformerDirectives } from 'unocss'
import presetIcons from '@unocss/preset-icons'

export default defineConfig({
  presets: [
    // docs: https://github.com/MellowCo/unocss-preset-weapp
    presetWeapp({
      whRpx: true,
    }),
    presetIcons({}),
  ],
  shortcuts: [
    {
      'border-base': 'border border-gray-500_10',
      'flex-center': 'flex justify-center items-center',
      'page-margin': 'mx-4',
      'z-panel': 'p4 mb-4 bg-white rounded-2',
      rr: 'rounded-2',
      'overflow-ell': 'overflow-hidden text-ellipsis whitespace-nowrap',
    },
  ],
  rules: [
    // 添加自定义规则
    [/^bb-(.+)$/, ([, color]) => ({ 'border-bottom': `1px solid ${color}` })],
    [/^bt-(.+)$/, ([, color]) => ({ 'border-top': `1px solid ${color}` })],
    [/^wh-(.+)$/, ([, rpx]) => ({ width: `${rpx}rpx`, height: `${rpx}rpx` })],
    [/^bg-url(.+)$/, ([, url]) => ({ 'background-image': `url${url}` })],
    [/^t-(.+)$/, ([, rpx]) => ({ 'font-size': `${rpx}rpx` })],
    [/^r-(.+)$/, ([, rpx]) => ({ 'border-radius': `${rpx}rpx` })],
    [/^r50$/, ([, rpx]) => ({ 'border-radius': `50%` })],
  ],
  transformers: [
    // https://github.com/unocss/unocss/tree/main/packages/transformer-directives
    transformerDirectives(),
    // docs: https://github.com/MellowCo/unocss-preset-weapp/tree/main/src/transformer/transformerAttributify
    transformerAttributify({
      nonValuedAttribute: true, // 以支持在小程序中使用 attributify mode, 例如 <button bg="blue-400"></button>
    }),

    // docs: https://github.com/MellowCo/unocss-preset-weapp/tree/main/src/transformer/transformerClass
    transformerClass(),
  ],
})
