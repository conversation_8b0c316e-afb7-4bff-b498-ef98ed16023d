// 文档教程: https://uniapp.dcloud.net.cn/uniCloud/schema
{
	"bsonType": "object",
	"required": ["title", "type", "create_date", "user_id"],
	"permission": {
		"read": "doc.user_id == auth.uid",
		"create": true,
		"update": "doc.user_id == auth.uid",
		"delete": "doc.user_id == auth.uid"
	},
	"properties": {
		"_id": {
			"description": "ID，系统自动生成"
		},
		"title": {
			"bsonType": "string",
			"title": "标签名字",
			"trim": "both"
		},
		"type": {
			"bsonType": "int",
			"title": "习惯类型",
			"description": "0 按天 1 按量",
			"enum": [0, 1]
		},
		"label_id": {
			"bsonType": "string",
			"title": "习惯标签",
			"description": "分类标签",
			"foreignKey": "habit-label._id"
		},
		"enable": {
			"bsonType": "int",
			"title": "启用状态",
			"description": "类型 0：禁用 1：启用",
			"enum": [0, 1]
		},
		"slug": {
			"bsonType": "string",
			"title": "唯一标识符",
			"description": "旧版使用的唯一标识符"
		},
		"create_date": {
			"bsonType": "timestamp",
			"title": "创建时间",
			"forceDefaultValue": {
				"$env": "now"
			}
		},
		"update_date": {
			"bsonType": "timestamp",
			"title": "更新时间",
			"defaultValue": {
				"$env": "now"
			}
		},
		"user_id": {
			"bsonType": "string",
			"title": "用户 id",
			"description": "旧版使用的唯一标识符",
			"forceDefaultValue": {
				"$env": "uid"
			}
		}
	}
}