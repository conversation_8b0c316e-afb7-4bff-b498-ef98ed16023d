// 文档教程: https://uniapp.dcloud.net.cn/uniCloud/schema
{
	"bsonType": "object",
	"required": [],
	"permission": {
		"read": true,
		"create": true,
		"update": true,
		"delete": true
	},
	"properties": {
		"_id": {
			"description": "ID，系统自动生成"
		},
		"title": {
			"bsonType": "string",
			"title": "标题",
			"trim": "both"
		},
		"white_id": {
			"bsonType": "string",
			"title": "白名单 id",
			"description": "不鸭白名单 id"
		},
		"group_id": {
			"bsonType": "string",
			"title": "id",
			"description": "群的唯一标识 id"
		},
		"menu_list": {
			"bsonType": "array",
			"title": "菜单列表"
		},
		"menu_date": {
			"bsonType": "timestamp",
			"title": "点菜日期"
		},
		"menu_done": {
			"bsonType": "array",
			"title": "已点菜单"
		},
		"chatlog_list": {
			"bsonType": "array",
			"title": "聊天记录"
		},
		"chatlog_id": {
			"bsonType": "string",
			"title": "聊天记录 id"
		},
		"update_date": {
			"bsonType": "timestamp",
			"title": "最后提问时间",
			"defaultValue": {
				"$env": "now"
			}
		},
		"create_date": {
			"bsonType": "timestamp",
			"title": "创建时间",
			"forceDefaultValue": {
				"$env": "now"
			}
		}
	}
}