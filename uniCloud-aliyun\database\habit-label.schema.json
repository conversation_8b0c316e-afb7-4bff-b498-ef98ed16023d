// 文档教程: https://uniapp.dcloud.net.cn/uniCloud/schema
{
	"bsonType": "object",
	"required": ["title", "type", "create_date"],
	"permission": {
		"read": "doc.user_id == auth.uid",
		"create": true,
		"update": "doc.user_id == auth.uid",
		"delete": "doc.user_id == auth.uid"
	},
	"properties": {
		"_id": {
			"description": "ID，系统自动生成"
		},
		"title": {
			"bsonType": "string",
			"title": "习惯类别",
			"trim": "both"
		},
		"create_date": {
			"bsonType": "timestamp",
			"title": "创建时间",
			"forceDefaultValue": {
				"$env": "now"
			}
		},
		"update_date": {
			"bsonType": "timestamp",
			"title": "更新时间",
			"defaultValue": {
				"$env": "now"
			}
		},
		"user_id": {
			"bsonType": "string",
			"title": "用户 id",
			"description": "旧版使用的唯一标识符",
			"forceDefaultValue": {
				"$env": "uid"
			}
		}
	}
}