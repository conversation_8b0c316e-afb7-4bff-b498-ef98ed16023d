// 云对象教程：https://uniapp.dcloud.net.cn/uniCloud/cloud-obj
const { log } = require('console')
const COS = require('cos-nodejs-sdk-v5')
// jsdoc 语法提示教程：https://ask.dcloud.net.cn/docs/#//ask.dcloud.net.cn/article/129
const cos = new COS({
  SecretId: 'AKIDkUOEuK8l9HBrNhu0wd5JLAmBTXNLOKD6',
  SecretKey: 'lHeprlTAh4MRVgnkuZ6ZuBKH4iL2hKxQ',
})
const params = {
  Bucket: 'obsidian-sync-1257140447', // 必须
  Region: 'ap-guangzhou',
}

module.exports = {
  _before: function () {},
  /**
   * 创建 MD 笔记
   * @param {string} content
   * @param {string} fileName
   */
  async createNote() {
    const httpInfo = this.getHttpInfo()

    let { content, fileName, tableList, t } = JSON.parse(httpInfo.body)
    try {
      const Key = fileName
      await cos.putObject({
        ...params,
        Key,
        Body: Buffer.from(content),
        ContentType: 'text/markdown; charset=utf-8',
      })
      return {
        code: 200,
        url: `https://${params.Bucket}.cos.${params.Region}.myqcloud.com/${Key}`,
      }
    } catch (err) {
      console.error(err)
      return {
        code: 400,
        message: '文件创建失败',
      }
    }
  },
}
