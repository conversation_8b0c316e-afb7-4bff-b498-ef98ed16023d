// 文档教程：https://uniapp.dcloud.net.cn/uniCloud/schema
{
  "bsonType": "object",
  "required": ["title", "initVal", "tgtVal", "create_date", "uid"],
  "permission": {
    "read": "doc.uid == auth.uid",
    "create": true,
    "update": "doc.uid == auth.uid",
    "delete": "doc.uid == auth.uid"
  },
  "properties": {
    "_id": {
      "description": "ID，系统自动生成"
    },
    "okr_id": {
      "bsonType": "string",
      "title": "目标 id",
      "description": "外键，所属目标 id",
      "foreignKey": "okr-object._id"
    },
    "title": {
      "bsonType": "string",
      "title": "标题",
      "trim": "both"
    },
    "initVal": {
      "bsonType": "number",
      "title": "初始值"
    },
    "tgtVal": {
      "bsonType": "number",
      "title": "目标值"
    },
    "curVal": {
      "bsonType": "number",
      "title": "当前值"
    },
    "record": {
      "bsonType": "array",
      "title": "记录",
      "arrayType": "object",
      "required": ["number", "create_date"],
      "properties": {
        "number": {
          "bsonType": "number",
          "title": "记录值",
          "trim": "both"
        },
        "todo_id": {
          "bsonType": "string",
          "title": "待办 id",
          "foreignKey": "okr-todo._id"
        },
        "create_date": {
          "bsonType": "timestamp",
          "title": "记录时间",
          "forceDefaultValue": {
            "$env": "now"
          }
        }
      }
    },
    "update_date": {
      "bsonType": "timestamp",
      "title": "更新时间",
      "defaultValue": {
        "$env": "now"
      }
    },
    "create_date": {
      "bsonType": "timestamp",
      "title": "创建时间",
      "forceDefaultValue": {
        "$env": "now"
      }
    },
    "uid": {
      "bsonType": "string",
      "title": "用户 id",
      "forceDefaultValue": {
        "$env": "uid"
      }
    }
  }
}
