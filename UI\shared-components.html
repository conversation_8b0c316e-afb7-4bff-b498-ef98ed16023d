<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>共享组件</title>
		<link
			href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css"
			rel="stylesheet"
		/>
		<link
			rel="stylesheet"
			href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
		/>
		<style>
			@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
			
			:root {
				--color-primary: #5E6AD2;
				--color-primary-light: #7C84E8;
				--color-primary-dark: #4549A9;
				--color-secondary: #4EA8DE;
				--color-secondary-light: #70C1E8;
				--color-secondary-dark: #2889C9;
				--color-accent: #FF7C7C;
				--color-accent-light: #FF9E9E;
				--color-accent-dark: #FF5252;
				--color-success: #56C993;
				--color-warning: #FFAE57;
				--color-danger: #FF6B6B;
				--color-bg: #F8F9FF;
				--color-white: #ffffff;
				--color-gray-100: #F5F7FF;
				--color-gray-200: #EBEEFE;
				--color-gray-300: #D8DCEF;
				--color-gray-400: #B0B5D1;
				--color-gray-500: #868AAF;
				--color-gray-600: #585D80;
				--color-gray-700: #424366;
				--color-gray-800: #2B2C44;
				--color-gray-900: #1A1B2E;
				--rounded-lg: 16px;
				--rounded-md: 12px;
				--rounded-sm: 8px;
				--shadow-sm: 0 2px 8px rgba(94, 106, 210, 0.08);
				--shadow-md: 0 4px 16px rgba(94, 106, 210, 0.12);
				--shadow-lg: 0 8px 24px rgba(94, 106, 210, 0.16);
			}

			body {
				background: var(--color-bg);
				font-family: 'Poppins', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
				color: var(--color-gray-800);
			}

			/* 状态栏样式 */
			.ios-status-bar {
				height: 44px;
				width: 100%;
				background-color: var(--color-white);
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 0 20px;
				position: fixed;
				top: 0;
				left: 0;
				right: 0;
				z-index: 1000;
				border-bottom: none;
				box-shadow: var(--shadow-sm);
			}

			/* 底部导航栏样式 - 现代感风格 */
			.ios-tab-bar {
				height: 80px;
				background: var(--color-white);
				display: flex;
				justify-content: space-evenly;
				align-items: center;
				padding-bottom: 20px;
				border-top: none;
				position: fixed;
				bottom: 0;
				left: 0;
				right: 0;
				z-index: 1000;
				box-shadow: 0 -2px 10px rgba(94, 106, 210, 0.08);
				border-radius: 24px 24px 0 0;
			}

			.tab-item {
				display: flex;
				flex-direction: column;
				align-items: center;
				color: var(--color-gray-500);
				font-size: 12px;
				font-weight: 500;
				transition: all 0.3s ease;
				position: relative;
				padding: 10px 12px;
				border-radius: var(--rounded-md);
				width: 22%;
			}

			.tab-item.active {
				color: var(--color-primary);
				background: var(--color-gray-100);
				transform: translateY(-4px);
			}

			.tab-item.active::after {
				content: "";
				position: absolute;
				bottom: -5px;
				width: 30px;
				height: 4px;
				background: var(--color-primary);
				border-radius: 10px;
				opacity: 0.8;
			}

			.tab-item i {
				font-size: 22px;
				margin-bottom: 6px;
				transition: all 0.3s ease;
			}

			.tab-item.active i {
				color: var(--color-primary);
				transform: scale(1.15);
			}

			/* 导航栏样式 */
			.app-nav-bar {
				padding: 16px 20px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				background: var(--color-white);
				border-bottom: none;
				margin-bottom: 15px;
				box-shadow: var(--shadow-sm);
				border-radius: 0 0 var(--rounded-md) var(--rounded-md);
			}

			.app-nav-bar .title {
				font-weight: 600;
				font-size: 18px;
				color: var(--color-gray-800);
				letter-spacing: -0.3px;
			}

			.app-nav-bar .left-action i,
			.app-nav-bar .right-action i {
				font-size: 20px;
				color: var(--color-primary);
				background: var(--color-gray-100);
				padding: 8px;
				border-radius: 50%;
				transition: all 0.2s ease;
			}

			.app-nav-bar .left-action i:hover,
			.app-nav-bar .right-action i:hover {
				background: var(--color-gray-200);
				transform: scale(1.05);
			}

			/* 内容区域样式 */
			.content-area {
				padding-top: 44px;
				padding-bottom: 80px;
				height: 100vh;
				overflow-y: auto;
				background: var(--color-bg);
			}

			/* 通用卡片样式 */
			.glass-card {
				background: var(--color-white);
				border-radius: var(--rounded-lg);
				border: none;
				margin-bottom: 16px;
				padding: 20px;
				box-shadow: var(--shadow-sm);
				transition: all 0.3s ease;
			}

			.glass-card:hover {
				box-shadow: var(--shadow-md);
				transform: translateY(-2px);
			}

			/* 按钮样式 */
			.gradient-btn {
				background: linear-gradient(120deg, var(--color-primary), var(--color-primary-dark));
				color: white;
				border: none;
				border-radius: var(--rounded-md);
				padding: 12px 20px;
				font-weight: 500;
				transition: all 0.3s ease;
				box-shadow: 0 4px 10px rgba(94, 106, 210, 0.25);
			}

			.gradient-btn:hover {
				background: linear-gradient(120deg, var(--color-primary-dark), var(--color-primary));
				box-shadow: 0 6px 15px rgba(94, 106, 210, 0.35);
				transform: translateY(-2px);
			}

			.gradient-btn:active {
				transform: translateY(1px);
				box-shadow: 0 2px 5px rgba(94, 106, 210, 0.2);
			}
		</style>
	</head>
	<body>
		<!-- iOS 状态栏模板 -->
		<template id="ios-status-bar-template">
			<div class="ios-status-bar">
				<div class="time font-semibold">9:41</div>
				<div class="status-icons flex items-center space-x-2">
					<i class="fas fa-signal"></i>
					<i class="fas fa-wifi"></i>
					<i class="fas fa-battery-three-quarters"></i>
				</div>
			</div>
		</template>

		<!-- App 导航栏模板 -->
		<template id="app-nav-bar-template">
			<div class="app-nav-bar">
				<div class="left-action">
					<i class="fas fa-chevron-left"></i>
				</div>
				<div class="title text-center flex-1">
					<slot name="title">页面标题</slot>
				</div>
				<div class="right-action">
					<slot name="right-action"></slot>
				</div>
			</div>
		</template>

		<!-- iOS 底部标签栏模板 -->
		<template id="ios-tab-bar-template">
			<div class="ios-tab-bar">
				<a href="home.html" class="tab-item">
					<i class="fas fa-bullseye"></i>
					<span>目标</span>
				</a>
				<a href="tasks.html" class="tab-item">
					<i class="fas fa-tasks"></i>
					<span>任务</span>
				</a>
				<a href="time-management.html" class="tab-item">
					<i class="fas fa-clock"></i>
					<span>时间</span>
				</a>
				<a href="analytics.html" class="tab-item">
					<i class="fas fa-chart-pie"></i>
					<span>分析</span>
				</a>
			</div>
		</template>

		<!-- 内容区域模板 -->
		<template id="content-area-template">
			<div class="content-area">
				<slot></slot>
			</div>
		</template>

		<script>
			// 用于在各页面中引入共享组件的脚本
			function loadStatusBar(elementId) {
				const template = document.getElementById("ios-status-bar-template");
				const element = document.getElementById(elementId);
				if (template && element) {
					element.innerHTML = template.innerHTML;
				}
			}

			function loadAppNavBar(elementId, title, rightActionHtml) {
				const template = document.getElementById("app-nav-bar-template");
				const element = document.getElementById(elementId);
				if (template && element) {
					const navBarHtml = template.innerHTML
						.replace('<slot name="title">页面标题</slot>', title)
						.replace(
							'<slot name="right-action"></slot>',
							rightActionHtml || ""
						);
					element.innerHTML = navBarHtml;
				}
			}

			function loadTabBar(elementId, activeTab) {
				const template = document.getElementById("ios-tab-bar-template");
				const element = document.getElementById(elementId);
				if (template && element) {
					let tabBarHtml = template.innerHTML;

					// 设置激活的标签页
					if (activeTab) {
						const tabs = ["home", "tasks", "time-management", "analytics"];
						// 先移除所有 active 类
						tabs.forEach((tab) => {
							tabBarHtml = tabBarHtml.replace(
								`href="${tab}.html" class="tab-item active"`,
								`href="${tab}.html" class="tab-item"`
							);
						});
						// 添加当前页面的 active 类
						tabBarHtml = tabBarHtml.replace(
							`href="${activeTab}.html" class="tab-item"`,
							`href="${activeTab}.html" class="tab-item active"`
						);
					}

					element.innerHTML = tabBarHtml;
				}
			}

			function loadContentArea(elementId) {
				const template = document.getElementById("content-area-template");
				const element = document.getElementById(elementId);
				if (template && element) {
					element.innerHTML = template.innerHTML;
				}
			}

			// 添加主题色更新函数
			function updateThemeColors(theme) {
				// 更新CSS变量
				document.documentElement.style.setProperty('--color-primary', theme.primary);
				document.documentElement.style.setProperty('--color-primary-light', adjustColor(theme.primary, 20));
				document.documentElement.style.setProperty('--color-primary-dark', adjustColor(theme.primary, -20));
				document.documentElement.style.setProperty('--color-secondary', theme.secondary);
				document.documentElement.style.setProperty('--color-secondary-light', adjustColor(theme.secondary, 20));
				document.documentElement.style.setProperty('--color-secondary-dark', adjustColor(theme.secondary, -20));
				document.documentElement.style.setProperty('--color-accent', theme.accent);
				document.documentElement.style.setProperty('--color-accent-light', adjustColor(theme.accent, 20));
				document.documentElement.style.setProperty('--color-accent-dark', adjustColor(theme.accent, -20));
				
				// 更新阴影颜色，基于主色
				const shadowColor = hexToRgb(theme.primary);
				if (shadowColor) {
					document.documentElement.style.setProperty('--shadow-sm', `0 2px 8px rgba(${shadowColor.r}, ${shadowColor.g}, ${shadowColor.b}, 0.08)`);
					document.documentElement.style.setProperty('--shadow-md', `0 4px 16px rgba(${shadowColor.r}, ${shadowColor.g}, ${shadowColor.b}, 0.12)`);
					document.documentElement.style.setProperty('--shadow-lg', `0 8px 24px rgba(${shadowColor.r}, ${shadowColor.g}, ${shadowColor.b}, 0.16)`);
				}
			}

			// 辅助函数：调整颜色明暗
			function adjustColor(hex, percent) {
				// 将十六进制颜色转换为RGB
				let r = parseInt(hex.substring(1, 3), 16);
				let g = parseInt(hex.substring(3, 5), 16);
				let b = parseInt(hex.substring(5, 7), 16);

				// 调整亮度
				r = Math.max(0, Math.min(255, r + percent));
				g = Math.max(0, Math.min(255, g + percent));
				b = Math.max(0, Math.min(255, b + percent));

				// 转回十六进制
				return '#' + 
					((1 << 24) + (r << 16) + (g << 8) + b)
						.toString(16)
						.slice(1);
			}

			// 辅助函数：十六进制转RGB
			function hexToRgb(hex) {
				const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
				return result ? {
					r: parseInt(result[1], 16),
					g: parseInt(result[2], 16),
					b: parseInt(result[3], 16)
				} : null;
			}

			// 监听来自父窗口的主题更改消息
			window.addEventListener('message', function(event) {
				if (event.data && event.data.type === 'THEME_CHANGE') {
					updateThemeColors(event.data.theme);
				}
			});
		</script>
	</body>
</html>
