//全局配置
const baseHost = 'https://fc-mp-c6815a6a-de20-45ad-a345-2e804f127311.next.bspapp.com/lifeOS'

const errCodes = {
  'uni-id-token-expired': '登录过期，请重新登录！',
  'uni-id-param-required': '缺少参数',
  'uni-id-check-token-failed': 'token 校验未通过',
  'uni-id-account-exists': '账户已存在',
  'uni-id-account-not-exists': '账户不存在',
  'uni-id-account-not-exists-in-current-app': '匹配到的用户不可在当前应用登录',
  'uni-id-account-conflict': '用户账号冲突',
  'uni-id-account-banned': '此账号已封禁',
  'uni-id-account-auditing': '此账号正在审核中',
  'uni-id-account-audit-failed': '此账号审核失败',
  'uni-id-account-closed': '此账号已注销',
  'uni-id-captcha-required': '	请输入图形验证码',
  'uni-id-password-error': '用户名或密码错误',
  'uni-id-invalid-username': '用户名不合法',
  'uni-id-invalid-password': '	密码不合法',
  'uni-id-invalid-mobile': '手机号码不合法',
  'uni-id-invalid-email': '邮箱不合法',
  'uni-id-invalid-nickname': '	昵称不合法',
  'uni-id-invalid-param': '参数错误',
  'uni-id-get-third-party-account-failed': '获取第三方账号失败',
  'uni-id-get-third-party-user-info-failed': '获取第三方用户信息失败',
  'uni-id-mobile-verify-code-error': '手机验证码错误或已过期',
  'uni-id-email-verify-code-error': '邮箱验证码错误或已过期',
  'uni-id-admin-exists': '超级管理员已存在',
  'uni-id-permission-error': '权限错误',
  'uni-id-system-error': '	系统错误',
  'uni-id-set-invite-code-failed': '设置邀请码失败',
  'uni-id-invalid-invite-code': '邀请码不可用',
  'uni-id-change-inviter-forbidden': '禁止修改邀请人',
  'uni-id-bind-conflict': '此账号已被绑定',
  FunctionBizError: '业务异常',
}

function request(
  method,
  url,
  params,
  option = {
    timeout: 10000,
  }
) {
  const hasHttp = url.indexOf('http') !== -1
  const token = uni.getStorageSync('lifeOSKey')
  if (token) {
    option.header = {
      ...option.header,
      'x-api-key': token,
    }
  }
  return new Promise((resolve, reject) => {
    uni.request({
      ...option,
      method: method,
      url: baseHost + url,
      data: params,
      // 请求成功
      success: ({ data }) => {
        console.log('请求成功', data, hasHttp)
        if (data.code === 401) {
          uni.showToast({
            title: data?.message,
            icon: 'none',
          })
        }
        if (data.code !== 200) {
          uni.showToast({
            title: '请求失败！',
            icon: 'none',
          })
        }
        resolve(data)
      },
      // 请求失败
      fail: (err) => {
        console.error('err===')
        console.error(err)

        // uni.showToast({
        // 	title: "请求失败！请检查网络配置！",
        // 	icon: "none"
        // })
        // goPage.replace('/pages/error/500')
      },
    })
  })
}

export default {
  get(url, params, option) {
    return new Promise((resolve, reject) => {
      request('GET', url, params, option)
        .then((res) => {
          resolve(res)
        })
        .catch((err) => {
          reject(err)
        })
    })
  },
  post(url, params, option) {
    return new Promise((resolve, reject) => {
      request('POST', url, params, option)
        .then((res) => {
          resolve(res)
        })
        .catch((err) => {
          reject(err)
        })
    })
  },
  put(url, params, option) {
    return new Promise((resolve, reject) => {
      request('PUT', url, params, option)
        .then((res) => {
          resolve(res)
        })
        .catch((err) => {
          reject(err)
        })
    })
  },
  delete(url, params, option) {
    return new Promise((resolve, reject) => {
      request('DELETE', url, params, option)
        .then((res) => {
          resolve(res)
        })
        .catch((err) => {
          reject(err)
        })
    })
  },
}
