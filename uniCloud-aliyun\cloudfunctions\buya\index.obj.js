// 云对象教程：https://uniapp.dcloud.net.cn/uniCloud/cloud-obj
// jsdoc 语法提示教程：https://ask.dcloud.net.cn/docs/#//ask.dcloud.net.cn/article/129

const { wxServerHost, apiKey, openaiProxy, summonses } = require('./config.js')
const { getLocalTime } = require('./utils')
const buyaDB = uniCloud.databaseForJQL().collection('buya')
// 特定回复
const getFixedMeg = async (fromParams, toParams) => {
  if (['睡'].includes(fromParams.msg)) {
    // 目标时间为每天凌晨 12 点
    const targetHour = 23
    const targetMinute = 59

    const now = getLocalTime()
    const target = getLocalTime()
    // 如果当前时间已经过了目标时间，则目标时间改成明天的同一时刻
    if (now.getHours() > targetHour || (now.getHours() === targetHour && now.getMinutes() >= targetMinute))
      return (toParams.msg = '该睡了')

    target.setHours(targetHour)
    target.setMinutes(targetMinute)
    target.setSeconds(0)

    const diff = target - now
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const minutes = Math.ceil((diff % (1000 * 60 * 60)) / (1000 * 60))

    return (toParams.msg = `距离睡觉还有：${hours} 小时 ${minutes} 分钟`)
  }
  // if (['吃'].includes(fromParams.msg)){
  // 	// 目标时间为每天中午 12 点
  // 	const targetHour = 12
  // 	  const targetMinute = 00

  // 	  const now = getLocalTime()
  // 	  const target = getLocalTime()
  // 	  // 如果当前时间已经过了目标时间，则目标时间改成明天的同一时刻
  // 	  if (now.getHours() > targetHour || (now.getHours() === targetHour && now.getMinutes() >= targetMinute))
  // 		return '吃什么吃'

  // 	  target.setHours(targetHour)
  // 	  target.setMinutes(targetMinute)
  // 	  target.setSeconds(0)

  // 	  const diff = target - now
  // 	  const hours = Math.floor(diff / (1000 * 60 * 60))
  // 	  const minutes = Math.ceil((diff % (1000 * 60 * 60)) / (1000 * 60))

  // 	  return `距离中午吃饭：${hours} 小时 ${minutes} 分钟`
  // }
  if (['下', '下班', '下班倒计时'].includes(fromParams.msg)) {
    // 目标时间为每天下午 6 点半
    const targetHour = 18
    const targetMinute = 30

    const now = getLocalTime()
    const target = getLocalTime()
    // 如果当前时间已经过了目标时间，则目标时间改成明天的同一时刻
    if (now.getHours() > targetHour || (now.getHours() === targetHour && now.getMinutes() >= targetMinute))
      return (toParams.msg = '已经下班了，快上号！')

    target.setHours(targetHour)
    target.setMinutes(targetMinute)
    target.setSeconds(0)

    const diff = target - now
    const hours = Math.floor(diff / (1000 * 60 * 60))
    const minutes = Math.ceil((diff % (1000 * 60 * 60)) / (1000 * 60))
    console.log('===toParams===')
    return (toParams.msg = `距离下班：${hours} 小时 ${minutes} 分钟`)
  }
  if (['上', '上班', '上班倒计时'].includes(fromParams.msg)) {
    // 获取当前时间
    const now = getLocalTime()

    // 设置上班时间
    const workStart = getLocalTime()
    workStart.setHours(9)
    workStart.setMinutes(0)

    // 设置下班时间
    const workEnd = getLocalTime()
    workEnd.setHours(18)
    workEnd.setMinutes(30)

    // 设置午休开始时间
    const lunchStart = getLocalTime()
    lunchStart.setHours(12)
    lunchStart.setMinutes(0)

    // 设置午休结束时间
    const lunchEnd = getLocalTime()

    lunchEnd.setHours(13)
    lunchEnd.setMinutes(30)

    // 判断当前时间是否在上班时间内
    if (now >= workStart && now <= workEnd) {
      // 计算已经过去的时间
      let elapsed = now - workStart

      // 如果当前时间在下午，需要减去午休时间
      if (now > lunchEnd) elapsed -= lunchEnd - lunchStart
      // 如果当前时间在午休
      else if (now > lunchStart && now < lunchEnd) elapsed -= now - lunchStart

      // 显示已经上班的时间
      const hours = Math.floor(elapsed / 3600000)
      const minutes = Math.floor((elapsed % 3600000) / 60000)

      return (toParams.msg = `今日已上班 ${hours} 小时 ${minutes} 分钟`)
    } else {
      return (toParams.msg = '还没到上班时间，沙雕')
    }
  }
  if (fromParams.msg.indexOf('加菜：') === 0) {
    const { data } = await buyaDB.where(`group_id == '${fromParams.from_wxid}'`).get({ getOne: true })
    const menuList = fromParams.msg.split('加菜：').join('').trim().split(' ')
    if (data && data.menu_list) {
      await buyaDB.where(`group_id == '${fromParams.from_wxid}'`).update({
        menu_list: [...new Set([...data.menu_list, ...menuList])],
      })
    } else {
      await buyaDB.add({
        title: `菜谱-${fromParams.from_name}`,
        group_id: fromParams.from_wxid,
        menu_list: menuList,
      })
    }
    let str = `已添加，菜谱如下：`
    data.menu_list.forEach((e) => {
      str += `\n${e}`
    })
    menuList.forEach((e) => {
      str += `\n${e}（新加）`
    })
    return (toParams.msg = toParams.msg = str)
  }
  if (fromParams.msg.indexOf('删菜：') === 0) {
    const { data } = await buyaDB.where(`group_id == '${fromParams.from_wxid}'`).get({ getOne: true })
    const delMenuList = fromParams.msg.split('删菜：').join('').trim().split(' ')
    let menu_list = []
    if (data && data.menu_list) {
      menu_list = data.menu_list.filter((e) => delMenuList.indexOf(e) === -1)
      await buyaDB.where(`group_id == '${fromParams.from_wxid}'`).update({
        menu_list,
      })
    } else {
      return (toParams.msg = '没菜可删')
    }
    let str = `已删除，菜谱如下：`
    menu_list.forEach((e) => {
      str += `\n${e}`
    })
    return (toParams.msg = str)
  }
  if (['菜谱'].includes(fromParams.msg)) {
    const { data } = await buyaDB.where(`group_id == '${fromParams.from_wxid}'`).get({ getOne: true })
    let str = `菜谱列表：\n`
    if (data && data.menu_list) {
      data.menu_list.forEach((e) => {
        str += `${e}\n`
      })
    } else {
      str += '无\n'
    }
    str += '\n增减菜品指令：\n加菜：猪哥哥 肥牛饭 福建人\n删菜：猪哥哥 湖建人\n\n发送【今天吃什么】开始纠结点菜'
    return (toParams.msg = str)
  }
  if (['中午吃啥', '今天吃什么', '今天吃啥', '不吃', '吃'].includes(fromParams.msg)) {
    const { data } = await buyaDB.where(`group_id == '${fromParams.from_wxid}'`).get({ getOne: true })
    let { menu_list, menu_date, menu_done } = data
    // 减去已选菜品
    console.log('减去已选菜品')
    console.log(menu_done)
    console.log(menu_list)

    if (['中午吃啥', '今天吃啥', '今天吃什么'].includes(fromParams.msg)) {
      const randomItem = menu_list[Math.floor(Math.random() * menu_list.length)]
      await buyaDB.where(`group_id == '${fromParams.from_wxid}'`).update({
        menu_date: getLocalTime().getTime(),
        menu_done: [randomItem],
      })
      return (toParams.msg = `【${randomItem}】吃不吃？\n\n请回答【吃】或者【不吃】`)
    }
    if ('不吃' === fromParams.msg) {
      if (menu_done) menu_list = menu_list.filter((e) => menu_done.indexOf(e) === -1)
      const randomItem = menu_list[Math.floor(Math.random() * menu_list.length)]
      const currentDate = getLocalTime()
      if (!randomItem) {
        return (toParams.msg = '没菜了，都选完了。。。')
      }
      if (new Date(menu_date) > currentDate.setMinutes(currentDate.getMinutes() - 30)) {
        // 询问时间在半小时内
        await buyaDB.where(`group_id == '${fromParams.from_wxid}'`).update({
          menu_done: [...menu_done, randomItem],
        })
        return (toParams.msg = `不吃的话，吃【${randomItem}】吗？`)
      }
    }
    if ('吃' === fromParams.msg) {
      const c = menu_done[menu_done.length - 1]
      return (toParams.msg = `好！今天吃【${c}】`)
    }
  }
}
// chatGPT 回复
const getGPT = async (fromParams, toParams) => {
  // 判断是否需要回复消息
  const isTrue = canReplyMsg(fromParams, toParams)
  if (!isTrue) return

  const h = '画：'
  const y = '使用量'
  // 画图
  if (fromParams.msg.indexOf(h) === 0) {
    console.log('开始画图')
    const { data } = await uniCloud.httpclient.request(`https://${openaiProxy}/v1/images/generations`, {
      method: 'POST',
      contentType: 'json',
      dataType: 'json',
      timeout: 60000,
      headers: {
        Authorization: `Bearer ${apiKey}`,
      },
      data: {
        prompt: fromParams.msg.split(h).join(''),
        n: 1,
        size: '512x512',
      },
    })
    console.log('画图 end')
    console.log(data)
    if (!data.error) {
      toParams.event = 'SendImageMsg'
      toParams.msg = {
        name: '1.png',
        url: data.data[0].url,
      }
    } else {
      toParams.msg = '你这个太色了，画不了！'
    }
    return
  }
  // 查询使用量
  if (fromParams.msg === y) {
    // 获取当前日期
    var currentDate = new Date()

    // 计算 100 天前的日期
    var targetDate = new Date()
    targetDate.setDate(currentDate.getDate() - 100)

    // 获取年、月、日
    var year = targetDate.getFullYear()
    var month = targetDate.getMonth() + 1 // 月份从 0 开始，所以要加 1
    var day = targetDate.getDate()

    // 将日期格式化为字符串
    var formattedDate = year + '-' + month + '-' + day
    const nowDate = currentDate.getFullYear() + '-' + (currentDate.getMonth() + 1) + '-' + currentDate.getDate()
    const { data } = await uniCloud.httpclient.request(`https://${openaiProxy}/v1/dashboard/billing/usage`, {
      method: 'GET',
      contentType: 'json',
      dataType: 'json',
      timeout: 30000,
      headers: {
        Authorization: `Bearer ${apiKey}`,
      },
      data: {
        start_date: formattedDate,
        end_date: nowDate,
      },
    })
    toParams.msg = data.total_usage / 100
    toParams.msg = `apiKey 已经使用：${toParams.msg.toFixed(2)}$`
    return
  }
  // 聊天
  const { data } = await buyaDB.where(`chatlog_id == '${fromParams.from_wxid}'`).get({ getOne: true })
  let chatlog_list = []
  var halfHourAgo = getLocalTime().getTime() - 5 * 60 * 1000 // 5 分钟前的时间戳
  if (data && data.update_date > halfHourAgo) {
    chatlog_list = data.chatlog_list
  }
  chatlog_list.push({
    role: 'user',
    content: fromParams.msg,
  })

  const llmManager = uniCloud.ai.getLLMManager({
    provider: 'openai',
    apiKey,
    proxy: openaiProxy, //也可以是 ip
  })
  const m = await llmManager.chatCompletion({
    messages: chatlog_list,
    // model: 'gpt-4',
  })
  toParams.msg = m.reply

  chatlog_list.push({
    role: 'assistant',
    content: toParams.msg,
  })
  if (data) {
    // 更新
    await buyaDB.where(`chatlog_id == '${fromParams.from_wxid}'`).update({
      chatlog_list,
      update_date: getLocalTime().getTime(),
    })
  } else {
    // 添加
    await buyaDB.add({
      title: `聊天记录-${fromParams.from_name}`,
      chatlog_id: fromParams.from_wxid,
      chatlog_list,
    })
  }
}
// 需要回复消息
const canReplyMsg = (fromParams, toParams) => {
  let isTrue = false
  fromParams.msg = fromParams.msg.trim()
  const list = ['不鸭', '鸭哥', '[@at,nickname=不鸭不鸭，wxid=wxid_8j6s37pfxclw22]', '@不鸭不鸭']
  // 群聊
  if (fromParams.event === 'EventGroupMsg') {
    list.forEach((e) => {
      if (fromParams.msg.indexOf(e) === 0) {
        fromParams.msg = fromParams.msg.split(e).join('')
        toParams.event = 'SendGroupMsgAndAt'
        toParams.member_wxid = fromParams.final_from_wxid
        toParams.group_wxid = fromParams.from_wxid
        isTrue = true
      }
    })
  }
  // 私聊
  if (fromParams.event === 'EventFriendMsg') {
    toParams.event = 'SendTextMsg'
    isTrue = true
  }
  fromParams.msg = fromParams.msg.trim()
  return isTrue
}
/**
 * 回复消息
 * @param {string} robot_wxid 机器人 id
 * @param {string} to_wxid 发给谁
 * @param {string} msg 消息内容
 * @param {string} event 消息类型
 */
const callbackMsg = async (toParams) => {
  await uniCloud.httpclient.request(wxServerHost, {
    method: 'POST',
    contentType: 'json',
    data: toParams,
  })
}
// 添加 memo
const onSaveMemo = async (fromParams, toParams) => {
  console.log('====fromParams=====')
  console.log(fromParams)
  if (fromParams.from_wxid === 'APTX-4869_1412' && fromParams.event === 'EventFriendMsg') {
    console.log('已经添加啦')
    const memo = uniCloud.importObject('memo')
    const res = await memo.memo({
      content: fromParams.msg,
    })
    console.log(res)
    if (res.code === 200) {
      toParams.msg = `已经添加啦，点击查看 https://todo.xpzgg.top/#/pages/memo/memo`
    } else {
      toParams.msg = '添加失败'
    }
  }
}
// 检查白名单
const addWhiteList = async (fromParams, toParams) => {
  const { data } = await buyaDB.where(`white_id == '${fromParams.from_wxid}'`).get({ getOne: true })
  console.log(data)
  console.log(fromParams.msg)
  if (!data && fromParams.msg !== summonses) throw new Error('不在白名单')
  if (!data) {
    const isGroup = fromParams.event === 'EventGroupMsg'
    let title = `白名单-${isGroup ? '群-' : ''}${fromParams.from_name}`
    await buyaDB.add({
      title,
      type: 'white',
      white_id: fromParams.from_wxid,
    })
    toParams.msg = '已添加白名单'
  }
}
module.exports = {
  _before: async function () {
    const httpInfo = this.getHttpInfo()
    const { from_wxid, from_name, msg } = JSON.parse(httpInfo.body)
    console.log('-------------start--------------')
  },
  async wxchat() {
    const httpInfo = this.getHttpInfo()
    const fromParams = JSON.parse(httpInfo.body)
    const toParams = {event:'SendTextMsg',robot_wxid:'wxid_8j6s37pfxclw22',to_wxid:fromParams.from_wxid,msg:''}

    // 检查到白名单
    await addWhiteList(fromParams, toParams)
    // await onSaveMemo(fromParams, toParams)
    // 特定回复
    if (!toParams.msg) await getFixedMeg(fromParams, toParams)
    // chatGPT 回复
    if (!toParams.msg) await getGPT(fromParams, toParams)
    console.log('===========回复的消息==========')
    console.log(toParams)
    // 回复消息
    if (toParams.msg) await callbackMsg(toParams)
  },
}
