<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>登录 - OKR助手</title>
		<link
			href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css"
			rel="stylesheet"
		/>
		<link
			rel="stylesheet"
			href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
		/>
		<!-- 引入共享组件 -->
		<script>
			// 动态加载 shared-components.html
			(function () {
				const xhr = new XMLHttpRequest();
				xhr.open("GET", "shared-components.html", true);
				xhr.onreadystatechange = function () {
					if (xhr.readyState === 4 && xhr.status === 200) {
						// 创建临时容器
						const tempDiv = document.createElement("div");
						tempDiv.innerHTML = xhr.responseText;

						// 提取 style 标签并添加到 head
						const styleTags = tempDiv.getElementsByTagName("style");
						for (let i = 0; i < styleTags.length; i++) {
							const styleContent = styleTags[i].textContent;
							const style = document.createElement("style");
							style.textContent = styleContent;
							document.head.appendChild(style);
						}

						// 添加模板到页面
						const templates = tempDiv.getElementsByTagName("template");
						for (let i = 0; i < templates.length; i++) {
							document.body.appendChild(templates[i].cloneNode(true));
						}
					}
				};
				xhr.send();
			})();
		</script>
		<style>
			@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap");

			body {
				height: 100vh;
				overflow: hidden;
				background-color: var(--color-bg);
			}

			.login-container {
				height: 100vh;
				width: 100%;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				position: relative;
				z-index: 1;
				overflow: hidden;
			}

			.login-header {
				margin-top: 60px;
				text-align: center;
			}

			.login-form {
				padding: 0 24px;
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: center;
			}

			.login-title {
				font-size: 32px;
				font-weight: 700;
				margin-bottom: 8px;
				text-align: center;
				color: var(--color-primary);
			}

			.login-subtitle {
				font-size: 16px;
				color: var(--color-gray-600);
				text-align: center;
				margin-bottom: 40px;
			}

			.form-input {
				background: var(--color-white);
				border: 1px solid var(--color-gray-300);
				border-radius: var(--rounded-md);
				padding: 16px;
				margin-bottom: 16px;
				font-size: 16px;
				transition: all 0.2s ease;
				width: 100%;
			}

			.form-input:focus {
				outline: none;
				border-color: var(--color-primary);
			}

			.login-button {
				background: var(--color-primary);
				color: white;
				font-weight: 600;
				padding: 16px;
				border-radius: var(--rounded-md);
				text-align: center;
				margin-top: 20px;
				transition: all 0.2s ease;
			}

			.login-button:hover {
				background: var(--color-primary-dark);
			}

			.login-button:active {
				transform: translateY(1px);
			}

			.login-footer {
				text-align: center;
				margin-bottom: 40px;
			}

			.login-options {
				display: flex;
				justify-content: center;
				margin-top: 16px;
			}

			.login-option {
				color: var(--color-gray-600);
				margin: 0 10px;
				font-size: 14px;
				font-weight: 500;
				transition: all 0.2s ease;
			}

			.login-option:hover {
				color: var(--color-primary);
			}

			.social-login {
				display: flex;
				justify-content: center;
				margin-top: 40px;
				gap: 20px;
			}

			.social-button {
				width: 50px;
				height: 50px;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				background: var(--color-white);
				border: 1px solid var(--color-gray-200);
				transition: all 0.2s ease;
			}

			.social-button:hover {
				border-color: var(--color-gray-300);
			}

			.social-button.wechat {
				color: #07c160;
			}

			.social-button.qq {
				color: #12b7f5;
			}

			.social-button.weibo {
				color: #e6162d;
			}

			.app-logo {
				width: 80px;
				height: 80px;
				border-radius: 12px;
				background: var(--color-primary);
				display: flex;
				align-items: center;
				justify-content: center;
				margin: 0 auto;
			}

			.logo-icon {
				font-size: 40px;
				color: white;
			}
		</style>
	</head>
	<body>
		<div class="login-container">
			<div class="login-header">
				<div class="app-logo">
					<i class="fas fa-bullseye logo-icon"></i>
				</div>
			</div>

			<div class="login-form">
				<h1 class="login-title">欢迎使用OKR助手</h1>
				<p class="login-subtitle">登录您的账号以继续</p>

				<input
					type="text"
					class="form-input"
					placeholder="用户名或邮箱"
					autofocus
				/>
				<input type="password" class="form-input" placeholder="密码" />

				<button class="login-button">登 录</button>

				<div class="login-options">
					<a href="#" class="login-option">忘记密码?</a>
					<a href="#" class="login-option">注册账号</a>
				</div>

				<div class="social-login">
					<button class="social-button wechat">
						<i class="fab fa-weixin fa-lg"></i>
					</button>
					<button class="social-button qq">
						<i class="fab fa-qq fa-lg"></i>
					</button>
					<button class="social-button weibo">
						<i class="fab fa-weibo fa-lg"></i>
					</button>
				</div>
			</div>

			<div class="login-footer">
				<p class="text-gray-500 text-sm">
					© 2023 OKR助手 - 版本 1.0.0
				</p>
			</div>
		</div>

		<script>
			// 页面初始化脚本
			document.addEventListener("DOMContentLoaded", function () {
				// 表单提交事件
				const loginButton = document.querySelector(".login-button");
				if (loginButton) {
					loginButton.addEventListener("click", function () {
						window.location.href = "home.html";
					});
				}
				
				// 接收主题变更消息
				window.addEventListener('message', function(event) {
					if (event.data && event.data.type === 'THEME_CHANGE') {
						applyTheme(event.data.theme);
					}
				});
			});
			
			// 应用主题颜色到特定元素
			function applyTheme(theme) {
				// 应用到APP Logo背景
				const appLogo = document.querySelector('.app-logo');
				if (appLogo) {
					appLogo.style.background = `linear-gradient(135deg, ${theme.primary}, ${theme.secondary})`;
				}
				
				// 应用到标题
				const loginTitle = document.querySelector('.login-title');
				if (loginTitle) {
					loginTitle.style.background = `linear-gradient(120deg, ${theme.primary}, ${theme.secondary})`;
					loginTitle.style.webkitBackgroundClip = 'text';
					loginTitle.style.webkitTextFillColor = 'transparent';
				}
				
				// 登录按钮
				const loginButton = document.querySelector('.login-button');
				if (loginButton) {
					loginButton.style.background = `linear-gradient(120deg, ${theme.primary}, ${theme.secondary})`;
				}
				
				// 输入框焦点边框色
				document.documentElement.style.setProperty('--color-primary', theme.primary);
				document.documentElement.style.setProperty('--color-primary-dark', adjustColor(theme.primary, -20));
				
				// 更新文字链接悬停颜色
				const loginOptions = document.querySelectorAll('.login-option');
				loginOptions.forEach(option => {
					const originalStyle = option.style.color;
					option.addEventListener('mouseenter', () => {
						option.style.color = theme.primary;
					});
					option.addEventListener('mouseleave', () => {
						option.style.color = '';
					});
				});
			}
			
			// 辅助函数：调整颜色亮度
			function adjustColor(hex, percent) {
				// 将十六进制颜色转换为RGB
				let r = parseInt(hex.substring(1, 3), 16);
				let g = parseInt(hex.substring(3, 5), 16);
				let b = parseInt(hex.substring(5, 7), 16);

				// 调整亮度
				r = Math.max(0, Math.min(255, r + percent));
				g = Math.max(0, Math.min(255, g + percent));
				b = Math.max(0, Math.min(255, b + percent));

				// 转回十六进制
				return '#' + 
					((1 << 24) + (r << 16) + (g << 8) + b)
						.toString(16)
						.slice(1);
			}
		</script>
	</body>
</html>
