<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>创建目标 | LifeOS</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />
    <!-- 引入共享组件 -->
    <script>
      // 动态加载 shared-components.html
      ;(function () {
        const xhr = new XMLHttpRequest()
        xhr.open('GET', 'shared-components.html', true)
        xhr.onreadystatechange = function () {
          if (xhr.readyState === 4 && xhr.status === 200) {
            // 创建临时容器
            const tempDiv = document.createElement('div')
            tempDiv.innerHTML = xhr.responseText

            // 提取 style 标签并添加到 head
            const styleTags = tempDiv.getElementsByTagName('style')
            for (let i = 0; i < styleTags.length; i++) {
              const styleContent = styleTags[i].textContent
              const style = document.createElement('style')
              style.textContent = styleContent
              document.head.appendChild(style)
            }

            // 提取script标签内容并执行
            const scriptTags = tempDiv.getElementsByTagName('script')
            if (scriptTags.length > 0) {
              const scriptContent = scriptTags[0].textContent
              const script = document.createElement('script')
              script.textContent = scriptContent
              document.head.appendChild(script)

              // 等待脚本加载后初始化标签栏
              setTimeout(function () {
                loadTabBar('tab-bar-container')
              }, 100)
            }

            // 添加模板到页面
            const templates = tempDiv.getElementsByTagName('template')
            for (let i = 0; i < templates.length; i++) {
              document.body.appendChild(templates[i].cloneNode(true))
            }
          }
        }
        xhr.send()
      })()
    </script>
    <style>
      @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

      :root {
        --primary-color: var(--color-primary, #4361ee);
        --primary-light: var(--color-primary-light, rgba(67, 97, 238, 0.1));
        --primary-dark: var(--color-primary-dark, #3a56d4);
        --accent-color: var(--color-accent, #f72585);
        --text-color: var(--color-gray-800, #2b2d42);
        --bg-color: var(--color-bg, #eef1ff);
        --light-bg: var(--color-gray-100, #f8f9fa);
        --light-bg-alt: var(--color-gray-200, #f1f3f9);
        --white: var(--color-white, #ffffff);
        --border-color: var(--color-gray-300, #e9ecef);
        --success-color: var(--color-success, #28a745);
        --shadow-sm: var(--shadow-sm, 0 2px 8px rgba(0, 0, 0, 0.05));
        --shadow-md: var(--shadow-md, 0 4px 12px rgba(0, 0, 0, 0.08));
        --shadow-lg: var(--shadow-lg, 0 8px 24px rgba(0, 0, 0, 0.12));
        --transition-fast: all 0.2s ease;
        --transition-normal: all 0.3s ease;
        --font-sans: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
        --rounded-sm: 0.375rem;
        --rounded-md: 0.5rem;
        --rounded-lg: 0.75rem;
        --rounded-xl: 1rem;
        --rounded-2xl: 1.5rem;
        --rounded-full: 9999px;
      }

      body {
        font-family: var(--font-sans);
        background: var(--bg-color);
        height: 100vh;
        display: flex;
        flex-direction: column;
        color: var(--text-color);
        overflow-x: hidden;
        line-height: 1.6;
      }

      .content-area {
        flex: 1;
        overflow-y: auto;
        padding: 0 16px 90px;
        scroll-behavior: smooth;
      }

      .navbar {
        display: flex;
        align-items: center;
        padding: 15px 20px;
        background: var(--bg-color);
        position: sticky;
        top: 44px;
        z-index: 40;
        backdrop-filter: blur(12px);
        -webkit-backdrop-filter: blur(12px);
      }

      .navbar-title {
        font-weight: 600;
        font-size: 18px;
        margin-left: 15px;
        color: var(--text-color);
        letter-spacing: -0.3px;
      }

      .navbar-back {
        color: var(--text-color);
        width: 36px;
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: var(--transition-fast);
        border-radius: var(--rounded-full);
      }

      .navbar-back:hover {
        background: var(--light-bg-alt);
      }

      .navbar-action {
        margin-left: auto;
        color: var(--primary-color);
        font-weight: 500;
        font-size: 15px;
        padding: 8px 16px;
        border-radius: var(--rounded-full);
        transition: var(--transition-fast);
      }

      .navbar-action:hover {
        background: var(--primary-light);
      }

      .container {
        max-width: 600px;
        margin: 0 auto;
        padding: 15px 15px 30px;
      }

      /* 表单部分 */
      .form-section {
        margin-bottom: 24px;
      }

      .section-label {
        font-size: 14px;
        color: var(--text-color);
        margin-bottom: 12px;
        margin-left: 5px;
        opacity: 0.8;
        font-weight: 500;
      }

      .input-card {
        background-color: white;
        border-radius: 16px;
        padding: 16px 18px;
        box-shadow: var(--shadow-sm);
        margin-bottom: 5px;
        border: 1px solid var(--border-color);
        transition: var(--transition-normal);
      }

      .input-card:hover {
        transform: translateY(-1px);
        box-shadow: var(--shadow-lg);
      }

      .color-picker {
        display: flex;
        align-items: center;
        margin-bottom: 0;
        position: relative;
      }

      .color-circle {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: var(--primary-color);
        margin-right: 16px;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
        cursor: pointer;
        transition: var(--transition-normal);
        border: 1.5px solid rgba(255, 255, 255, 0.8);
      }

      .color-circle:after {
        content: '';
      }

      .color-circle:before {
        content: '\f53f';
        font-family: 'Font Awesome 5 Free';
        font-weight: 900;
        font-size: 12px;
        color: rgba(255, 255, 255, 0.9);
      }

      .color-circle:hover {
        transform: scale(1.08);
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
      }

      .input-field {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        min-height: 46px;
      }

      .input-text {
        width: 100%;
        border: none;
        font-size: 16px;
        color: var(--text-color);
        background: transparent;
        padding: 5px 0;
        margin-bottom: 2px;
        border-bottom: 1px solid transparent;
      }

      .input-text:focus {
        outline: none;
        border-color: var(--color-gray-300, #eee);
      }

      .input-hint {
        font-size: 13px;
        color: var(--color-gray-500, #aaa);
        margin-top: 4px;
      }

      /* 日期部分 */
      .date-display {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        background-color: white;
        border-radius: 16px;
        padding: 16px;
        box-shadow: var(--shadow-sm);
      }

      .date-icon {
        color: var(--color-accent, #ff6b6b);
        margin-right: 15px;
        font-size: 18px;
      }

      .date-range {
        font-size: 15px;
        font-weight: 500;
        flex: 1;
        color: var(--text-color);
      }

      .date-details {
        display: flex;
        gap: 12px;
        margin-bottom: 15px;
      }

      .date-card {
        flex: 1;
        background-color: white;
        border-radius: 16px;
        padding: 16px;
        box-shadow: var(--shadow-sm);
      }

      .date-label {
        font-size: 13px;
        color: var(--color-gray-500, #888);
        margin-bottom: 6px;
      }

      .date-value {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-color);
      }

      .date-day {
        font-size: 12px;
        color: var(--color-gray-500, #888);
        margin-top: 4px;
      }

      /* 持续时间部分 */
      .duration-container {
        background-color: white;
        border-radius: 16px;
        padding: 16px;
        box-shadow: var(--shadow-sm);
      }

      .duration-label {
        font-size: 13px;
        color: var(--color-gray-500, #888);
        margin-bottom: 6px;
      }

      .duration-control {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 4px;
      }

      .duration-value {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-color);
        text-align: center;
        flex: 1;
        margin: 0 15px;
      }

      .duration-btn {
        width: 32px;
        height: 32px;
        border-radius: 50%;
        background-color: var(--primary-color);
        color: white;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: var(--transition-normal);
        box-shadow: var(--shadow-sm);
      }

      .duration-btn i {
        font-size: 12px;
      }

      .duration-btn:hover {
        transform: scale(1.1);
        box-shadow: var(--shadow-md);
      }

      /* 颜色选择器弹窗 */
      .color-picker-modal {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        background: white;
        border-radius: 24px 24px 0 0;
        padding: 24px 20px;
        box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.08);
        z-index: 100;
        transform: translateY(100%);
        transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      }

      .color-picker-modal.active {
        transform: translateY(0);
      }

      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 18px;
      }

      .modal-title {
        font-size: 18px;
        font-weight: 600;
        color: var(--text-color);
      }

      .modal-close {
        width: 28px;
        height: 28px;
        border-radius: 50%;
        background: var(--color-gray-100, #f5f5f5);
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
      }

      .modal-close:hover {
        background: var(--color-gray-200, #e9e9e9);
        transform: scale(1.05);
      }

      .modal-close i {
        font-size: 14px;
        color: var(--color-gray-600, #666);
      }

      .color-grid {
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        gap: 14px 12px;
        padding: 6px 0 28px;
        max-width: 350px;
        margin: 0 auto;
      }

      .color-option {
        aspect-ratio: 1/1;
        border-radius: 50%;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
        position: relative;
        cursor: pointer;
        transition: var(--transition-normal);
        border: 1.5px solid rgba(255, 255, 255, 0.8);
        max-width: 42px;
        width: 100%;
        margin: 0 auto;
      }

      .color-option:hover {
        transform: scale(1.08);
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.12);
      }

      .color-option.selected {
        transform: scale(1.1);
        border-color: var(--white);
        box-shadow: 0 0 0 2px var(--primary-color);
      }

      .color-option.selected:after {
        content: '✓';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-weight: bold;
        font-size: 14px;
      }

      .overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.4);
        backdrop-filter: blur(4px);
        z-index: 90;
        opacity: 0;
        pointer-events: none;
        transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }

      .overlay.active {
        opacity: 1;
        pointer-events: all;
      }

      /* 底部按钮 */
      .footer {
        position: fixed;
        bottom: 0;
        left: 0;
        width: 100%;
        background: var(--light-bg);
        padding: 16px 20px calc(16px + env(safe-area-inset-bottom, 0));
        box-shadow: var(--shadow-lg);
        z-index: 50;
        border-top: 1px solid var(--border-color);
        backdrop-filter: blur(12px);
        -webkit-backdrop-filter: blur(12px);
      }

      .create-btn {
        width: 100%;
        background: var(--primary-color);
        color: white;
        border: none;
        border-radius: var(--rounded-xl);
        padding: 16px;
        font-size: 16px;
        font-weight: 600;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 4px 12px rgba(67, 97, 238, 0.3);
        transition: var(--transition-normal);
        cursor: pointer;
      }

      .create-btn:hover {
        background: var(--primary-dark);
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(67, 97, 238, 0.4);
      }

      .create-btn:active {
        transform: translateY(0);
      }

      .loading {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(8px);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
        opacity: 0;
        pointer-events: none;
        transition: var(--transition-normal);
      }

      .loading.active {
        opacity: 1;
        pointer-events: all;
      }

      .spinner {
        width: 48px;
        height: 48px;
        border: 4px solid rgba(67, 97, 238, 0.2);
        border-left: 4px solid var(--primary-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(10px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .hidden-date-input {
        position: absolute;
        visibility: hidden;
        pointer-events: none;
      }
    </style>
  </head>
  <body>
    <!-- 状态栏 -->
    <div id="status-bar-container"></div>

    <!-- 导航栏 -->
    <div class="navbar">
      <a href="home.html" class="navbar-back">
        <i class="fas fa-chevron-left"></i>
      </a>
      <div class="navbar-title">创建目标</div>
    </div>

    <!-- 内容区域 -->
    <div class="content-area">
      <div class="container">
        <form id="objective-form">
          <!-- 目标名称部分 -->
          <div class="form-section">
            <div class="section-label">目标名称</div>
            <div class="input-card">
              <div class="color-picker" id="color-trigger">
                <div class="color-circle" id="selected-color"></div>
                <div class="input-field">
                  <input type="text" id="title" class="input-text" placeholder="如"身体健康💪"" required>
                  <div class="input-hint">写一句激励自己的话吧！</div>
                </div>
              </div>
            </div>
          </div>

          <!-- 目标时间部分 -->
          <div class="form-section">
            <div class="section-label">目标时间</div>
            <div class="date-display" id="date-trigger">
              <i class="far fa-calendar-alt date-icon"></i>
              <div class="date-range" id="date-range-display">2025/5/13~2025/8/13</div>
            </div>

            <div class="date-details">
              <div class="date-card">
                <div class="date-label">开始日期</div>
                <div class="date-value" id="start-date-display">2025/5/13</div>
                <div class="date-day" id="start-day-display">周二</div>
                <input type="date" id="start-date" class="hidden-date-input" required />
              </div>
              <div class="date-card">
                <div class="date-label">结束日期</div>
                <div class="date-value" id="end-date-display">2025/8/13</div>
                <div class="date-day" id="end-day-display">周三</div>
                <input type="date" id="end-date" class="hidden-date-input" required />
              </div>
            </div>

            <!-- 持续时间部分 -->
            <div class="duration-container">
              <div class="duration-label">持续时间</div>
              <div class="duration-control">
                <button type="button" class="duration-btn minus" id="duration-minus">
                  <i class="fas fa-minus"></i>
                </button>
                <div class="duration-value" id="duration">持续 93 天</div>
                <button type="button" class="duration-btn plus" id="duration-plus">
                  <i class="fas fa-plus"></i>
                </button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- 颜色选择弹窗 -->
    <div class="overlay" id="overlay"></div>
    <div class="color-picker-modal" id="color-modal">
      <div class="modal-header">
        <div class="modal-title">选择颜色</div>
        <div class="modal-close" id="close-modal">
          <i class="fas fa-times"></i>
        </div>
      </div>
      <div class="color-grid" id="color-options">
        <div class="color-option selected" data-color="#4361ee" style="background-color: #4361ee"></div>
        <div class="color-option" data-color="#f72585" style="background-color: #f72585"></div>
        <div class="color-option" data-color="#4cc9f0" style="background-color: #4cc9f0"></div>
        <div class="color-option" data-color="#06d6a0" style="background-color: #06d6a0"></div>
        <div class="color-option" data-color="#ff9e00" style="background-color: #ff9e00"></div>
        <div class="color-option" data-color="#9b5de5" style="background-color: #9b5de5"></div>
        <div class="color-option" data-color="#2ec4b6" style="background-color: #2ec4b6"></div>
        <div class="color-option" data-color="#fd5d93" style="background-color: #fd5d93"></div>
        <div class="color-option" data-color="#38b000" style="background-color: #38b000"></div>
        <div class="color-option" data-color="#fb5607" style="background-color: #fb5607"></div>
        <div class="color-option" data-color="#3a86ff" style="background-color: #3a86ff"></div>
        <div class="color-option" data-color="#8338ec" style="background-color: #8338ec"></div>
        <div class="color-option" data-color="#ff006e" style="background-color: #ff006e"></div>
        <div class="color-option" data-color="#ffbe0b" style="background-color: #ffbe0b"></div>
        <div class="color-option" data-color="#1a1a2e" style="background-color: #1a1a2e"></div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="footer">
      <button type="button" class="create-btn" id="submit-btn">创建目标</button>
    </div>

    <div class="loading" id="loading">
      <div class="spinner"></div>
    </div>

    <script>
      document.addEventListener('DOMContentLoaded', function () {
        // 状态栏
        const statusBarContainer = document.getElementById('status-bar-container')
        if (statusBarContainer) {
          const statusBarTemplate = document.getElementById('ios-status-bar-template')
          if (statusBarTemplate) {
            statusBarContainer.appendChild(statusBarTemplate.content.cloneNode(true))
          }
        }

        // 设置默认日期值（开始日期为今天，结束日期为 93 天后）
        const today = new Date()
        const laterDate = new Date(today)
        laterDate.setDate(today.getDate() + 93)

        // 格式化日期为 yyyy/m/d
        const formatDateDisplay = (date) => {
          const year = date.getFullYear()
          const month = date.getMonth() + 1
          const day = date.getDate()
          return `${year}/${month}/${day}`
        }

        // 格式化日期为星期几
        const formatDayOfWeek = (date) => {
          const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
          return days[date.getDay()]
        }

        // 格式化日期为 yyyy-mm-dd (input 元素需要的格式)
        const formatDateValue = (date) => {
          const year = date.getFullYear()
          const month = String(date.getMonth() + 1).padStart(2, '0')
          const day = String(date.getDate()).padStart(2, '0')
          return `${year}-${month}-${day}`
        }

        // 设置初始日期
        document.getElementById('start-date').value = formatDateValue(today)
        document.getElementById('end-date').value = formatDateValue(laterDate)

        // 更新显示
        document.getElementById('start-date-display').textContent = formatDateDisplay(today)
        document.getElementById('end-date-display').textContent = formatDateDisplay(laterDate)
        document.getElementById('start-day-display').textContent = formatDayOfWeek(today)
        document.getElementById('end-day-display').textContent = formatDayOfWeek(laterDate)
        document.getElementById('date-range-display').textContent = `${formatDateDisplay(today)}~${formatDateDisplay(
          laterDate
        )}`

        // 更新日期显示信息函数
        function updateDateDisplay() {
          const startDate = new Date(document.getElementById('start-date').value)
          const endDate = new Date(document.getElementById('end-date').value)

          // 更新显示
          document.getElementById('start-date-display').textContent = formatDateDisplay(startDate)
          document.getElementById('end-date-display').textContent = formatDateDisplay(endDate)
          document.getElementById('start-day-display').textContent = formatDayOfWeek(startDate)
          document.getElementById('end-day-display').textContent = formatDayOfWeek(endDate)
          document.getElementById('date-range-display').textContent = `${formatDateDisplay(
            startDate
          )}~${formatDateDisplay(endDate)}`

          // 计算天数差异
          const diffTime = Math.abs(endDate - startDate)
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
          document.getElementById('duration').textContent = `持续${diffDays}天`
        }

        // 添加日期更改事件
        document.getElementById('start-date').addEventListener('change', function () {
          const startDate = new Date(this.value)
          const endDate = new Date(document.getElementById('end-date').value)

          // 如果开始日期大于结束日期，则更新结束日期
          if (startDate > endDate) {
            document.getElementById('end-date').value = this.value
          }

          updateDateDisplay()
        })

        document.getElementById('end-date').addEventListener('change', function () {
          const startDate = new Date(document.getElementById('start-date').value)
          const endDate = new Date(this.value)

          // 如果结束日期小于开始日期，则更新开始日期
          if (endDate < startDate) {
            document.getElementById('start-date').value = this.value
          }

          updateDateDisplay()
        })

        // 日期显示区域点击事件
        document.getElementById('date-trigger').addEventListener('click', function () {
          // 这里可以添加日期选择器弹窗
          const startDateInput = document.getElementById('start-date')
          // 模拟点击开始日期输入框
          startDateInput.showPicker()
        })

        // 持续时间加减按钮事件
        document.getElementById('duration-plus').addEventListener('click', function () {
          const startDate = new Date(document.getElementById('start-date').value)
          const endDate = new Date(document.getElementById('end-date').value)

          // 增加结束日期一天
          endDate.setDate(endDate.getDate() + 1)
          document.getElementById('end-date').value = formatDateValue(endDate)

          updateDateDisplay()
        })

        document.getElementById('duration-minus').addEventListener('click', function () {
          const startDate = new Date(document.getElementById('start-date').value)
          const endDate = new Date(document.getElementById('end-date').value)

          // 确保至少有一天的持续时间
          const diffTime = Math.abs(endDate - startDate)
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

          if (diffDays > 1) {
            // 减少结束日期一天
            endDate.setDate(endDate.getDate() - 1)
            document.getElementById('end-date').value = formatDateValue(endDate)

            updateDateDisplay()
          }
        })

        // 颜色选择器功能
        let selectedColor = '#4361ee'
        const colorTrigger = document.getElementById('color-trigger')
        const colorModal = document.getElementById('color-modal')
        const overlay = document.getElementById('overlay')
        const closeModal = document.getElementById('close-modal')
        const colorOptions = document.querySelectorAll('.color-option')
        const selectedColorCircle = document.getElementById('selected-color')

        // 初始化颜色圆圈
        selectedColorCircle.style.backgroundColor = selectedColor

        // 打开颜色选择器
        colorTrigger.addEventListener('click', function () {
          colorModal.classList.add('active')
          overlay.classList.add('active')
        })

        // 关闭颜色选择器
        closeModal.addEventListener('click', function () {
          colorModal.classList.remove('active')
          overlay.classList.remove('active')
        })

        overlay.addEventListener('click', function () {
          colorModal.classList.remove('active')
          overlay.classList.remove('active')
        })

        // 颜色选项点击事件
        colorOptions.forEach((option) => {
          option.addEventListener('click', function () {
            // 移除所有选项的选中状态
            document.querySelector('.color-option.selected').classList.remove('selected')

            // 设置当前选项为选中状态
            this.classList.add('selected')

            // 更新选中的颜色
            selectedColor = this.getAttribute('data-color')
            selectedColorCircle.style.backgroundColor = selectedColor

            // 更新界面中的颜色
            updateInterfaceColors(selectedColor)

            // 关闭颜色选择器
            setTimeout(() => {
              colorModal.classList.remove('active')
              overlay.classList.remove('active')
            }, 300)
          })
        })

        // 更新界面颜色
        function updateInterfaceColors(color) {
          // 更新 CSS 变量
          document.documentElement.style.setProperty('--primary-color', color)
          document.documentElement.style.setProperty('--primary-dark', adjustColorBrightness(color, -15))

          // 更新按钮颜色
          document.querySelector('.create-btn').style.backgroundColor = color
          document.querySelector('.create-btn').style.boxShadow = `0 4px 10px ${color}50`
        }

        // 辅助函数：调整颜色亮度
        function adjustColorBrightness(color, percent) {
          let R = parseInt(color.substring(1, 3), 16)
          let G = parseInt(color.substring(3, 5), 16)
          let B = parseInt(color.substring(5, 7), 16)

          R = Math.max(0, Math.min(255, Math.round(R + (R * percent) / 100)))
          G = Math.max(0, Math.min(255, Math.round(G + (G * percent) / 100)))
          B = Math.max(0, Math.min(255, Math.round(B + (B * percent) / 100)))

          const RR = R.toString(16).padStart(2, '0')
          const GG = G.toString(16).padStart(2, '0')
          const BB = B.toString(16).padStart(2, '0')

          return `#${RR}${GG}${BB}`
        }

        // 初始化界面颜色
        updateInterfaceColors(selectedColor)

        // 表单提交
        const form = document.getElementById('objective-form')
        const submitBtn = document.getElementById('submit-btn')

        // 创建按钮点击事件
        function handleSubmit() {
          // 先检查表单是否有效
          if (!form.checkValidity()) {
            // 触发浏览器的表单验证
            const submitEvent = new Event('submit', { cancelable: true })
            form.dispatchEvent(submitEvent)
            return
          }

          // 显示加载状态
          const loading = document.getElementById('loading')
          loading.classList.add('active')

          // 收集表单数据
          const objectiveData = {
            title: document.getElementById('title').value,
            startDate: document.getElementById('start-date').value,
            endDate: document.getElementById('end-date').value,
            color: selectedColor,
          }

          console.log('提交的目标数据：', objectiveData)

          // 这里可以添加保存到本地存储或发送到服务器的代码

          // 模拟延迟
          setTimeout(() => {
            // 隐藏加载状态
            loading.classList.remove('active')

            // 显示成功消息并跳转
            alert('目标创建成功！')
            window.location.href = 'home.html'
          }, 1000)
        }

        submitBtn.addEventListener('click', handleSubmit)

        form.addEventListener('submit', function (e) {
          e.preventDefault()
          handleSubmit()
        })

        // 接收主题变更消息
        window.addEventListener('message', function (event) {
          if (event.data && event.data.type === 'THEME_CHANGE') {
            applyTheme(event.data.theme)
          }
        })

        // 应用主题函数
        function applyTheme(theme) {
          // 更新 CSS 变量
          document.documentElement.style.setProperty('--primary-color', theme.primary)
          document.documentElement.style.setProperty('--primary-dark', adjustColorBrightness(theme.primary, -15))
          document.documentElement.style.setProperty('--primary-light', adjustColorBrightness(theme.primary, 20))

          // 更新颜色选择器
          const selectedColorCircle = document.getElementById('selected-color')
          if (selectedColorCircle) {
            selectedColorCircle.style.backgroundColor = theme.primary
            selectedColor = theme.primary
          }

          // 更新按钮颜色
          const createBtn = document.querySelector('.create-btn')
          if (createBtn) {
            createBtn.style.backgroundColor = theme.primary
            createBtn.style.boxShadow = `0 4px 10px ${theme.primary}50`
          }

          // 更新持续时间按钮
          const durationBtns = document.querySelectorAll('.duration-btn')
          durationBtns.forEach((btn) => {
            btn.style.backgroundColor = theme.primary
          })

          // 更新颜色选择器
          updateInterfaceColors(theme.primary)
        }
      })
    </script>
  </body>
</html>
