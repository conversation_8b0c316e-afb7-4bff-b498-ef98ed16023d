// 云对象教程：https://uniapp.dcloud.net.cn/uniCloud/cloud-obj
const { log } = require('console')
const uniID = require('uni-id-common')

// jsdoc 语法提示教程：https://ask.dcloud.net.cn/docs/#//ask.dcloud.net.cn/article/129
module.exports = {
  _before: function () {
    // 通用预处理器
    const clientInfo = this.getClientInfo()
    this.uniID = uniID.createInstance({
      // 创建 uni-id 实例，其上方法同 uniID
      clientInfo,
    })
  },
  /*
   * 清空所有表数据
   */
  // async reset(){
  // 	const httpInfo = this.getHttpInfo()
  // 	let { tableList } = JSON.parse(httpInfo.body)
  // 	tableList.forEach(tName =>{
  // 		db.collection("todo").remove()
  // 	})
  // }
  /**
   * method1 方法描述
   * @param {string} param1 参数 1 描述
   * @returns {object} 返回值描述
   */
  async sync() {
    const httpInfo = this.getHttpInfo()

    // uniIDIns.
    // TODO 保证 lastSyncTime 这个值传入是正确的，tableList 这个值优化传入方式，比如写死之类的
    let { syncData, lastSyncTime, tableList, t } = JSON.parse(httpInfo.body)

    try {
      const userInfo = await this.uniID.checkToken(t)
      console.log('userInfo')
      log(userInfo)
      if (userInfo.errCode !== 0) {
        // TODO 报错时，如何抛出错误让客户端处理
        return userInfo
        // throw new Error(userInfo)
      }

      const db = uniCloud.database()
      const dbCmd = db.command
      const nowTime = new Date().getTime() // 更新时间
      const serverData = {} // 服务端请求的数据
      if (!lastSyncTime) lastSyncTime = 0
      // 拉取服务端最新数据
      for (let tableName of tableList) {
        const res = await db
          .collection(tableName)
          .where({
            updateTime: dbCmd.gt(new Date(lastSyncTime).toISOString()).and(dbCmd.lte(new Date(nowTime).toISOString())),
            uid: userInfo.uid,
          })
          .limit(999)
          .get()
        if (res.data) {
          serverData[tableName] = res.data.map((e) => {
            const { uid, ...rest } = e
            return rest
          })
        }
      }

      console.log('请求的时间')
      console.log(lastSyncTime, nowTime)

      // 解决冲突
      Object.keys(serverData).forEach((table) => {
        serverData[table] = serverData[table].map((item) => {
          const id = item._id
          let localData = syncData[id]
          if (localData) {
            // 有冲突，对比更新时间
            if (localData.updateTime > item.updateTime) {
              return localData
            } else {
              localData = item
              return item
            }
          } else {
            // 无冲突
            return item
          }
        })
      })
      // 更新到服务器
      // TODO: 需要一次性更新
      // TODO: 需要处理更新失败问题
      // TODO: 删除逻辑需要处理一下
      // TODO: 数据库需要优化表结构跟索引
      // TODO: 使用事务锁定
      // TODO 当重复插入时报错应该如何处理，应该收集起来
      // TODO!!!!!!!!!!!!!! 如果插入有些正常，有些错误怎么处理？这个是个阻塞性的 bug，因为会导致从此无法同步
      const asyncFn = async (id) => {
        const local = syncData[id]
        const { isDirty, _id, ...item } = local.data
        return await db
          .collection(local.tableName)
          .doc(_id)
          .set({
            ...item,
            uid: userInfo.uid,
          })
      }
      // TODO Promise.allSettled 不支持
      await Promise.all(Object.keys(syncData).map((id) => asyncFn(id)))

      return {
        errCode: 0,
        lastSyncTime: nowTime,
        serverData,
      }
    } catch (e) {
      //TODO handle the exception
      console.log('handle the exception')
      console.error(JSON.stringify(e))
      return e
    }
  },
  async getHabits() {
    const db = uniCloud.database()
    const res = await db.collection('badHabit').where({}).limit(999).get()
    return res
  },
  async setHabits() {
    try {
      const httpInfo = this.getHttpInfo()
      const { _id, ...params } = JSON.parse(httpInfo.body)
      const db = uniCloud.database()
      await db
        .collection('badHabit')
        .doc(_id)
        .set({
          ...params,
        })
      return true
    } catch (e) {
      return false
    }
  },
  // 更新日记
  async updateDiary() {
    const httpInfo = this.getHttpInfo()
    const { _id, aiContent, tags } = JSON.parse(httpInfo.body)
    
    // 判断 aiContent 是否为空
    let params = {}
    if (!aiContent) {
      // aiContent 为空时，直接将 aiAnalysis 设为空字符串
      params = {
        aiAnalysis: '',
        updateTime: new Date().toISOString(),
      }
    } else {
      // aiContent 不为空时，按原逻辑处理
      const aiContentWithTagNames = JSON.parse(aiContent)
      const aiAnalysisObj = {}
      // 处理标签内容
      for (const tagName in aiContentWithTagNames) {
        const tag = tags.find((t) => t.content === tagName)
        if (tag) {
          aiAnalysisObj[tag._id] = aiContentWithTagNames[tagName]
        } else {
          aiAnalysisObj[tagName] = aiContentWithTagNames[tagName]
        }
      }
      params = {
        aiAnalysis: JSON.stringify(aiAnalysisObj),
        updateTime: new Date().toISOString(),
      }
    }
    
    const db = uniCloud.database()
    let checkResult = {}
    // 先查询是否存在该条数据
    if(_id){
      checkResult = await db.collection('memo').doc(_id).get()
    }
    let res
    if (checkResult.data && checkResult.data.length > 0) {
      // 数据存在，使用 update 更新
      res = await db
        .collection('memo')
        .doc(_id)
        .update({
          ...params,
        })
    } else {
      // 数据不存在，使用 add 添加
      res = await db.collection('memo').add({
        _id,
        ...params,
        uid: '68405fdc189f86d5e123fd1c',
        createTime: new Date().toISOString(),
      })
    }
    return res
  },
  // 通过 ID 查询日记数据
  async getDiaryById() {
    const httpInfo = this.getHttpInfo()
    const { _id } = JSON.parse(httpInfo.body)

    if (!_id) {
      return {
        code: -1,
        message: '缺少必要参数_id',
      }
    }

    try {
      const db = uniCloud.database()
      const result = await db.collection('memo').doc(_id).get()

      if (result.data && result.data.length > 0) {
        return {
          code: 0,
          data: result.data[0],
          message: '查询成功',
        }
      } else {
        return {
          code: -2,
          message: '未找到对应数据',
        }
      }
    } catch (error) {
      console.error('查询数据失败', error)
      return {
        code: -3,
        message: '查询数据失败',
        error: error.message,
      }
    }
  },
  // 更新周概览
  async updateMemo() {
    const httpInfo = this.getHttpInfo()
    const { _id, ...rest } = JSON.parse(httpInfo.body)

    let params = {
      ...rest,
      updateTime: new Date().toISOString(),
    }

    const db = uniCloud.database()
    let checkResult = {}
    if(_id){
      // 先查询是否存在该条数据
      checkResult = await db.collection('memo').doc(_id).get()
    }
    
    let res
    if (checkResult.data && checkResult.data.length > 0) {
      // 数据存在，使用 update 更新
      res = await db
        .collection('memo')
        .doc(_id)
        .update({
          ...params,
        })
    } else {
      // 数据不存在，使用 add 添加
      res = await db.collection('memo').add({
        _id,
        ...params,
        createTime: new Date().toISOString(),
        uid: '68405fdc189f86d5e123fd1c',
      })
    }
    return res
  },
  // 上传信息到云存储
  async uploadInfoToCloudStorage() {
    const httpInfo = this.getHttpInfo()
    const params = JSON.parse(httpInfo.body)
    const db = uniCloud.database()
    const res = await db.collection('transfer').add({
      ...params,
    })
    return res
  },
  // 获取信息列表
  async getInfoList() {
    const httpInfo = this.getHttpInfo()
    const { matchCode } = JSON.parse(httpInfo.body)
    const db = uniCloud.database()
    const res = await db.collection('transfer')
      .where({
        matchCode
      })
      .orderBy('createTime', 'desc')
      .limit(999)
      .get()
    return res
  }
}
