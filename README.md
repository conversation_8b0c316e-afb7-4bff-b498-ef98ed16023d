## 技术选型

- [Vue3](https://cn.vuejs.org/) Web 前端框架，组合式 API `<script setup lang="ts">`
- [Typescript](https://www.typescriptlang.org/) 在 JavaScript 的基础上添加静态类型。
- [uCharts](https://www.ucharts.cn/v2/#/guide/index) 图表组件
- [Vite3](https://cn.vitejs.dev/) 前端工具链
- [vk-uview-ui](https://vk uviewdoc.fsq.pub/) 支持 Vue3 的 uview 组件库
- [Pinia](https://pinia.vuejs.org/) 状态管理
- [VueUse](https://vueuse.org/) 基于 Composition API 的实用函数集合、hooks。
- [UnoCSS](https://github.com/antfu/unocss) 即时按需原子 CSS 引擎
- [ucharts](https://www.ucharts.cn/) 跨平台图表
- [unplugin-vue-components](https://github.com/antfu/unplugin-vue-components) 自动本地导入组件
- [Auto import](https://github.com/antfu/unplugin-auto-import) 自动导入常用 API，无需显式 `import { computed, ref } from 'vue'`
- [ESLint](https://eslint.org/) 支持保存自动代码格式化
- [Fastify](https://github.com/fastify/fastify) 使用 fastify 作为 mock api
- [husky](https://typicode.github.io/husky/#/) Git 提交 hooks, 自动执行脚本
  - [lint-staged](https://github.com/okonet/lint-staged) 提交时仅检查暂存区代码，代码检查通过才能提交
  - [commitlint](https://commitlint.js.org/#/) 提交规范检查，Commit message 需要符合规范才能提交
- 使用 px 作为尺寸单位，rpx 不支持动态横竖屏切换计算
- 封装分页 (userPager)、加载 (useLoading) 、scrollColor 等 hooks，提高复用

## 目录结构

```
├── .vscode          — VSCode 编辑器、代码片段、推荐扩展等
├── .husky           — 代码格式、提交规范自动检查
├── mock-api/        — 基于 Fastify 的极简 api mock
├── src/             — 前端源码
│   ├── api/         — api 接口
│   ├── components/  — 自定义组件
│   ├── hooks/       — 自定义 hooks
│   ├── pages/       — uniapp 页面
│   ├── plugins/     — 自定义 Vue3 插件
│   ├── static/      — 图片、公共样式
│   ├── store/       — 基于 Pinia 的状态管理
│   ├── uni_modules/ — 组件库
│   ├── utils/       — 常用工具函数
│   ├── config.ts    — 应用配置
│   ├── pages.json   — 页面路由
│   ├── auto-imports.d.ts — unplugin-auto-import 自动生成
│   ├── manifest.json     — uniapp 配置，如，小程序id、web 路由模式等
│   ├── uni.scss          — uniapp 样式变量
│   ├── permission.ts     — uniapp 拦截器
│   └── ...         — 其他.
├── .env            — 环境变量 (development 开发、production 生产)
├── vite.config     — vite 设置
├── unocss.config   — unocss 设置
├── package.json    — 依赖
└── ...             — 其他.
```

## 依赖

- [Node.js](https://nodejs.org/) >= V18
- [VS Code](https://code.visualstudio.com/) 及 [推荐扩展](.vscode/extensions.json)
- [pnpm](https://pnpm.io/)

## 快速开始

```bash
$ git clone https://github.com/aaron-zzh/uniapp-starter example
$ cd ./example
$ pnpm install                  # 安装依赖
$ pnpm run dev:h5               # H5 开发调试
$ pnpm run mock                 # 打开一个新终端运行，启动模拟接口
```

支持 VSCode F5 一键启动 H5 开发模式

## 部署

打包前确认各项参数配置、环境变量

```bash
$ pnpm run build:h5               # H5 打包到 ./dist/build/h5
```

## 参考

- [Vue3 开发指南](https://cn.vuejs.org/guide/introduction.html)
- [UnoCSS 工具](https://uno.antfu.me/)
- [uview 组件库 v1](https://v1.uviewui.com/)
- [uniapp 官网](https://uniapp.dcloud.net.cn/)

## 升级

```bash
$ npx @dcloudio/uvm   # 升级 uniapp
$ pnpm update         # 升级所有依赖的版本
```

# 发布准备工作

- 换包名
- 换 sqlite 数据库名

# 魔改的 uni 包

- uni-collapse 折叠面板

# 开发文档

- uni-id 的云端配置文件在 uniCloud/cloudfunctions/common/uni-config-center/uni-id/config.json 中

# 备注

云函数 uni-stat-cron 6 月 21 关闭，原定时 "8 22 \* \* \* \*"

// autocorrect: false 忽略空格

说法萨芬

## 单元测试

本项目包含 OKR 功能的单元测试代码，位于`tests`目录下。

### 运行测试

在运行测试前，需要先安装测试依赖：

```bash
npm install
```

运行所有测试：

```bash
npm run test
```

监视模式（修改代码后自动运行测试）：

```bash
npm run test:watch
```

生成测试覆盖率报告：

```bash
npm run test:coverage
```

### 测试文件结构

- `tests/unit/example.test.js` - 验证 Jest 环境的基本测试
- `tests/unit/okr.test.js` - OKR API 测试
- `tests/unit/okrEdit.test.js` - OKR 编辑页面测试
- `tests/unit/okrDetail.test.js` - OKR 详情页面测试

### 测试框架

项目使用以下测试工具：

- Jest: JavaScript 测试框架
- Vue Test Utils: Vue 组件测试工具
- JSDOM: 浏览器环境模拟

## 循环任务实现逻辑

循环任务是系统中的一种特殊任务类型，通过重复规则 (repeatFlag) 实现定期执行的功能。其实现逻辑如下：

### 任务展示逻辑

- 系统会读取当天的所有任务
- 同时读取所有满足 startDate <= 当天 <= endDate 条件的任务
- 根据任务的 repeatFlag 字段 (RRULE 格式) 判断当天是否应该展示该循环任务

### 任务完成记录

当用户完成一个循环任务时：

- 系统会在任务的 compInfos 字段中添加一条记录
- 记录类型为 TaskRec，包含以下属性：
  - key: 4 位随机数，作为该次完成记录的唯一标识
  - val: 量化值
  - compTime: 完成时间，对应 repeatFlag 计算出的当次任务时间
  - updateTime: 更新时间
  - remark: 备注信息 (可选)

### 关键结果 (KR) 进度记录

- 当循环任务关联到 KR(关键结果) 时，系统会生成对应的进度记录
- 进度记录保存在 RecList 中
- 如果 RecList 记录是由重复任务产生，则其 taskId 的构成为：循环母任务 ID + "-" + TaskRec.key
- 这种设计确保了每次循环任务完成都能正确关联到对应的进度记录

### 数据结构

循环任务主要涉及以下数据结构：
- Task: 包含 repeatFlag、compInfos 等字段
- TaskRec: 循环任务的完成记录
- RecList: 量化任务的进度记录
