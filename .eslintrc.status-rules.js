/**
 * ESLint 自定义规则：检查状态值使用
 * 确保代码中使用的状态值来自统一的常量定义
 */

module.exports = {
  rules: {
    // 禁止硬编码OKR状态值
    'no-hardcoded-okr-status': {
      meta: {
        type: 'error',
        docs: {
          description: '禁止硬编码OKR状态值，应使用 OKR_STATUS 常量',
          category: 'Best Practices',
          recommended: true
        },
        fixable: 'code',
        schema: []
      },
      create(context) {
        const hardcodedOkrStatuses = [
          'pending', 'inProgress', 'completed', 'paused', 'abandoned'
        ]
        
        return {
          Literal(node) {
            if (typeof node.value === 'string' && 
                hardcodedOkrStatuses.includes(node.value)) {
              
              // 检查是否在状态比较或赋值中使用
              const parent = node.parent
              if (parent && (
                parent.type === 'BinaryExpression' ||
                parent.type === 'AssignmentExpression' ||
                parent.type === 'Property'
              )) {
                context.report({
                  node,
                  message: `不要硬编码OKR状态值 "${node.value}"，请使用 OKR_STATUS.${node.value.toUpperCase()}`,
                  fix(fixer) {
                    const statusConstant = `OKR_STATUS.${node.value.toUpperCase()}`
                    return fixer.replaceText(node, statusConstant)
                  }
                })
              }
            }
          },
          
          TemplateLiteral(node) {
            // 检查模板字符串中的状态值
            node.quasis.forEach(quasi => {
              hardcodedOkrStatuses.forEach(status => {
                if (quasi.value.raw.includes(`"${status}"`)) {
                  context.report({
                    node: quasi,
                    message: `模板字符串中不要硬编码OKR状态值 "${status}"，请使用 OKR_STATUS.${status.toUpperCase()}`
                  })
                }
              })
            })
          }
        }
      }
    },

    // 禁止硬编码任务状态值
    'no-hardcoded-task-status': {
      meta: {
        type: 'error',
        docs: {
          description: '禁止硬编码任务状态值，应使用 TASK_STATUS 常量',
          category: 'Best Practices',
          recommended: true
        },
        fixable: 'code',
        schema: []
      },
      create(context) {
        return {
          BinaryExpression(node) {
            // 检查 status === 0, status === 1, status === 2 这样的比较
            if (node.operator === '===' || node.operator === '!==') {
              const left = node.left
              const right = node.right
              
              if (left.type === 'MemberExpression' && 
                  left.property && left.property.name === 'status' &&
                  right.type === 'Literal' && 
                  typeof right.value === 'number' &&
                  [0, 1, 2].includes(right.value)) {
                
                const statusMap = {
                  0: 'INCOMPLETE',
                  1: 'COMPLETED', 
                  2: 'ABANDONED'
                }
                
                context.report({
                  node: right,
                  message: `不要硬编码任务状态值 ${right.value}，请使用 TASK_STATUS.${statusMap[right.value]}`,
                  fix(fixer) {
                    return fixer.replaceText(right, `TASK_STATUS.${statusMap[right.value]}`)
                  }
                })
              }
            }
          }
        }
      }
    },

    // 要求导入状态常量
    'require-status-constants': {
      meta: {
        type: 'warning',
        docs: {
          description: '在使用状态值的文件中要求导入状态常量',
          category: 'Best Practices',
          recommended: true
        },
        schema: []
      },
      create(context) {
        let hasStatusImport = false
        let usesStatusValues = false
        
        return {
          ImportDeclaration(node) {
            if (node.source.value === '@/constants/status') {
              hasStatusImport = true
            }
          },
          
          Literal(node) {
            if (typeof node.value === 'string' && 
                ['pending', 'inProgress', 'completed', 'paused', 'abandoned'].includes(node.value)) {
              usesStatusValues = true
            }
          },
          
          'Program:exit'() {
            if (usesStatusValues && !hasStatusImport) {
              context.report({
                node: context.getSourceCode().ast,
                message: '文件中使用了状态值，但未导入状态常量。请添加: import { OKR_STATUS, TASK_STATUS } from "@/constants/status"'
              })
            }
          }
        }
      }
    }
  }
}
