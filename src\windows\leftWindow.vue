<template>
  <view class="main-container">
    <l-side-menu @toggle="handleToggle" @menu-change="handleMenuChange" />
    <view class="content-container" :class="{ collapsed: hideContent }">
      <component :is="currentComponent" />
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import lOkrMenu from './components/l-okr-menu.vue'
import lBillMenu from './components/l-bill-menu.vue'
import lMemoMenu from './components/l-memo-menu.vue'
import lWeeklyMenu from './components/l-weekly-menu.vue'
import lSpeakMenu from './components/l-speak-menu.vue'

// 是否隐藏内容区域
const hideContent = ref(false)

const activeMenu = ref('okr')

const currentComponent = computed(() => {
  switch (activeMenu.value) {
    case 'okr':
      return lOkrMenu
    case 'bill':
      return lBillMenu
    case 'memo':
      return lMemoMenu
    case 'weekly':
      return lWeeklyMenu
    case 'speak':
      return lSpeakMenu
    default:
      return lOkrMenu
  }
})

// 处理菜单切换
const handleMenuChange = (menu) => {
  console.log('菜单改变', menu)
  activeMenu.value = menu.id
  hideContent.value = menu.collapsed
  updateLeftWindowWidth()
}

// 处理折叠/展开内容区域
const handleToggle = (collapsed) => {
  hideContent.value = collapsed
  updateLeftWindowWidth()
}

const updateLeftWindowWidth = () => {
  const leftWindow = document.querySelector('uni-left-window')
  if (leftWindow) {
    leftWindow.style.width = hideContent.value ? '60px' : '350px'
  }
}
</script>

<style lang="scss" scoped>
.main-container {
  display: flex;
  height: 100vh;
  width: 100%;
  background-color: #fff;
}

.content-container {
  flex: 1;
  padding: 20rpx;
  overflow-y: auto;
  background-color: #fafafa;
  border-radius: 20rpx 0 0 20rpx;
  margin: 10rpx 10rpx 10rpx 0;
  box-shadow: -5rpx 0 15rpx rgba(0, 0, 0, 0.05);

  &.collapsed {
    flex: 0;
    width: 0;
    padding: 0;
    margin: 0;
    overflow: hidden;
  }
}

.menu-item {
  @apply flex justify-center items-center p-[6px] rounded-4px cursor-pointer hover:bg-blue hover:color-white mb-6px;
}

.active-tab {
  background-color: #64b6f7 !important;
  color: white !important;
  transition: all 0.3s ease;
}

.menu-item:hover {
  transform: scale(1.1);
}

.rotating {
  animation: rotate 1s linear infinite;
}
</style>
