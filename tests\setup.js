// Jest setup file
const { jest } = require('@jest/globals')

// Mock global objects that might be used in tests
global.jest = jest

// 全局设置uni-app相关API
global.uni = {
  showToast: jest.fn(),
  showModal: jest.fn(),
  navigateTo: jest.fn(),
  navigateBack: jest.fn(),
  redirectTo: jest.fn(),
  switchTab: jest.fn(),
  reLaunch: jest.fn(),
}

// 模拟DOM API
global.document = {
  getElementById: jest.fn().mockReturnValue({
    style: {},
  }),
  documentElement: {
    style: {
      setProperty: jest.fn(),
    },
  },
}

// Clear mocks between tests
beforeEach(() => {
  jest.clearAllMocks()
})
