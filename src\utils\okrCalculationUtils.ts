/**
 * OKR进度计算工具函数
 */

/**
 * 计算单个Key Result的进度百分比
 * @param curVal 当前值
 * @param tgtVal 目标值
 * @param initVal 初始值（默认为0）
 * @returns 进度百分比（0-100之间）
 */
export const calculateKrProgress = (curVal: number, tgtVal: number, initVal = 0): number => {
  if (tgtVal === initVal) return 0 // 避免除以零
  const progress = ((curVal - initVal) / (tgtVal - initVal)) * 100
  return Math.min(Math.max(0, Math.round(progress)), 100) // 限制在0-100之间
}

/**
 * 基于权重计算OKR的整体进度
 * @param keyResults 关键结果数组 {curVal, tgtVal, weight}
 * @returns 加权平均的进度百分比（0-100之间）
 */
export const calculateWeightedProgress = (
  keyResults: Array<{ curVal: number; tgtVal: number; weight?: number; initVal?: number }>
): number => {
  if (!keyResults || keyResults.length === 0) return 0

  let totalProgress = 0
  let totalWeight = 0

  keyResults.forEach((kr) => {
    // 使用相对权重，默认为1
    const weight = kr.weight || 1
    const progress = calculateKrProgress(kr.curVal, kr.tgtVal, kr.initVal || 0)

    totalProgress += progress * weight
    totalWeight += weight
  })

  // 避免除以零
  if (totalWeight === 0) return 0

  return Math.round(totalProgress / totalWeight)
}
