# P0 核心优化技术实现方案

## 📋 方案概述

### 实施目标

- **状态管理简化**：将复杂的 10 种 SSE 消息类型简化为 4 种核心状态
- **错误处理优化**：建立用户友好的统一错误处理体系
- **代码量减少**：前端状态管理代码减少 50%以上
- **用户体验提升**：错误理解率提升至 90%以上

### 技术原则

- **奥卡姆剃刀**：用最简洁的方案解决复杂问题
- **向后兼容**：直接替换现有实现，无需考虑兼容性
- **用户优先**：所有技术决策以用户体验为导向

## 🔧 1. 状态管理简化方案

### 1.1 现状分析

**当前问题**：

- 10 种 SSE 消息类型：`task_created`, `task_updated`, `ai_thinking`, `tool_executing`, `error_occurred`, `progress_update`, `completion`, `suggestion`, `context_loaded`, `session_ended`
- 前端 aiState 对象过于庞大（150+行）
- 状态转换逻辑复杂，调试困难

### 1.2 简化状态模型设计

**新状态架构**：

```javascript
// 简化的4种核心状态
const SIMPLE_STATES = {
  IDLE: 'idle', // 空闲状态 - 等待用户输入
  THINKING: 'thinking', // AI思考中 - 理解用户需求
  EXECUTING: 'executing', // 工具执行中 - 创建/更新任务
  RESPONDING: 'responding', // 生成回复中 - 准备反馈信息
}

// 简化的状态管理对象
const aiState = {
  status: 'idle', // 当前状态
  message: '', // 状态描述信息
  progress: null, // 进度信息（0-100或null）
  sessionId: null, // 会话ID
  error: null, // 错误信息对象
}
```

### 1.3 状态映射规则

**旧状态 → 新状态映射**：

```javascript
const STATE_MAPPING = {
  // 思考阶段
  ai_thinking: 'thinking',
  context_loaded: 'thinking',

  // 执行阶段
  task_created: 'executing',
  task_updated: 'executing',
  tool_executing: 'executing',
  progress_update: 'executing',

  // 响应阶段
  completion: 'responding',
  suggestion: 'responding',

  // 空闲阶段
  session_ended: 'idle',

  // 错误处理
  error_occurred: 'idle', // 错误后回到空闲状态
}
```

### 1.4 界面交互设计

**状态指示器组件**：

```vue
<template>
  <div class="ai-status-indicator">
    <!-- 状态图标和文字 -->
    <div class="status-display" :class="statusClass">
      <i :class="statusIcon" class="status-icon"></i>
      <span class="status-text">{{ statusMessage }}</span>
    </div>

    <!-- 进度条（仅在executing状态显示） -->
    <div v-if="showProgress" class="progress-bar">
      <div class="progress-fill" :style="{ width: progressWidth }"></div>
    </div>

    <!-- 错误信息（仅在有错误时显示） -->
    <div v-if="hasError" class="error-display">
      <i class="fas fa-exclamation-triangle error-icon"></i>
      <span class="error-message">{{ errorMessage }}</span>
      <button @click="retryAction" class="retry-button">重试</button>
    </div>
  </div>
</template>
```

**状态视觉设计**：

- **IDLE**: 灰色圆点 + "等待输入..."
- **THINKING**: 蓝色旋转图标 + "正在理解您的需求..."
- **EXECUTING**: 绿色进度条 + "正在创建任务..." + 进度百分比
- **RESPONDING**: 橙色打字图标 + "正在生成回复..."

### 1.5 实现思路

**前端状态管理重构**：

```javascript
// 1. 简化状态管理类
class SimpleAIStateManager {
  constructor() {
    this.state = {
      status: 'idle',
      message: '',
      progress: null,
      sessionId: null,
      error: null,
    }
    this.listeners = []
  }

  // 状态更新方法
  updateStatus(newStatus, message = '', progress = null) {
    this.state.status = newStatus
    this.state.message = message
    this.state.progress = progress
    this.state.error = null
    this.notifyListeners()
  }

  // 错误处理方法
  setError(error) {
    this.state.status = 'idle'
    this.state.error = error
    this.state.progress = null
    this.notifyListeners()
  }

  // 重置状态
  reset() {
    this.state = {
      status: 'idle',
      message: '',
      progress: null,
      sessionId: null,
      error: null,
    }
    this.notifyListeners()
  }
}

// 2. SSE消息处理简化
class SimpleSSEHandler {
  constructor(stateManager) {
    this.stateManager = stateManager
  }

  handleMessage(event) {
    const data = JSON.parse(event.data)
    const newStatus = STATE_MAPPING[data.type] || 'idle'

    switch (newStatus) {
      case 'thinking':
        this.stateManager.updateStatus('thinking', '正在理解您的需求...')
        break
      case 'executing':
        this.stateManager.updateStatus('executing', '正在创建任务...', data.progress)
        break
      case 'responding':
        this.stateManager.updateStatus('responding', '正在生成回复...')
        break
      case 'idle':
        if (data.type === 'error_occurred') {
          this.stateManager.setError(data.error)
        } else {
          this.stateManager.updateStatus('idle', '完成')
        }
        break
    }
  }
}
```

**后端 SSE 消息简化**：

```javascript
// 后端只发送4种核心状态消息
class SimplifiedSSEEmitter {
  emitThinking(sessionId, message = '正在理解您的需求...') {
    this.emit(sessionId, {
      type: 'thinking',
      message,
      timestamp: Date.now(),
    })
  }

  emitExecuting(sessionId, message = '正在创建任务...', progress = null) {
    this.emit(sessionId, {
      type: 'executing',
      message,
      progress,
      timestamp: Date.now(),
    })
  }

  emitResponding(sessionId, message = '正在生成回复...') {
    this.emit(sessionId, {
      type: 'responding',
      message,
      timestamp: Date.now(),
    })
  }

  emitComplete(sessionId, result) {
    this.emit(sessionId, {
      type: 'complete',
      result,
      timestamp: Date.now(),
    })
  }
}
```

## 🚨 2. 错误处理优化方案

### 2.1 现状分析

**当前问题**：

- 错误信息技术化：`NetworkError: fetch failed`
- 错误类型过多：网络错误、认证错误、解析错误、系统错误等
- 缺少用户友好的错误恢复机制

### 2.2 统一错误分类设计

**错误分类体系**：

```javascript
const USER_FRIENDLY_ERRORS = {
  NETWORK: {
    code: 'NETWORK_ERROR',
    message: '网络连接不稳定，请检查网络后重试',
    action: 'retry',
    icon: 'fas fa-wifi',
    color: 'orange',
    autoRetry: true,
    retryDelay: 3000,
  },
  AUTH: {
    code: 'AUTH_ERROR',
    message: '登录已过期，请重新登录',
    action: 'login',
    icon: 'fas fa-lock',
    color: 'red',
    autoRetry: false,
  },
  PARSE: {
    code: 'PARSE_ERROR',
    message: '我没理解您的意思，能换个说法吗？',
    action: 'rephrase',
    icon: 'fas fa-question-circle',
    color: 'blue',
    autoRetry: false,
  },
  SYSTEM: {
    code: 'SYSTEM_ERROR',
    message: '系统暂时繁忙，请稍后重试',
    action: 'retry',
    icon: 'fas fa-exclamation-triangle',
    color: 'red',
    autoRetry: true,
    retryDelay: 5000,
  },
  QUOTA: {
    code: 'QUOTA_ERROR',
    message: '今日使用次数已达上限，请明天再试',
    action: 'upgrade',
    icon: 'fas fa-hourglass-end',
    color: 'purple',
    autoRetry: false,
  },
}
```

### 2.3 界面交互设计

**错误提示组件**：

```vue
<template>
  <div v-if="error" class="error-container" :class="`error-${error.color}`">
    <!-- 错误图标和信息 -->
    <div class="error-content">
      <i :class="error.icon" class="error-icon"></i>
      <div class="error-text">
        <h4 class="error-title">{{ error.message }}</h4>
        <p v-if="error.suggestion" class="error-suggestion">{{ error.suggestion }}</p>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="error-actions">
      <button
        v-if="error.action === 'retry'"
        @click="handleRetry"
        class="action-button retry-button"
        :disabled="retrying"
      >
        <i class="fas fa-redo" :class="{ 'fa-spin': retrying }"></i>
        {{ retrying ? '重试中...' : '重试' }}
      </button>

      <button v-if="error.action === 'login'" @click="handleLogin" class="action-button login-button">
        <i class="fas fa-sign-in-alt"></i>
        重新登录
      </button>

      <button v-if="error.action === 'rephrase'" @click="handleRephrase" class="action-button rephrase-button">
        <i class="fas fa-edit"></i>
        换个说法
      </button>

      <button @click="dismissError" class="action-button dismiss-button">
        <i class="fas fa-times"></i>
        关闭
      </button>
    </div>

    <!-- 自动重试倒计时 -->
    <div v-if="error.autoRetry && countdown > 0" class="auto-retry-countdown">{{ countdown }}秒后自动重试...</div>
  </div>
</template>
```

### 2.4 实现思路

**错误处理管理器**：

```javascript
class ErrorHandler {
  constructor() {
    this.errorMappings = new Map()
    this.setupErrorMappings()
  }

  // 设置错误映射规则
  setupErrorMappings() {
    // 网络错误映射
    this.errorMappings.set(/network|fetch|connection/i, 'NETWORK')
    this.errorMappings.set(/timeout/i, 'NETWORK')

    // 认证错误映射
    this.errorMappings.set(/401|unauthorized|token/i, 'AUTH')

    // 解析错误映射
    this.errorMappings.set(/parse|understand|invalid/i, 'PARSE')

    // 配额错误映射
    this.errorMappings.set(/quota|limit|exceeded/i, 'QUOTA')

    // 系统错误映射（默认）
    this.errorMappings.set(/.*/, 'SYSTEM')
  }

  // 处理错误
  handleError(originalError) {
    const errorType = this.classifyError(originalError)
    const userFriendlyError = USER_FRIENDLY_ERRORS[errorType]

    return {
      ...userFriendlyError,
      originalError,
      timestamp: Date.now(),
      suggestion: this.generateSuggestion(errorType, originalError),
    }
  }

  // 错误分类
  classifyError(error) {
    const errorMessage = error.message || error.toString()

    for (let [pattern, type] of this.errorMappings) {
      if (pattern.test(errorMessage)) {
        return type
      }
    }

    return 'SYSTEM' // 默认系统错误
  }

  // 生成建议
  generateSuggestion(errorType, originalError) {
    switch (errorType) {
      case 'NETWORK':
        return '请检查网络连接，或尝试刷新页面'
      case 'AUTH':
        return '您的登录状态已过期，需要重新登录'
      case 'PARSE':
        return '尝试用更具体的描述，比如"明天上午开会讨论产品规划"'
      case 'QUOTA':
        return '升级到高级版本可获得更多使用次数'
      default:
        return '如果问题持续存在，请联系技术支持'
    }
  }
}
```

**自动重试机制**：

```javascript
class AutoRetryManager {
  constructor(errorHandler) {
    this.errorHandler = errorHandler
    this.retryAttempts = new Map()
    this.maxRetries = 3
  }

  async executeWithRetry(operation, context) {
    const operationId = this.generateOperationId(context)
    let attempts = this.retryAttempts.get(operationId) || 0

    try {
      const result = await operation()
      this.retryAttempts.delete(operationId) // 成功后清除重试记录
      return result
    } catch (error) {
      const processedError = this.errorHandler.handleError(error)

      // 检查是否可以自动重试
      if (processedError.autoRetry && attempts < this.maxRetries) {
        attempts++
        this.retryAttempts.set(operationId, attempts)

        // 等待重试延迟
        await this.delay(processedError.retryDelay)

        // 递归重试
        return this.executeWithRetry(operation, context)
      } else {
        // 达到最大重试次数或不可重试，抛出处理后的错误
        throw processedError
      }
    }
  }

  delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }

  generateOperationId(context) {
    return `${context.type}_${context.sessionId}_${Date.now()}`
  }
}
```

## 🚀 3. 实施计划

### 3.1 第一周：状态管理简化

- **Day 1-2**: 设计新状态模型，创建 SimpleAIStateManager 类
- **Day 3-4**: 重构前端状态管理，更新 UI 组件
- **Day 5**: 后端 SSE 消息简化，测试状态转换
- **Day 6-7**: 集成测试，修复问题

### 3.2 第二周：错误处理优化

- **Day 1-2**: 实现 ErrorHandler 和错误分类体系
- **Day 3-4**: 创建用户友好的错误 UI 组件
- **Day 5**: 实现自动重试机制
- **Day 6-7**: 端到端测试，用户体验验证

### 3.3 验收标准

- **代码量减少**: 前端状态管理代码减少 50%以上
- **状态清晰度**: 用户能清楚理解当前系统状态
- **错误理解率**: 用户对错误信息理解率达到 90%以上
- **自动恢复率**: 网络等临时错误自动恢复率达到 80%以上

## 📊 4. 风险评估与缓解

### 4.1 技术风险

- **状态丢失风险**: 简化过程中可能丢失重要状态信息
- **兼容性风险**: 新状态模型与现有功能的兼容性

### 4.2 缓解措施

- **渐进式迁移**: 先在测试环境验证，再逐步上线
- **状态映射验证**: 确保所有旧状态都有对应的新状态映射
- **回滚方案**: 准备快速回滚到旧版本的方案

## 🏗️ 5. 架构设计详解

### 5.1 整体架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户界面层     │    │   状态管理层     │    │   错误处理层     │
│                │    │                │    │                │
│ StatusIndicator │◄──►│SimpleStateManager│◄──►│  ErrorHandler   │
│ ErrorDisplay    │    │ SSEHandler      │    │ AutoRetryManager│
│ TaskInput       │    │ StateStore      │    │ ErrorMapper     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   后端服务层     │
                    │                │
                    │ SimplifiedSSE   │
                    │ TaskService     │
                    │ ErrorLogger     │
                    └─────────────────┘
```

### 5.2 前端核心组件设计

**主要组件结构**：

```vue
<!-- TaskManager.vue - 主组件 -->
<template>
  <div class="task-manager">
    <!-- 状态指示器 -->
    <AIStatusIndicator :state="aiState" @retry="handleRetry" @dismiss-error="dismissError" />

    <!-- 任务输入区 -->
    <TaskInput :disabled="aiState.status !== 'idle'" @submit="handleTaskSubmit" @clear="clearInput" />

    <!-- 任务列表 -->
    <TaskList :tasks="tasks" :loading="aiState.status === 'executing'" />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useAIState } from '@/composables/useAIState'
import { useErrorHandler } from '@/composables/useErrorHandler'

const { aiState, updateState, resetState } = useAIState()
const { handleError, dismissError } = useErrorHandler()

// 任务提交处理
const handleTaskSubmit = async (taskDescription) => {
  try {
    updateState('thinking', '正在理解您的需求...')
    await submitTask(taskDescription)
  } catch (error) {
    handleError(error)
  }
}
</script>
```

### 5.3 状态管理 Composable

**useAIState.js**：

```javascript
import { ref, reactive } from 'vue'

const globalState = reactive({
  status: 'idle',
  message: '',
  progress: null,
  sessionId: null,
  error: null,
  lastUpdate: null,
})

export function useAIState() {
  const updateState = (status, message = '', progress = null) => {
    globalState.status = status
    globalState.message = message
    globalState.progress = progress
    globalState.error = null
    globalState.lastUpdate = Date.now()

    // 状态变化日志
    console.log(`[AIState] ${status}: ${message}`, { progress })
  }

  const setError = (error) => {
    globalState.status = 'idle'
    globalState.error = error
    globalState.progress = null
    globalState.lastUpdate = Date.now()

    console.error('[AIState] Error:', error)
  }

  const resetState = () => {
    Object.assign(globalState, {
      status: 'idle',
      message: '',
      progress: null,
      sessionId: null,
      error: null,
      lastUpdate: Date.now(),
    })
  }

  return {
    aiState: globalState,
    updateState,
    setError,
    resetState,
  }
}
```

### 5.4 SSE 连接管理

**SSEManager.js**：

```javascript
class SSEManager {
  constructor() {
    this.eventSource = null
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectDelay = 1000
    this.listeners = new Map()
  }

  connect(sessionId) {
    if (this.eventSource) {
      this.disconnect()
    }

    const url = `/api/sse/connect?sessionId=${sessionId}`
    this.eventSource = new EventSource(url)

    this.eventSource.onopen = () => {
      console.log('[SSE] Connected')
      this.reconnectAttempts = 0
    }

    this.eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        this.handleMessage(data)
      } catch (error) {
        console.error('[SSE] Parse error:', error)
      }
    }

    this.eventSource.onerror = (error) => {
      console.error('[SSE] Connection error:', error)
      this.handleReconnect()
    }
  }

  handleMessage(data) {
    const { type, message, progress, error } = data

    // 触发状态更新
    const listeners = this.listeners.get('message') || []
    listeners.forEach((callback) => callback({ type, message, progress, error }))
  }

  handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1)

      setTimeout(() => {
        console.log(`[SSE] Reconnecting... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
        this.connect(this.currentSessionId)
      }, delay)
    } else {
      console.error('[SSE] Max reconnection attempts reached')
      this.notifyConnectionLost()
    }
  }

  addEventListener(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event).push(callback)
  }

  disconnect() {
    if (this.eventSource) {
      this.eventSource.close()
      this.eventSource = null
    }
  }
}
```

## 🎨 6. UI/UX 设计规范

### 6.1 状态视觉设计

**状态颜色系统**：

```css
:root {
  /* 状态颜色 */
  --status-idle: #6b7280; /* 灰色 - 等待状态 */
  --status-thinking: #3b82f6; /* 蓝色 - 思考状态 */
  --status-executing: #10b981; /* 绿色 - 执行状态 */
  --status-responding: #f59e0b; /* 橙色 - 响应状态 */

  /* 错误颜色 */
  --error-network: #f97316; /* 橙色 - 网络错误 */
  --error-auth: #ef4444; /* 红色 - 认证错误 */
  --error-parse: #3b82f6; /* 蓝色 - 解析错误 */
  --error-system: #dc2626; /* 深红 - 系统错误 */
  --error-quota: #8b5cf6; /* 紫色 - 配额错误 */
}

/* 状态指示器样式 */
.ai-status-indicator {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 8px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.status-icon {
  margin-right: 8px;
  font-size: 16px;
}

.status-thinking .status-icon {
  animation: spin 1s linear infinite;
  color: var(--status-thinking);
}

.status-executing .status-icon {
  animation: pulse 1.5s ease-in-out infinite;
  color: var(--status-executing);
}

/* 进度条样式 */
.progress-bar {
  width: 100%;
  height: 4px;
  background: var(--bg-tertiary);
  border-radius: 2px;
  overflow: hidden;
  margin-top: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--status-executing), #34d399);
  border-radius: 2px;
  transition: width 0.3s ease;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: 200px 0;
  }
}
```

### 6.2 错误提示设计

**错误提示组件样式**：

```css
.error-container {
  display: flex;
  flex-direction: column;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid;
  background: var(--bg-error);
  margin: 12px 0;
  animation: slideIn 0.3s ease-out;
}

.error-network {
  border-left-color: var(--error-network);
}
.error-auth {
  border-left-color: var(--error-auth);
}
.error-parse {
  border-left-color: var(--error-parse);
}
.error-system {
  border-left-color: var(--error-system);
}
.error-quota {
  border-left-color: var(--error-quota);
}

.error-content {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
}

.error-icon {
  margin-right: 12px;
  font-size: 20px;
  margin-top: 2px;
}

.error-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: var(--text-primary);
}

.error-suggestion {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.4;
}

.error-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-button {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.retry-button {
  background: var(--status-executing);
  color: white;
}

.retry-button:hover {
  background: #059669;
  transform: translateY(-1px);
}

.retry-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
```

## 🔧 7. 后端实现方案

### 7.1 简化 SSE 服务设计

**后端 SSE 控制器**：

```javascript
// SSEController.js
class SSEController {
  constructor() {
    this.connections = new Map() // sessionId -> response对象
    this.stateManager = new StateManager()
  }

  // 建立SSE连接
  async connect(req, res) {
    const { sessionId } = req.query

    // 设置SSE响应头
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      Connection: 'keep-alive',
      'Access-Control-Allow-Origin': '*',
    })

    // 保存连接
    this.connections.set(sessionId, res)

    // 发送连接确认
    this.sendMessage(sessionId, {
      type: 'connected',
      message: '连接已建立',
      timestamp: Date.now(),
    })

    // 处理连接断开
    req.on('close', () => {
      this.connections.delete(sessionId)
      console.log(`[SSE] Session ${sessionId} disconnected`)
    })
  }

  // 发送消息
  sendMessage(sessionId, data) {
    const connection = this.connections.get(sessionId)
    if (connection) {
      const message = `data: ${JSON.stringify(data)}\n\n`
      connection.write(message)
    }
  }

  // 发送状态更新
  sendStateUpdate(sessionId, status, message, progress = null) {
    this.sendMessage(sessionId, {
      type: 'state_update',
      status,
      message,
      progress,
      timestamp: Date.now(),
    })
  }

  // 发送错误信息
  sendError(sessionId, error) {
    this.sendMessage(sessionId, {
      type: 'error',
      error: {
        code: error.code,
        message: error.message,
        suggestion: error.suggestion,
      },
      timestamp: Date.now(),
    })
  }
}
```

### 7.2 任务处理服务

**TaskService.js**：

```javascript
class TaskService {
  constructor(sseController) {
    this.sseController = sseController
    this.aiService = new AIService()
    this.taskRepository = new TaskRepository()
  }

  async processTask(sessionId, taskDescription) {
    try {
      // 1. 思考阶段
      this.sseController.sendStateUpdate(sessionId, 'thinking', '正在理解您的需求...')

      // AI理解任务
      const taskAnalysis = await this.aiService.analyzeTask(taskDescription)

      // 2. 执行阶段
      this.sseController.sendStateUpdate(sessionId, 'executing', '正在创建任务...', 0)

      // 创建任务
      const task = await this.createTask(sessionId, taskAnalysis)

      // 更新进度
      this.sseController.sendStateUpdate(sessionId, 'executing', '正在保存任务...', 50)

      // 保存到数据库
      const savedTask = await this.taskRepository.save(task)

      // 3. 响应阶段
      this.sseController.sendStateUpdate(sessionId, 'responding', '正在生成确认信息...')

      // 生成响应
      const response = await this.generateResponse(savedTask)

      // 4. 完成
      this.sseController.sendMessage(sessionId, {
        type: 'task_completed',
        task: savedTask,
        response,
        timestamp: Date.now(),
      })

      return savedTask
    } catch (error) {
      await this.handleError(sessionId, error)
      throw error
    }
  }

  async handleError(sessionId, error) {
    const processedError = ErrorProcessor.process(error)
    this.sseController.sendError(sessionId, processedError)

    // 记录错误日志
    console.error(`[TaskService] Error for session ${sessionId}:`, error)
  }
}
```

### 7.3 错误处理中间件

**ErrorMiddleware.js**：

```javascript
class ErrorMiddleware {
  static handle(error, req, res, next) {
    const processedError = ErrorProcessor.process(error)

    // 记录错误日志
    console.error('[ErrorMiddleware]', {
      error: error.message,
      stack: error.stack,
      url: req.url,
      method: req.method,
      timestamp: new Date().toISOString(),
    })

    // 返回用户友好的错误响应
    res.status(processedError.httpStatus || 500).json({
      success: false,
      error: {
        code: processedError.code,
        message: processedError.message,
        suggestion: processedError.suggestion,
        action: processedError.action,
      },
    })
  }
}

class ErrorProcessor {
  static process(error) {
    // 网络相关错误
    if (error.code === 'ECONNREFUSED' || error.code === 'ETIMEDOUT') {
      return {
        code: 'NETWORK_ERROR',
        message: '网络连接不稳定，请检查网络后重试',
        suggestion: '请检查网络连接，或尝试刷新页面',
        action: 'retry',
        httpStatus: 503,
      }
    }

    // 认证错误
    if (error.status === 401 || error.message.includes('unauthorized')) {
      return {
        code: 'AUTH_ERROR',
        message: '登录已过期，请重新登录',
        suggestion: '您的登录状态已过期，需要重新登录',
        action: 'login',
        httpStatus: 401,
      }
    }

    // AI解析错误
    if (error.type === 'AI_PARSE_ERROR') {
      return {
        code: 'PARSE_ERROR',
        message: '我没理解您的意思，能换个说法吗？',
        suggestion: '尝试用更具体的描述，比如"明天上午开会讨论产品规划"',
        action: 'rephrase',
        httpStatus: 400,
      }
    }

    // 配额错误
    if (error.code === 'QUOTA_EXCEEDED') {
      return {
        code: 'QUOTA_ERROR',
        message: '今日使用次数已达上限，请明天再试',
        suggestion: '升级到高级版本可获得更多使用次数',
        action: 'upgrade',
        httpStatus: 429,
      }
    }

    // 默认系统错误
    return {
      code: 'SYSTEM_ERROR',
      message: '系统暂时繁忙，请稍后重试',
      suggestion: '如果问题持续存在，请联系技术支持',
      action: 'retry',
      httpStatus: 500,
    }
  }
}
```

## 🧪 8. 测试方案

### 8.1 单元测试

**状态管理测试**：

```javascript
// useAIState.test.js
import { describe, it, expect, beforeEach } from 'vitest'
import { useAIState } from '@/composables/useAIState'

describe('useAIState', () => {
  let stateManager

  beforeEach(() => {
    stateManager = useAIState()
    stateManager.resetState()
  })

  it('应该正确初始化状态', () => {
    expect(stateManager.aiState.status).toBe('idle')
    expect(stateManager.aiState.message).toBe('')
    expect(stateManager.aiState.progress).toBeNull()
    expect(stateManager.aiState.error).toBeNull()
  })

  it('应该正确更新状态', () => {
    stateManager.updateState('thinking', '正在思考...', 50)

    expect(stateManager.aiState.status).toBe('thinking')
    expect(stateManager.aiState.message).toBe('正在思考...')
    expect(stateManager.aiState.progress).toBe(50)
    expect(stateManager.aiState.error).toBeNull()
  })

  it('应该正确处理错误', () => {
    const error = { code: 'NETWORK_ERROR', message: '网络错误' }
    stateManager.setError(error)

    expect(stateManager.aiState.status).toBe('idle')
    expect(stateManager.aiState.error).toEqual(error)
    expect(stateManager.aiState.progress).toBeNull()
  })
})
```

**错误处理测试**：

```javascript
// ErrorHandler.test.js
import { describe, it, expect } from 'vitest'
import { ErrorHandler } from '@/utils/ErrorHandler'

describe('ErrorHandler', () => {
  let errorHandler

  beforeEach(() => {
    errorHandler = new ErrorHandler()
  })

  it('应该正确分类网络错误', () => {
    const networkError = new Error('fetch failed')
    const result = errorHandler.handleError(networkError)

    expect(result.code).toBe('NETWORK_ERROR')
    expect(result.message).toContain('网络连接不稳定')
    expect(result.action).toBe('retry')
  })

  it('应该正确分类认证错误', () => {
    const authError = new Error('401 unauthorized')
    const result = errorHandler.handleError(authError)

    expect(result.code).toBe('AUTH_ERROR')
    expect(result.message).toContain('登录已过期')
    expect(result.action).toBe('login')
  })

  it('应该为未知错误提供默认处理', () => {
    const unknownError = new Error('unknown error')
    const result = errorHandler.handleError(unknownError)

    expect(result.code).toBe('SYSTEM_ERROR')
    expect(result.action).toBe('retry')
  })
})
```

### 8.2 集成测试

**SSE 连接测试**：

```javascript
// SSE.integration.test.js
import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import { SSEManager } from '@/utils/SSEManager'

describe('SSE Integration', () => {
  let sseManager
  let mockServer

  beforeEach(() => {
    // 启动测试服务器
    mockServer = startMockSSEServer()
    sseManager = new SSEManager()
  })

  afterEach(() => {
    sseManager.disconnect()
    mockServer.close()
  })

  it('应该成功建立SSE连接', async () => {
    const connected = await sseManager.connect('test-session')
    expect(connected).toBe(true)
  })

  it('应该正确接收状态更新消息', async () => {
    const messages = []
    sseManager.addEventListener('message', (data) => {
      messages.push(data)
    })

    await sseManager.connect('test-session')

    // 模拟服务器发送消息
    mockServer.sendMessage({
      type: 'thinking',
      message: '正在思考...',
    })

    await new Promise((resolve) => setTimeout(resolve, 100))

    expect(messages).toHaveLength(1)
    expect(messages[0].type).toBe('thinking')
  })

  it('应该在连接断开时自动重连', async () => {
    await sseManager.connect('test-session')

    // 模拟连接断开
    mockServer.disconnect()

    // 等待重连
    await new Promise((resolve) => setTimeout(resolve, 2000))

    expect(sseManager.reconnectAttempts).toBeGreaterThan(0)
  })
})
```

### 8.3 端到端测试

**任务创建流程测试**：

```javascript
// TaskCreation.e2e.test.js
import { test, expect } from '@playwright/test'

test.describe('任务创建流程', () => {
  test('应该成功创建简单任务', async ({ page }) => {
    await page.goto('/tasks')

    // 输入任务描述
    await page.fill('[data-testid="task-input"]', '明天上午开会')
    await page.click('[data-testid="submit-button"]')

    // 验证状态变化
    await expect(page.locator('[data-testid="status-indicator"]')).toContainText('正在理解')
    await expect(page.locator('[data-testid="status-indicator"]')).toContainText('正在创建')
    await expect(page.locator('[data-testid="status-indicator"]')).toContainText('完成')

    // 验证任务创建成功
    await expect(page.locator('[data-testid="task-list"]')).toContainText('明天上午开会')
  })

  test('应该正确处理网络错误', async ({ page }) => {
    // 模拟网络错误
    await page.route('/api/tasks', (route) => route.abort())

    await page.goto('/tasks')
    await page.fill('[data-testid="task-input"]', '测试任务')
    await page.click('[data-testid="submit-button"]')

    // 验证错误提示
    await expect(page.locator('[data-testid="error-message"]')).toContainText('网络连接不稳定')
    await expect(page.locator('[data-testid="retry-button"]')).toBeVisible()
  })

  test('应该支持错误重试', async ({ page }) => {
    let requestCount = 0

    await page.route('/api/tasks', (route) => {
      requestCount++
      if (requestCount === 1) {
        route.abort() // 第一次请求失败
      } else {
        route.continue() // 第二次请求成功
      }
    })

    await page.goto('/tasks')
    await page.fill('[data-testid="task-input"]', '测试任务')
    await page.click('[data-testid="submit-button"]')

    // 等待错误提示出现
    await expect(page.locator('[data-testid="error-message"]')).toBeVisible()

    // 点击重试
    await page.click('[data-testid="retry-button"]')

    // 验证重试成功
    await expect(page.locator('[data-testid="task-list"]')).toContainText('测试任务')
  })
})
```

## 📋 9. 部署和监控

### 9.1 部署检查清单

**部署前检查**：

- [ ] 所有单元测试通过
- [ ] 集成测试通过
- [ ] 端到端测试通过
- [ ] 性能测试达标（响应时间<2 秒）
- [ ] 错误处理覆盖率>90%
- [ ] 代码审查完成

**部署步骤**：

1. 备份当前版本
2. 部署到测试环境验证
3. 灰度发布（10%用户）
4. 监控关键指标
5. 全量发布

### 9.2 监控指标

**技术指标**：

- 状态转换成功率 > 99%
- SSE 连接稳定性 > 95%
- 错误自动恢复率 > 80%
- 平均响应时间 < 2 秒

**用户体验指标**：

- 任务创建成功率 > 95%
- 错误信息理解率 > 90%
- 用户操作步骤减少 > 40%

这个方案遵循奥卡姆剃刀原则，用最简洁的技术方案解决复杂的状态管理和错误处理问题，为后续 P1 功能的开发奠定坚实基础。
