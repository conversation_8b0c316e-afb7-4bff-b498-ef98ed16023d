// 文档教程: https://uniapp.dcloud.net.cn/uniCloud/schema
{
	"bsonType": "object",
	"required": ["title", "status", "create_date", "user_id"],
	"permission": {
		"read": "doc.user_id == auth.uid",
		"create": true,
		"update": "doc.user_id == auth.uid",
		"delete": "doc.user_id == auth.uid"
	},
	"properties": {
		"_id": {
			"description": "ID，系统自动生成"
		},
		"title": {
			"bsonType": "string",
			"title": "todo 内容",
			"trim": "both"
		},
		"status": {
			"bsonType": "number",
			"title": "todo 状态",
			"description": "0 未完成，1 已完成，2 已放弃",
			"enum": [0, 1, 2]
		},
		"create_date": {
			"bsonType": "timestamp",
			"title": "创建时间",
			"forceDefaultValue": {
				"$env": "now"
			}
		},
		"update_date": {
			"bsonType": "timestamp",
			"title": "更新时间",
			"defaultValue": {
				"$env": "now"
			}
		},
		"user_id": {
			"bsonType": "string",
			"title": "用户 id",
			"description": "旧版使用的唯一标识符",
			"forceDefaultValue": {
				"$env": "uid"
			}
		}
	}
}