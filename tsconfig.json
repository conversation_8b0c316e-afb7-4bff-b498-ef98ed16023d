{
  "compilerOptions": {
    "target": "esnext",
    "useDefineForClassFields": true,
    "module": "esnext",
    "moduleResolution": "node",
    "strict": true,
    "jsx": "preserve",
    "sourceMap": true,
    "resolveJsonModule": true,
    "esModuleInterop": true,
    "lib": ["esnext", "dom"],
    "types": ["vite/client", "node", "@dcloudio/types"],
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    },
    // 加强类型检查配置
    "noImplicitAny": true,
    "noImplicitReturns": true,
    "noImplicitThis": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "exactOptionalPropertyTypes": true,
    "noUncheckedIndexedAccess": true,
    "noImplicitOverride": true
  },
  "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.vue", "src/uni.d.ts"],
  "exclude": ["node_modules", "dist", "**/*.js"]
}
