<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>O</title>
		<link
			href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css"
			rel="stylesheet"
		/>
		<link
			rel="stylesheet"
			href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
		/>
		<style>
			body {
				background-color: #f9fafb;
				padding: 20px;
				/* 添加滚动条样式 */
				scrollbar-width: thin; /* Firefox */
				scrollbar-color: #a0a0a0 #f1f1f1; /* Firefox */
			}

			/* Webkit browsers (Chrome, Safari, Edge) */
			::-webkit-scrollbar {
				width: 8px; /* 滚动条宽度 */
			}

			::-webkit-scrollbar-track {
				background: #f1f1f1; /* 轨道背景色 */
			}

			::-webkit-scrollbar-thumb {
				background: #a0a0a0; /* 滑块颜色 */
				border-radius: 4px; /* 滑块圆角 */
			}

			::-webkit-scrollbar-thumb:hover {
				background: #888; /* 滑块鼠标悬停颜色 */
			}

			.prototype-container {
				display: grid;
				grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
				gap: 30px;
			}
			.phone-frame {
				width: 375px;
				height: 812px;
				background-color: white;
				border-radius: 40px;
				overflow: hidden;
				box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
				margin: 0 auto;
				position: relative;
				border: 10px solid #000;
			}
			.phone-screen {
				width: 100%;
				height: 100%;
				border: none;
			}
			.screen-title {
				text-align: center;
				font-weight: bold;
				margin-top: 10px;
				margin-bottom: 20px;
				font-size: 18px;
				color: #333;
			}

			/* 主题选择器样式 */
			.theme-switcher {
				background: #f1f5f9;
				border-radius: 16px;
				padding: 12px 16px;
				box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
				max-width: 600px;
				margin: 0 auto;
			}

			.theme-btn {
				position: relative;
				border: 2px solid transparent;
				transition: all 0.3s ease;
				cursor: pointer;
			}

			.theme-btn:hover::after {
				content: attr(title);
				position: absolute;
				bottom: -30px;
				left: 50%;
				transform: translateX(-50%);
				background: #333;
				color: white;
				padding: 4px 8px;
				border-radius: 4px;
				font-size: 12px;
				white-space: nowrap;
				z-index: 10;
			}

			.theme-btn.active {
				transform: scale(1.1);
				border: 2px solid white;
				box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
			}

			.theme-btn.active::before {
				content: "✓";
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				color: white;
				font-size: 14px;
				font-weight: bold;
			}
		</style>
	</head>
	<body>
		<div class="container mx-auto my-5">
			<!-- 添加主题色选择器 -->
			<div class="theme-switcher flex justify-center items-center mb-8 gap-4">
				<span class="text-gray-700 font-medium">选择主题色：</span>
				<div class="flex flex-wrap gap-3 justify-center">
					<button
						class="theme-btn w-10 h-10 rounded-full shadow-md transition-all hover:scale-110 active:scale-95"
						style="background: linear-gradient(135deg, #2196f3, #00bcd4)"
						data-theme="ocean"
						title="海洋主题"
					></button>
					<button
						class="theme-btn w-10 h-10 rounded-full shadow-md transition-all hover:scale-110 active:scale-95"
						style="background: linear-gradient(135deg, #4caf50, #8bc34a)"
						data-theme="forest"
						title="森林主题"
					></button>
					<button
						class="theme-btn w-10 h-10 rounded-full shadow-md transition-all hover:scale-110 active:scale-95"
						style="background: linear-gradient(135deg, #ff9800, #ff5722)"
						data-theme="sunset"
						title="日落主题"
					></button>
					<button
						class="theme-btn w-10 h-10 rounded-full shadow-md transition-all hover:scale-110 active:scale-95"
						style="background: linear-gradient(135deg, #e91e63, #ff4081)"
						data-theme="berry"
						title="莓果主题"
					></button>
					<button
						class="theme-btn w-10 h-10 rounded-full shadow-md transition-all hover:scale-110 active:scale-95"
						style="background: linear-gradient(135deg, #009688, #26a69a)"
						data-theme="mint"
						title="薄荷主题"
					></button>
					<!-- 添加 5 种新颜色 -->
					<button
						class="theme-btn w-10 h-10 rounded-full shadow-md transition-all hover:scale-110 active:scale-95"
						style="background: linear-gradient(135deg, #673ab7, #9c27b0)"
						data-theme="purple"
						title="紫罗兰主题"
					></button>
					<button
						class="theme-btn w-10 h-10 rounded-full shadow-md transition-all hover:scale-110 active:scale-95"
						style="background: linear-gradient(135deg, #607d8b, #455a64)"
						data-theme="slate"
						title="石板主题"
					></button>
					<button
						class="theme-btn w-10 h-10 rounded-full shadow-md transition-all hover:scale-110 active:scale-95"
						style="background: linear-gradient(135deg, #ffc107, #ffeb3b)"
						data-theme="sunshine"
						title="阳光主题"
					></button>
					<button
						class="theme-btn w-10 h-10 rounded-full shadow-md transition-all hover:scale-110 active:scale-95"
						style="background: linear-gradient(135deg, #795548, #a1887f)"
						data-theme="coffee"
						title="咖啡主题"
					></button>
					<button
						class="theme-btn w-10 h-10 rounded-full shadow-md transition-all hover:scale-110 active:scale-95"
						style="background: linear-gradient(135deg, #f44336, #e91e63)"
						data-theme="ruby"
						title="红宝石主题"
					></button>
				</div>
			</div>

			<div class="prototype-container">
				<!-- 登录/注册 -->
				<div>
					<h2 class="screen-title">
						登录 / 注册<button
							class="req-info-btn text-sm bg-gray-200 hover:bg-gray-300 text-gray-700 px-2 py-0.5 ml-2 rounded opacity-75"
							data-page="login.html"
						>
							?
						</button>
					</h2>
					<div class="phone-frame">
						<iframe src="login.html" class="phone-screen"></iframe>
					</div>
				</div>

				<!-- 主页面 -->
				<div>
					<h2 class="screen-title">
						主页<button
							class="req-info-btn text-sm bg-gray-200 hover:bg-gray-300 text-gray-700 px-2 py-0.5 ml-2 rounded opacity-75"
							data-page="home.html"
						>
							?
						</button>
					</h2>
					<div class="phone-frame">
						<iframe src="home.html" class="phone-screen"></iframe>
					</div>
				</div>

				<!-- 目标详情 -->
				<div>
					<h2 class="screen-title">
						目标详情<button
							class="req-info-btn text-sm bg-gray-200 hover:bg-gray-300 text-gray-700 px-2 py-0.5 ml-2 rounded opacity-75"
							data-page="objective-detail.html"
						>
							?
						</button>
					</h2>
					<div class="phone-frame">
						<iframe src="objective-detail.html" class="phone-screen"></iframe>
					</div>
				</div>

				<!-- 创建目标 -->
				<div>
					<h2 class="screen-title">
						创建目标<button
							class="req-info-btn text-sm bg-gray-200 hover:bg-gray-300 text-gray-700 px-2 py-0.5 ml-2 rounded opacity-75"
							data-page="create-objective.html"
						>
							?
						</button>
					</h2>
					<div class="phone-frame">
						<iframe src="create-objective.html" class="phone-screen"></iframe>
					</div>
				</div>

				<!-- 任务管理 -->
				<div>
					<h2 class="screen-title">
						任务管理<button
							class="req-info-btn text-sm bg-gray-200 hover:bg-gray-300 text-gray-700 px-2 py-0.5 ml-2 rounded opacity-75"
							data-page="tasks.html"
						>
							?
						</button>
					</h2>
					<div class="phone-frame">
						<iframe src="tasks.html" class="phone-screen"></iframe>
					</div>
				</div>

				<!-- 关键结果编辑页 -->
				<div>
					<h2 class="screen-title">
						关键结果编辑<button
							class="req-info-btn text-sm bg-gray-200 hover:bg-gray-300 text-gray-700 px-2 py-0.5 ml-2 rounded opacity-75"
							data-page="edit-keyresult.html"
						>
							?
						</button>
					</h2>
					<div class="phone-frame">
						<iframe src="edit-keyresult.html" class="phone-screen"></iframe>
					</div>
				</div>

				<!-- 关键结果详情页 -->
				<div>
					<h2 class="screen-title">
						关键结果详情<button
							class="req-info-btn text-sm bg-gray-200 hover:bg-gray-300 text-gray-700 px-2 py-0.5 ml-2 rounded opacity-75"
							data-page="key-result-detail.html"
						>
							?
						</button>
					</h2>
					<div class="phone-frame">
						<iframe src="key-result-detail.html" class="phone-screen"></iframe>
					</div>
				</div>

				<!-- 今日任务页面 -->
				<div>
					<h2 class="screen-title">
						今日任务<button
							class="req-info-btn text-sm bg-gray-200 hover:bg-gray-300 text-gray-700 px-2 py-0.5 ml-2 rounded opacity-75"
							data-page="today-tasks.html"
						>
							?
						</button>
					</h2>
					<div class="phone-frame">
						<iframe src="today-tasks.html" class="phone-screen"></iframe>
					</div>
				</div>

				<!-- 时间管理 -->
				<div>
					<h2 class="screen-title">
						时间管理<button
							class="req-info-btn text-sm bg-gray-200 hover:bg-gray-300 text-gray-700 px-2 py-0.5 ml-2 rounded opacity-75"
							data-page="time-management.html"
						>
							?
						</button>
					</h2>
					<div class="phone-frame">
						<iframe src="time-management.html" class="phone-screen"></iframe>
					</div>
				</div>

				<!-- 数据分析 -->
				<div>
					<h2 class="screen-title">
						数据分析<button
							class="req-info-btn text-sm bg-gray-200 hover:bg-gray-300 text-gray-700 px-2 py-0.5 ml-2 rounded opacity-75"
							data-page="analytics.html"
						>
							?
						</button>
					</h2>
					<div class="phone-frame">
						<iframe src="analytics.html" class="phone-screen"></iframe>
					</div>
				</div>

				<!-- 关键结果添加页 -->
				<div>
					<h2 class="screen-title">
						关键结果添加<button
							class="req-info-btn text-sm bg-gray-200 hover:bg-gray-300 text-gray-700 px-2 py-0.5 ml-2 rounded opacity-75"
							data-page="add-key-result.html"
						>
							?
						</button>
					</h2>
					<div class="phone-frame">
						<iframe src="add-key-result.html" class="phone-screen"></iframe>
					</div>
				</div>

				<!-- 关键结果详情页 -->
				<div>
					<h2 class="screen-title">
						关键结果详情<button
							class="req-info-btn text-sm bg-gray-200 hover:bg-gray-300 text-gray-700 px-2 py-0.5 ml-2 rounded opacity-75"
							data-page="key-result-detail.html"
						>
							?
						</button>
					</h2>
					<div class="phone-frame">
						<iframe src="key-result-detail.html" class="phone-screen"></iframe>
					</div>
				</div>

				<!-- 个人设置 -->
				<div>
					<h2 class="screen-title">
						个人设置<button
							class="req-info-btn text-sm bg-gray-200 hover:bg-gray-300 text-gray-700 px-2 py-0.5 ml-2 rounded opacity-75"
							data-page="settings.html"
						>
							?
						</button>
					</h2>
					<div class="phone-frame">
						<iframe src="settings.html" class="phone-screen"></iframe>
					</div>
				</div>
			</div>
		</div>

		<!-- Requirement Modal Structure -->
		<div
			id="requirementModal"
			class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden"
		>
			<div
				class="bg-white rounded-lg shadow-xl overflow-hidden w-11/12 max-w-7xl h-5/6 md:h-5/6 lg:h-5/6 flex flex-col"
				style="height: 90vh"
			>
				<!-- Modal Header -->
				<div class="flex justify-between items-center p-4 border-b">
					<h3 id="modalTitle" class="text-lg font-semibold text-gray-800">
						需求详情
					</h3>
					<button
						id="closeModal"
						class="text-gray-500 hover:text-gray-700 text-2xl"
					>
						&times;
					</button>
				</div>

				<!-- Modal Body -->
				<div class="flex flex-1 overflow-hidden">
					<!-- Prototype Frame (Left) -->
					<div
						class="w-1/2 h-full flex items-center justify-center bg-gray-100 p-8 overflow-auto"
					>
						<div class="phone-frame transform scale-80 origin-center">
							<iframe id="prototypeIframe" src="" class="phone-screen"></iframe>
						</div>
					</div>

					<!-- Requirement Description (Right) -->
					<div id="requirementDescription" class="w-1/2 p-6 overflow-y-auto">
						<h4 class="font-semibold mb-3">需求说明：</h4>
						<div id="requirementContent" class="text-gray-700 text-sm">
							<p>此处显示对应页面的需求说明。</p>
							<p>
								例如，这里可以详细描述该页面的功能、交互逻辑、数据来源、设计约束等。
							</p>
							<p>
								目前这段文字是占位符。你需要手动编辑 index.html 文件，根据
								data-page 属性来加载或显示不同的需求内容。
							</p>
						</div>
					</div>
				</div>
			</div>
		</div>

		<script>
			// 主题色配置
			const themes = {
				ocean: {
					primary: "#2196F3",
					secondary: "#00BCD4",
					accent: "#FF5722",
				},
				forest: {
					primary: "#4CAF50",
					secondary: "#8BC34A",
					accent: "#FFC107",
				},
				sunset: {
					primary: "#FF9800",
					secondary: "#FF5722",
					accent: "#03A9F4",
				},
				berry: {
					primary: "#E91E63",
					secondary: "#FF4081",
					accent: "#7C4DFF",
				},
				mint: {
					primary: "#009688",
					secondary: "#26A69A",
					accent: "#FF4081",
				},
				// 添加 5 种新主题色配置
				purple: {
					primary: "#673AB7",
					secondary: "#9C27B0",
					accent: "#4CAF50",
				},
				slate: {
					primary: "#607D8B",
					secondary: "#455A64",
					accent: "#FF9800",
				},
				sunshine: {
					primary: "#FFC107",
					secondary: "#FFEB3B",
					accent: "#FF5722",
				},
				coffee: {
					primary: "#795548",
					secondary: "#A1887F",
					accent: "#009688",
				},
				ruby: {
					primary: "#F44336",
					secondary: "#E91E63",
					accent: "#3F51B5",
				},
			};

			// 应用主题色到所有 iframe
			function applyTheme(themeName) {
				const theme = themes[themeName];
				if (!theme) return;

				// 更新按钮样式
				document.querySelectorAll(".theme-btn").forEach((btn) => {
					btn.classList.remove("active");
					if (btn.dataset.theme === themeName) {
						btn.classList.add("active");
					}
				});

				// 获取所有 iframe
				const iframes = document.querySelectorAll(".phone-screen");
				iframes.forEach((iframe) => {
					try {
						// 如果 iframe 已加载，立即发送主题消息
						if (iframe.contentWindow) {
							iframe.contentWindow.postMessage(
								{
									type: "THEME_CHANGE",
									theme: theme,
								},
								"*"
							);
						}

						// 添加加载事件，确保 iframe 加载后应用主题
						iframe.addEventListener("load", function () {
							iframe.contentWindow.postMessage(
								{
									type: "THEME_CHANGE",
									theme: theme,
								},
								"*"
							);
						});
					} catch (e) {
						console.error("Failed to send theme to iframe:", e);
					}
				});

				// 设置标题栏颜色
				const titleElement = document.querySelector("h1.text-3xl");
				if (titleElement) {
					titleElement.style.background = `linear-gradient(120deg, ${theme.primary}, ${theme.secondary})`;
					titleElement.style.webkitBackgroundClip = "text";
					titleElement.style.webkitTextFillColor = "transparent";
				}

				// 保存主题选择到 localStorage
				localStorage.setItem("selectedTheme", themeName);
			}

			// 设置主题色按钮点击事件
			document.addEventListener("DOMContentLoaded", function () {
				const themeButtons = document.querySelectorAll(".theme-btn");

				// 添加美观的点击效果
				themeButtons.forEach((btn) => {
					btn.addEventListener("click", () => {
						const themeName = btn.dataset.theme;

						// 清除所有按钮的 active 状态
						themeButtons.forEach((b) => {
							b.classList.remove("active");
						});

						// 设置当前按钮为 active
						btn.classList.add("active");

						// 应用主题
						applyTheme(themeName);
					});
				});

				// 加载保存的主题
				const savedTheme = localStorage.getItem("selectedTheme");
				if (savedTheme) {
					const themeBtn = document.querySelector(
						`[data-theme="${savedTheme}"]`
					);
					if (themeBtn) {
						themeBtn.click();
					}
				} else {
					// 默认加载海洋主题
					const defaultBtn = document.querySelector('[data-theme="ocean"]');
					if (defaultBtn) {
						defaultBtn.click();
					}
				}

				// 给 iframe 添加重新加载事件，确保重新加载时也应用主题
				const iframes = document.querySelectorAll(".phone-screen");
				iframes.forEach((iframe) => {
					iframe.addEventListener("load", function () {
						const currentTheme =
							localStorage.getItem("selectedTheme") || "ocean";
						const theme = themes[currentTheme];
						if (theme) {
							setTimeout(() => {
								iframe.contentWindow.postMessage(
									{
										type: "THEME_CHANGE",
										theme: theme,
									},
									"*"
								);
							}, 100); // 短暂延迟确保接收端已准备就绪
						}
					});
				});
			});

			// 打开需求详情模态框
			document.addEventListener("DOMContentLoaded", function () {
				const reqInfoBtns = document.querySelectorAll(".req-info-btn");
				const modal = document.getElementById("requirementModal");
				const closeModalBtn = document.getElementById("closeModal");
				const modalTitle = document.getElementById("modalTitle");
				const prototypeIframe = document.getElementById("prototypeIframe");
				const requirementContent =
					document.getElementById("requirementContent");

				// 需求内容映射
				const requirementContents = {
					"login.html": `
            <h5 class="font-semibold mb-2">页面目标：</h5>
            <p class="mb-3">提供用户登录和注册入口，支持多种登录方式。</p>
            <h5 class="font-semibold mb-2">功能需求：</h5>
            <ul class="list-disc pl-5 mb-3">
              <li>用户名/密码登录</li>
              <li>社交媒体账号登录（微信、QQ、微博）</li>
              <li>记住登录状态选项</li>
              <li>忘记密码和注册新账号入口</li>
            </ul>
            <h5 class="font-semibold mb-2">交互说明：</h5>
            <p>点击登录按钮后直接跳转到主页面，本原型不实现实际的认证流程。</p>
          `,
					"home.html": `
            <h5 class="font-semibold mb-2">页面目标：</h5>
            <p class="mb-3">为用户提供 OKR 目标概览，显示进行中的目标和状态统计。</p>
            <h5 class="font-semibold mb-2">功能需求：</h5>
            <ul class="list-disc pl-5 mb-3">
              <li>显示用户当前季度 OKR 完成进度</li>
              <li>列出进行中的目标卡片，包括进度条</li>
              <li>每个目标卡片显示关键结果预览</li>
              <li>支持快速添加新目标的入口</li>
              <li>底部导航可切换到其他主要功能区</li>
            </ul>
            <h5 class="font-semibold mb-2">数据来源：</h5>
            <p>目标数据来自用户创建的 OKR 记录，进度基于关键结果的完成情况自动计算。</p>
          `,
					"objective-detail.html": `
            <h5 class="font-semibold mb-2">页面目标：</h5>
            <p class="mb-3">详细展示单个 OKR 目标的所有关键结果和具体进度。</p>
            <h5 class="font-semibold mb-2">功能需求：</h5>
            <ul class="list-disc pl-5 mb-3">
              <li>显示目标的详细描述和周期</li>
              <li>列出所有关键结果 (KRs) 及其进度</li>
              <li>允许编辑目标和关键结果</li>
              <li>支持添加关键结果的相关任务</li>
              <li>提供目标进度的评论和反思功能</li>
            </ul>
            <h5 class="font-semibold mb-2">交互说明：</h5>
            <p>用户可以通过点击关键结果来更新其进度，系统会自动计算整体目标完成率。</p>
          `,
					"create-objective.html": `
            <h5 class="font-semibold mb-2">页面目标：</h5>
            <p class="mb-3">提供创建新 OKR 目标和关键结果的表单界面。</p>
            <h5 class="font-semibold mb-2">功能需求：</h5>
            <ul class="list-disc pl-5 mb-3">
              <li>设置目标标题、描述和类别</li>
              <li>选择目标周期和截止日期</li>
              <li>添加多个关键结果及其衡量标准</li>
              <li>设置目标优先级和关联项目</li>
              <li>保存为草稿或直接发布功能</li>
            </ul>
            <h5 class="font-semibold mb-2">数据验证：</h5>
            <p>目标必须至少包含 1 个关键结果，且每个关键结果需要有明确的衡量标准。</p>
          `,
					"edit-keyresult.html": `
            <h5 class="font-semibold mb-2">页面目标：</h5>
            <p class="mb-3">提供编辑单个关键结果的详细界面，包括设置进度和相关属性。</p>
            <h5 class="font-semibold mb-2">功能需求：</h5>
            <ul class="list-disc pl-5 mb-3">
              <li>编辑关键结果的标题和描述</li>
              <li>设置衡量标准（数值型/比例型/完成型）</li>
              <li>更新当前进度状态</li>
              <li>添加关联任务和里程碑</li>
              <li>设置提醒和检查点</li>
              <li>添加关键结果的注释和反思</li>
            </ul>
            <h5 class="font-semibold mb-2">交互说明：</h5>
            <p>根据不同类型的关键结果，提供对应的进度更新方式，如滑块、数值输入或复选框等。</p>
          `,
					"keyresult-detail.html": `
            <h5 class="font-semibold mb-2">页面目标：</h5>
            <p class="mb-3">展示关键结果的详细信息、进度历史和相关任务。</p>
            <h5 class="font-semibold mb-2">功能需求：</h5>
            <ul class="list-disc pl-5 mb-3">
              <li>显示关键结果详细描述和衡量标准</li>
              <li>展示进度历史和趋势图表</li>
              <li>显示关联任务列表及其状态</li>
              <li>提供检查点和里程碑视图</li>
              <li>展示更新历史和注释记录</li>
              <li>提供快速进度更新功能</li>
            </ul>
            <h5 class="font-semibold mb-2">数据整合：</h5>
            <p>整合任务完成情况、时间投入数据，提供关键结果执行的全面视图，辅助进度追踪和调整。</p>
          `,
					"today-tasks.html": `
            <h5 class="font-semibold mb-2">页面目标：</h5>
            <p class="mb-3">提供当日任务的集中查看和管理功能，帮助用户聚焦当天工作内容。</p>
            <h5 class="font-semibold mb-2">功能需求：</h5>
            <ul class="list-disc pl-5 mb-3">
              <li>日历周视图功能，左右滑动可切换周，点击显示当天任务</li>
              <li>今日任务聚合展示，自动汇总当日所有待办任务</li>
              <li>按目标分类展示任务</li>
              <li>显示任务所属关键结果、完成贡献的进度值和截止时间</li>
              <li>支持任务快速完成标记</li>
            </ul>
            <h5 class="font-semibold mb-2">数据整合：</h5>
            <p>从各个目标和关键结果中提取当天需要完成的任务，集中展示并支持快速操作，提高工作效率。</p>
          `,
					"tasks.html": `
            <h5 class="font-semibold mb-2">页面目标：</h5>
            <p class="mb-3">管理与 OKR 关键结果相关的具体任务，提供任务排期和执行跟踪。</p>
            <h5 class="font-semibold mb-2">功能需求：</h5>
            <ul class="list-disc pl-5 mb-3">
              <li>按状态分类显示任务（待办、进行中、已完成）</li>
              <li>显示任务与关键结果的关联</li>
              <li>支持任务拖拽排序和状态变更</li>
              <li>任务可设置优先级和截止日期</li>
              <li>提供任务筛选和搜索功能</li>
            </ul>
            <h5 class="font-semibold mb-2">交互说明：</h5>
            <p>完成任务会自动更新相关关键结果的进度，任务可以设置提醒和重复周期。</p>
          `,
					"time-management.html": `
            <h5 class="font-semibold mb-2">页面目标：</h5>
            <p class="mb-3">提供时间管理工具，帮助用户规划任务时间并追踪投入。</p>
            <h5 class="font-semibold mb-2">功能需求：</h5>
            <ul class="list-disc pl-5 mb-3">
              <li>日历视图显示任务安排</li>
              <li>专注计时器功能</li>
              <li>时间投入统计分析</li>
              <li>任务时间估算与实际对比</li>
              <li>番茄工作法支持</li>
            </ul>
            <h5 class="font-semibold mb-2">数据整合：</h5>
            <p>时间数据与 OKR 目标和任务关联，帮助用户了解时间投入与目标进展的关系。</p>
          `,
					"analytics.html": `
            <h5 class="font-semibold mb-2">页面目标：</h5>
            <p class="mb-3">提供 OKR 执行数据的可视化分析，帮助用户优化目标设定和执行。</p>
            <h5 class="font-semibold mb-2">功能需求：</h5>
            <ul class="list-disc pl-5 mb-3">
              <li>OKR 完成率趋势图</li>
              <li>目标类别分布分析</li>
              <li>时间投入与产出效率分析</li>
              <li>目标达成预测</li>
              <li>历史数据比较功能</li>
            </ul>
            <h5 class="font-semibold mb-2">数据来源：</h5>
            <p>分析基于用户历史 OKR 数据、任务完成记录和时间追踪数据生成。</p>
          `,
					"settings.html": `
            <h5 class="font-semibold mb-2">页面目标：</h5>
            <p class="mb-3">提供用户设置和个性化配置选项。</p>
            <h5 class="font-semibold mb-2">功能需求：</h5>
            <ul class="list-disc pl-5 mb-3">
              <li>个人资料设置</li>
              <li>通知偏好设置</li>
              <li>界面主题和语言选择</li>
              <li>数据导出和备份选项</li>
              <li>账号安全设置</li>
              <li>隐私控制选项</li>
            </ul>
            <h5 class="font-semibold mb-2">其他设置：</h5>
            <p>包括 OKR 周期设定、默认视图配置、与其他工具的集成设置等。</p>
          `,
					"add-key-result.html": `
            <h5 class="font-semibold mb-2">页面目标：</h5>
            <p class="mb-3">为现有目标添加新的关键结果，支持详细设置关键结果的衡量标准。</p>
            <h5 class="font-semibold mb-2">功能需求：</h5>
            <ul class="list-disc pl-5 mb-3">
              <li>设置关键结果标题和详细描述</li>
              <li>定义衡量标准和数值范围</li>
              <li>设置关键结果的完成期限</li>
              <li>关联相关团队成员或负责人</li>
              <li>设置检查点和里程碑</li>
              <li>添加参考资源和支持文档链接</li>
            </ul>
            <h5 class="font-semibold mb-2">数据关联：</h5>
            <p>新添加的关键结果将自动关联到所属目标，并影响目标的整体完成率计算。</p>
          `,
					"key-result-detail.html": `
            <h5 class="font-semibold mb-2">页面目标：</h5>
            <p class="mb-3">提供单个关键结果的详情查看和进度更新功能。</p>
            <h5 class="font-semibold mb-2">功能需求：</h5>
            <ul class="list-disc pl-5 mb-3">
              <li>显示关键结果的详细信息和衡量标准</li>
              <li>提供进度更新界面，支持数值和百分比调整</li>
              <li>显示历史进度更新记录和趋势</li>
              <li>关联任务列表及其完成状态</li>
              <li>支持添加进度说明和评论</li>
              <li>提供关键结果调整和重设功能</li>
            </ul>
            <h5 class="font-semibold mb-2">交互说明：</h5>
            <p>用户可以查看进度历史，了解关键结果的执行情况，并根据实际情况调整进度或添加相关任务。</p>
          `,
				};

				// 打开模态框
				reqInfoBtns.forEach((btn) => {
					btn.addEventListener("click", function () {
						const pageName = this.getAttribute("data-page");
						const pageTitle = this.parentElement.textContent
							.trim()
							.replace("?", "");

						modalTitle.textContent = pageTitle + " - 需求详情";
						prototypeIframe.src = pageName;

						// 设置需求内容
						if (requirementContents[pageName]) {
							requirementContent.innerHTML = requirementContents[pageName];
						} else {
							requirementContent.innerHTML =
								"<p>此页面的需求说明尚未添加。</p>";
						}

						// 显示模态框
						modal.classList.remove("hidden");
					});
				});

				// 关闭模态框
				closeModalBtn.addEventListener("click", function () {
					modal.classList.add("hidden");
					prototypeIframe.src = "";
				});

				// 点击背景关闭
				modal.addEventListener("click", function (e) {
					if (e.target === modal) {
						modal.classList.add("hidden");
						prototypeIframe.src = "";
					}
				});
			});
		</script>
	</body>
</html>
