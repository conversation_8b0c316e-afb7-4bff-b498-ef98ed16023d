// 云对象教程：https://uniapp.dcloud.net.cn/uniCloud/cloud-obj
// jsdoc 语法提示教程：https://ask.dcloud.net.cn/docs/#//ask.dcloud.net.cn/article/129

module.exports = {
  async update() {
    const httpInfo = this.getHttpInfo()
    const params = JSON.parse(httpInfo.body)
    const db = uniCloud.database()
    const res = await db.collection('opendb-app-versions').add({
      appid: params.appid,
      name: 'OKR',
      min_uni_version: '1.0.0',
      platform: ['Android'],
      stable_publish: true,
      is_silently: false,
      is_mandatory: true,
      create_date: new Date().getTime(),
      uni_platform: 'android',
      create_env: 'auto',
      title: params.title, //更新标题
      contents: params.contents, //更新内容
      type: params.type, // 类型：wgt native_app
      version: params.version, // 版本
      url: params.url, // 下载 url
    })
    console.log(res)
    if (res.id) {
      return true
    } else {
      return false
    }
  },
}
