<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>关键结果详情 - OKR助手</title>
		<link
			href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css"
			rel="stylesheet"
		/>
		<link
			rel="stylesheet"
			href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
		/>
		<!-- 引入共享组件 -->
		<script>
			// 动态加载 shared-components.html
			(function () {
				const xhr = new XMLHttpRequest();
				xhr.open("GET", "shared-components.html", true);
				xhr.onreadystatechange = function () {
					if (xhr.readyState === 4 && xhr.status === 200) {
						// 创建临时容器
						const tempDiv = document.createElement("div");
						tempDiv.innerHTML = xhr.responseText;

						// 提取 style 标签并添加到 head
						const styleTags = tempDiv.getElementsByTagName("style");
						for (let i = 0; i < styleTags.length; i++) {
							const styleContent = styleTags[i].textContent;
							const style = document.createElement("style");
							style.textContent = styleContent;
							document.head.appendChild(style);
						}

						// 提取script标签内容并执行
						const scriptTags = tempDiv.getElementsByTagName("script");
						if (scriptTags.length > 0) {
							const scriptContent = scriptTags[0].textContent;
							const script = document.createElement("script");
							script.textContent = scriptContent;
							document.head.appendChild(script);

							// 等待脚本加载后初始化标签栏
							setTimeout(function () {
								loadTabBar("tab-bar-container");
							}, 100);
						}

						// 添加模板到页面
						const templates = tempDiv.getElementsByTagName("template");
						for (let i = 0; i < templates.length; i++) {
							document.body.appendChild(templates[i].cloneNode(true));
						}
					}
				};
				xhr.send();
			})();
		</script>
		<style>
			@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap");

			body {
				font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
					Helvetica, Arial, sans-serif;
				background: var(--color-bg);
				height: 100vh;
				display: flex;
				flex-direction: column;
				color: var(--color-gray-800);
				overflow-x: hidden;
			}

			.content-area {
				flex: 1;
				overflow-y: auto;
				padding: 0 16px 90px;
				max-width: 800px;
				margin: 0 auto;
				width: 100%;
			}

			.navbar {
				display: flex;
				align-items: center;
				padding: 15px 20px;
				background: var(--color-white);
				border-bottom: 1px solid var(--color-gray-200);
				position: sticky;
				top: 44px;
				z-index: 40;
			}

			.navbar-title {
				font-weight: 600;
				font-size: 18px;
				margin-left: 15px;
				color: var(--color-primary);
			}

			.navbar-back {
				color: var(--color-primary);
				font-size: 16px;
				width: 36px;
				height: 36px;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 8px;
				background: var(--color-gray-100);
			}

			.navbar-action {
				margin-left: auto;
				color: var(--color-primary);
				font-weight: 500;
				font-size: 15px;
				padding: 8px 16px;
				border-radius: 6px;
				background: var(--color-primary-light);
			}

			.section {
				margin-bottom: 24px;
			}

			.card {
				background: var(--color-white);
				border-radius: 16px;
				box-shadow: var(--shadow-sm);
				margin-bottom: 16px;
				overflow: hidden;
			}

			.card-header {
				padding: 18px 20px;
				border-bottom: 1px solid var(--color-gray-200);
				display: flex;
				align-items: center;
				justify-content: space-between;
			}

			.card-title {
				font-size: 17px;
				font-weight: 600;
				color: var(--color-gray-800);
			}

			.card-subtitle {
				font-size: 14px;
				color: var(--color-gray-500);
				margin-top: 4px;
			}

			.card-body {
				padding: 20px;
			}

			.card-actions {
				display: flex;
				gap: 8px;
			}

			.card-action-btn {
				color: var(--color-gray-600);
				width: 32px;
				height: 32px;
				border-radius: 8px;
				display: flex;
				align-items: center;
				justify-content: center;
				transition: all 0.2s;
				background: var(--color-gray-100);
			}

			.card-action-btn:hover {
				background: var(--color-gray-200);
				color: var(--color-primary);
			}

			.kr-header {
				display: flex;
				flex-direction: column;
				gap: 8px;
				padding: 20px;
				background: linear-gradient(
					to right,
					var(--color-primary-light),
					rgba(255, 255, 255, 0.8)
				);
				border-radius: 16px;
				margin-bottom: 20px;
			}

			.kr-title {
				font-size: 20px;
				font-weight: 600;
				color: var(--color-gray-800);
				line-height: 1.4;
			}

			.kr-description {
				font-size: 15px;
				color: var(--color-gray-600);
				line-height: 1.5;
			}

			.kr-meta {
				display: flex;
				flex-wrap: wrap;
				gap: 12px;
				margin-top: 12px;
			}

			.kr-meta-item {
				display: flex;
				align-items: center;
				gap: 6px;
				font-size: 13px;
				color: var(--color-gray-600);
				background: rgba(255, 255, 255, 0.7);
				padding: 6px 12px;
				border-radius: 6px;
			}

			.progress-container {
				margin: 20px 0;
			}

			.progress-header {
				display: flex;
				justify-content: space-between;
				margin-bottom: 8px;
			}

			.progress-title {
				font-size: 15px;
				font-weight: 500;
				color: var(--color-gray-700);
			}

			.progress-value {
				font-size: 15px;
				font-weight: 600;
				color: var(--color-primary);
			}

			.progress-bar {
				height: 8px;
				background: var(--color-gray-200);
				border-radius: 4px;
				margin-bottom: 8px;
				overflow: hidden;
			}

			.progress-fill {
				height: 100%;
				background: linear-gradient(
					to right,
					var(--color-primary),
					var(--color-primary-light)
				);
				border-radius: 4px;
				transition: width 0.3s ease;
			}

			.progress-labels {
				display: flex;
				justify-content: space-between;
				font-size: 12px;
				color: var(--color-gray-500);
			}

			.progress-info {
				display: flex;
				justify-content: space-between;
				font-size: 12px;
				color: var(--color-gray-600);
			}

			.progress-weight {
				font-weight: 500;
			}

			.weight-value {
				font-weight: 600;
				color: var(--color-primary);
			}

			.chart-container {
				position: relative;
				height: 200px;
				margin: 20px 0;
			}

			.tabs {
				display: flex;
				border-bottom: 1px solid var(--color-gray-200);
				margin-bottom: 16px;
				overflow-x: auto;
				scrollbar-width: none; /* Firefox */
				-ms-overflow-style: none; /* IE and Edge */
			}

			.tabs::-webkit-scrollbar {
				display: none; /* Chrome, Safari, Opera */
			}

			.tab {
				padding: 12px 16px;
				font-size: 14px;
				font-weight: 500;
				color: var(--color-gray-600);
				cursor: pointer;
				white-space: nowrap;
				border-bottom: 2px solid transparent;
				transition: all 0.2s;
			}

			.tab.active {
				color: var(--color-primary);
				border-bottom-color: var(--color-primary);
			}

			.task-list {
				display: flex;
				flex-direction: column;
				gap: 10px;
			}

			.task-item {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 12px 16px;
				background: var(--color-gray-50);
				border-radius: 8px;
				transition: all 0.2s;
			}

			.task-item:hover {
				background: var(--color-gray-100);
			}

			.task-name {
				display: flex;
				align-items: center;
				gap: 10px;
				font-size: 14px;
				color: var(--color-gray-700);
			}

			.task-status {
				font-size: 12px;
				padding: 4px 8px;
				border-radius: 4px;
				font-weight: 500;
			}

			.status-pending {
				background: var(--color-gray-100);
				color: var(--color-gray-600);
			}

			.status-in-progress {
				background: #e8f4fd;
				color: #2196f3;
			}

			.status-completed {
				background: #e6f8f0;
				color: #4caf50;
			}

			.milestone-list {
				position: relative;
				padding-left: 20px;
				margin-top: 10px;
			}

			.milestone-list::before {
				content: "";
				position: absolute;
				left: 4px;
				top: 0;
				bottom: 0;
				width: 2px;
				background: var(--color-gray-200);
			}

			.milestone-item {
				position: relative;
				margin-bottom: 16px;
				padding-left: 18px;
			}

			.milestone-item::before {
				content: "";
				position: absolute;
				left: -4px;
				top: 6px;
				width: 12px;
				height: 12px;
				border-radius: 50%;
				background: var(--color-gray-300);
				z-index: 1;
			}

			.milestone-item.active::before {
				background: var(--color-primary);
			}

			.milestone-item.completed::before {
				background: var(--color-primary);
			}

			.milestone-date {
				font-size: 12px;
				color: var(--color-gray-500);
			}

			.milestone-title {
				font-size: 14px;
				font-weight: 500;
				color: var(--color-gray-700);
				margin: 4px 0;
			}

			.milestone-desc {
				font-size: 13px;
				color: var(--color-gray-600);
			}

			.note-list {
				display: flex;
				flex-direction: column;
				gap: 16px;
			}

			.note-item {
				background: var(--color-gray-50);
				border-radius: 12px;
				padding: 16px;
			}

			.note-header {
				display: flex;
				justify-content: space-between;
				margin-bottom: 8px;
			}

			.note-author {
				font-size: 14px;
				font-weight: 500;
				color: var(--color-gray-800);
			}

			.note-date {
				font-size: 12px;
				color: var(--color-gray-500);
			}

			.note-content {
				font-size: 14px;
				color: var(--color-gray-700);
				line-height: 1.5;
			}

			/* 底部快速更新按钮 */
			.footer {
				position: fixed;
				bottom: 0;
				left: 0;
				width: 100%;
				background: var(--color-white);
				padding: 16px;
				box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
				z-index: 50;
				display: flex;
				gap: 12px;
			}

			.action-btn {
				flex: 1;
				border: none;
				border-radius: 12px;
				padding: 14px;
				font-size: 15px;
				font-weight: 600;
				display: flex;
				align-items: center;
				justify-content: center;
				cursor: pointer;
				transition: all 0.2s;
			}

			.primary-btn {
				background: var(--color-primary);
				color: var(--color-white);
			}

			.primary-btn:hover {
				background: var(--color-primary-dark);
			}

			.secondary-btn {
				background: var(--color-gray-100);
				color: var(--color-gray-700);
			}

			.secondary-btn:hover {
				background: var(--color-gray-200);
			}

			/* 模态框样式 */
			.modal {
				display: none;
				position: fixed;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background-color: rgba(0, 0, 0, 0.5);
				z-index: 100;
				justify-content: center;
				align-items: center;
			}

			.modal.show {
				display: flex;
			}

			.modal-content {
				background: var(--color-white);
				border-radius: 16px;
				width: 90%;
				max-width: 500px;
				max-height: 90vh;
				overflow-y: auto;
				box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
			}

			.modal-header {
				padding: 16px 20px;
				border-bottom: 1px solid var(--color-gray-200);
				display: flex;
				align-items: center;
				justify-content: space-between;
			}

			.modal-title {
				font-size: 18px;
				font-weight: 600;
				color: var(--color-gray-800);
			}

			.modal-close {
				background: none;
				border: none;
				font-size: 24px;
				color: var(--color-gray-500);
				cursor: pointer;
			}

			.modal-body {
				padding: 20px;
			}

			.modal-footer {
				padding: 16px 20px;
				border-top: 1px solid var(--color-gray-200);
				display: flex;
				justify-content: flex-end;
				gap: 12px;
			}

			.form-group {
				margin-bottom: 16px;
			}

			.form-group label {
				display: block;
				font-size: 14px;
				font-weight: 500;
				color: var(--color-gray-700);
				margin-bottom: 8px;
			}

			.form-input {
				width: 100%;
				padding: 12px;
				border: 1px solid var(--color-gray-300);
				border-radius: 8px;
				font-size: 14px;
				color: var(--color-gray-800);
				background: var(--color-white);
			}

			.form-input:focus {
				border-color: var(--color-primary);
				outline: none;
				box-shadow: 0 0 0 2px var(--color-primary-light);
			}

			.related-kr {
				padding: 12px;
				background: var(--color-gray-50);
				border-radius: 8px;
				border: 1px solid var(--color-gray-200);
			}

			.kr-info {
				display: flex;
				flex-direction: column;
				gap: 4px;
			}

			.kr-name {
				font-size: 14px;
				font-weight: 500;
				color: var(--color-gray-800);
			}

			.kr-progress {
				font-size: 12px;
				color: var(--color-gray-600);
			}

			.cycle-options {
				display: flex;
				flex-wrap: wrap;
				gap: 12px;
			}

			.cycle-option {
				display: flex;
				align-items: center;
				gap: 6px;
				padding: 8px 12px;
				border-radius: 6px;
				background: var(--color-gray-50);
				border: 1px solid var(--color-gray-200);
				cursor: pointer;
				transition: all 0.2s;
			}

			.cycle-option:hover {
				background: var(--color-gray-100);
			}

			.cycle-option input {
				margin: 0;
			}

			.progress-input-container {
				display: flex;
				align-items: center;
			}

			.progress-unit {
				margin-left: 8px;
				font-size: 14px;
				color: var(--color-gray-600);
			}

			.btn-cancel {
				padding: 10px 16px;
				border: 1px solid var(--color-gray-300);
				background: var(--color-white);
				color: var(--color-gray-700);
				font-size: 14px;
				font-weight: 500;
				border-radius: 8px;
				cursor: pointer;
				transition: all 0.2s;
			}

			.btn-cancel:hover {
				background: var(--color-gray-50);
			}

			.btn-save {
				padding: 10px 16px;
				border: none;
				background: var(--color-primary);
				color: var(--color-white);
				font-size: 14px;
				font-weight: 500;
				border-radius: 8px;
				cursor: pointer;
				transition: all 0.2s;
			}

			.btn-save:hover {
				background: var(--color-primary-dark);
			}
		</style>
	</head>
	<body>
		<!-- 状态栏 -->
		<div id="status-bar-container"></div>

		<!-- 导航栏 -->
		<div class="navbar">
			<a href="objective-detail.html" class="navbar-back">
				<i class="fas fa-chevron-left"></i>
			</a>
			<div class="navbar-title">关键结果详情</div>
			<a href="edit-keyresult.html" class="navbar-action">
				<i class="fas fa-edit mr-1"></i> 编辑
			</a>
		</div>

		<!-- 内容区域 -->
		<div class="content-area">
			<!-- 关键结果标题和描述 -->
			<div class="kr-header">
				<div class="kr-title">用户满意度评分达到 4.8（满分 5 分）</div>
				<div class="kr-description">
					通过问卷调查收集用户反馈，确保产品用户体验满足高标准要求，每月进行一次用户满意度调查。
				</div>
				<div class="kr-meta">
					<div class="kr-meta-item">
						<i class="fas fa-calendar-alt"></i>
						<span>截止：2023/06/30</span>
					</div>
					<div class="kr-meta-item">
						<i class="fas fa-chart-line"></i>
						<span>数值型指标</span>
					</div>
					<div class="kr-meta-item">
						<i class="fas fa-tasks"></i>
						<span>4 个相关任务</span>
					</div>
					<div class="kr-meta-item">
						<i class="fas fa-balance-scale"></i>
						<span>权重：30%</span>
					</div>
				</div>
			</div>

			<!-- 进度条 -->
			<div class="progress-container">
				<div class="progress-header">
					<div class="progress-title">当前进度</div>
					<div class="progress-value">4.3/4.8 (90%)</div>
				</div>
				<div class="progress-bar">
					<div class="progress-fill" style="width: 90%"></div>
				</div>
				<div class="progress-labels">
					<div>起始值：3.8</div>
					<div>目标值：4.8</div>
				</div>
				<div class="progress-info">
					<div class="progress-weight">权重贡献：<span class="weight-value">目标完成度 × 30%</span></div>
				</div>
			</div>

			<!-- 选项卡 -->
			<div class="tabs">
				<div class="tab active" data-tab="history">进度历史</div>
				<div class="tab" data-tab="tasks">关联任务</div>
			</div>

			<!-- 进度历史 -->
			<div class="tab-content" id="history-tab">
				<div class="card">
					<div class="card-header">
						<div>
							<div class="card-title">进度趋势</div>
							<div class="card-subtitle">过去 4 个月的进度情况</div>
						</div>
						<div class="card-actions">
							<div class="card-action-btn">
								<i class="fas fa-expand-alt"></i>
							</div>
						</div>
					</div>
					<div class="card-body">
						<div class="chart-container">
							<!-- 假设这里是一个图表 -->
							<div
								style="
									width: 100%;
									height: 100%;
									display: flex;
									align-items: center;
									justify-content: center;
									color: var(--color-gray-400);
									font-size: 14px;
								"
							>
								<svg viewBox="0 0 400 200" style="width: 100%; height: 100%">
									<!-- 坐标轴 -->
									<line
										x1="40"
										y1="160"
										x2="380"
										y2="160"
										stroke="#ccc"
										stroke-width="1"
									/>
									<line
										x1="40"
										y1="20"
										x2="40"
										y2="160"
										stroke="#ccc"
										stroke-width="1"
									/>

									<!-- Y 轴刻度 -->
									<text x="30" y="160" font-size="10" text-anchor="end">
										3.8
									</text>
									<text x="30" y="120" font-size="10" text-anchor="end">
										4.0
									</text>
									<text x="30" y="80" font-size="10" text-anchor="end">
										4.2
									</text>
									<text x="30" y="40" font-size="10" text-anchor="end">
										4.4
									</text>
									<text x="30" y="20" font-size="10" text-anchor="end">
										4.6
									</text>

									<!-- X 轴刻度 -->
									<text x="70" y="175" font-size="10" text-anchor="middle">
										3 月
									</text>
									<text x="160" y="175" font-size="10" text-anchor="middle">
										4 月
									</text>
									<text x="250" y="175" font-size="10" text-anchor="middle">
										5 月
									</text>
									<text x="340" y="175" font-size="10" text-anchor="middle">
										6 月
									</text>

									<!-- 数据点和线 -->
									<polyline
										points="70,140 160,100 250,60"
										fill="none"
										stroke="var(--color-primary)"
										stroke-width="2"
									/>

									<!-- 数据点 -->
									<circle cx="70" cy="140" r="4" fill="var(--color-primary)" />
									<circle cx="160" cy="100" r="4" fill="var(--color-primary)" />
									<circle cx="250" cy="60" r="4" fill="var(--color-primary)" />

									<!-- 目标线 -->
									<line
										x1="40"
										y1="20"
										x2="380"
										y2="20"
										stroke="#ff9800"
										stroke-width="1"
										stroke-dasharray="5,5"
									/>
									<text x="385" y="20" font-size="10" fill="#ff9800">4.8</text>
								</svg>
							</div>
						</div>
						<div class="note-list">
							<div class="note-item">
								<div class="note-header">
									<div class="note-author">5 月更新</div>
									<div class="note-date">2023/05/12</div>
								</div>
								<div class="note-content">
									满意度评分达到 4.3 分，相比四月份提高了 0.3 分。主要改进在 UI 交互流畅度和界面美观度方面。
								</div>
							</div>
							<div class="note-item">
								<div class="note-header">
									<div class="note-author">4 月更新</div>
									<div class="note-date">2023/04/10</div>
								</div>
								<div class="note-content">
									满意度评分达到 4.0 分，相比基准值提高了 0.2 分。用户对新功能反馈积极，但对响应速度仍有抱怨。
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- 关联任务 -->
			<div class="tab-content hidden" id="tasks-tab">
				<div class="task-list">
					<div class="task-item">
						<div class="task-name">
							<i class="fas fa-check-circle text-green-500"></i>
							<span>设计用户反馈问卷</span>
						</div>
						<div class="task-status status-completed">已完成</div>
					</div>
					<div class="task-item">
						<div class="task-name">
							<i class="fas fa-spinner text-blue-500"></i>
							<span>收集至少 100 份调查数据</span>
						</div>
						<div class="task-status status-in-progress">进行中</div>
					</div>
					<div class="task-item">
						<div class="task-name">
							<i class="fas fa-spinner text-blue-500"></i>
							<span>分析用户反馈结果</span>
						</div>
						<div class="task-status status-in-progress">进行中</div>
					</div>
					<div class="task-item">
						<div class="task-name">
							<i class="far fa-circle text-gray-400"></i>
							<span>确定改进措施</span>
						</div>
						<div class="task-status status-pending">待开始</div>
					</div>
				</div>
				<div class="mt-4 flex justify-center">
					<button
						class="px-4 py-2 bg-gray-100 text-gray-600 rounded-md text-sm flex items-center"
					>
						<i class="fas fa-plus mr-2"></i> 添加关联任务
					</button>
				</div>
			</div>

			<!-- 里程碑 -->
			<div class="tab-content hidden" id="milestones-tab">
				<div class="milestone-list">
					<div class="milestone-item completed">
						<div class="milestone-date">2023/03/15</div>
						<div class="milestone-title">完成基准调查</div>
						<div class="milestone-desc">
							建立用户满意度的基准值（当前为 3.8 分）
						</div>
					</div>
					<div class="milestone-item completed">
						<div class="milestone-date">2023/04/15</div>
						<div class="milestone-title">首次改进后调查</div>
						<div class="milestone-desc">
							评估初步改进措施的效果（目标达到 4.0 分）
						</div>
					</div>
					<div class="milestone-item active">
						<div class="milestone-date">2023/05/15</div>
						<div class="milestone-title">二次改进后调查</div>
						<div class="milestone-desc">
							评估进一步改进措施的效果（目标达到 4.3 分）
						</div>
					</div>
					<div class="milestone-item">
						<div class="milestone-date">2023/06/15</div>
						<div class="milestone-title">最终评估调查</div>
						<div class="milestone-desc">
							最终评估所有改进措施的综合效果（目标达到 4.8 分）
						</div>
					</div>
				</div>
			</div>

			<!-- 注释记录 -->
			<div class="tab-content hidden" id="notes-tab">
				<div class="note-list">
					<div class="note-item">
						<div class="note-header">
							<div class="note-author">张经理</div>
							<div class="note-date">2023/05/15</div>
						</div>
						<div class="note-content">
							5 月份的调查结果显示用户对新界面反应良好，但仍有改进空间。需要重点解决响应速度问题，特别是在数据加载方面的延迟。
						</div>
					</div>
					<div class="note-item">
						<div class="note-header">
							<div class="note-author">李工程师</div>
							<div class="note-date">2023/04/20</div>
						</div>
						<div class="note-content">
							已经开始优化数据库查询和缓存策略，预计可以将响应时间缩短 30%。下周会部署这些优化。
						</div>
					</div>
					<div class="note-item">
						<div class="note-header">
							<div class="note-author">王设计师</div>
							<div class="note-date">2023/04/18</div>
						</div>
						<div class="note-content">
							完成了界面元素的视觉优化，增加了动画过渡效果，用户反馈很积极。下一步将优化移动端的交互体验。
						</div>
					</div>
				</div>
				<div class="mt-4">
					<textarea
						class="w-full border border-gray-300 rounded-lg p-3 text-sm"
						rows="3"
						placeholder="添加新注释..."
					></textarea>
					<div class="flex justify-end mt-2">
						<button
							class="px-4 py-2 bg-gray-100 text-gray-600 rounded-md text-sm"
						>
							添加注释
						</button>
					</div>
				</div>
			</div>
		</div>

		<!-- 底部操作栏 -->
		<div class="footer">
			<button class="action-btn secondary-btn" id="add-task-btn">
				<i class="fas fa-plus-circle mr-2"></i> 添加任务
			</button>
			<button class="action-btn primary-btn">
				<i class="fas fa-chart-line mr-2"></i> 更新进度
			</button>
		</div>

		<!-- 添加任务弹窗 -->
		<div id="add-task-modal" class="modal">
			<div class="modal-content">
				<div class="modal-header">
					<h3 class="modal-title">添加关联任务</h3>
					<button class="modal-close" id="close-task-modal">&times;</button>
				</div>
				<div class="modal-body">
					<div class="form-group">
						<label for="task-name">任务名称</label>
						<input type="text" id="task-name" class="form-input" placeholder="请输入任务名称">
					</div>
					<div class="form-group">
						<label>关联的关键结果</label>
						<div class="related-kr">
							<div class="kr-info">
								<div class="kr-name">用户满意度评分达到 4.8（满分 5 分）</div>
								<div class="kr-progress">当前进度：4.3/4.8 (90%)</div>
							</div>
						</div>
					</div>
					<div class="form-group">
						<label for="task-date">任务日期</label>
						<input type="date" id="task-date" class="form-input">
					</div>
					<div class="form-group">
						<label>任务循环</label>
						<div class="cycle-options">
							<label class="cycle-option">
								<input type="radio" name="cycle" value="none" checked>
								<span>不循环</span>
							</label>
							<label class="cycle-option">
								<input type="radio" name="cycle" value="daily">
								<span>每天</span>
							</label>
							<label class="cycle-option">
								<input type="radio" name="cycle" value="weekly">
								<span>每周</span>
							</label>
							<label class="cycle-option">
								<input type="radio" name="cycle" value="monthly">
								<span>每月</span>
							</label>
						</div>
					</div>
					<div class="form-group">
						<label for="task-progress">完成后贡献的进度值</label>
						<div class="progress-input-container">
							<input type="number" id="task-progress" class="form-input" min="0" max="100" value="5" step="1">
							<span class="progress-unit">%</span>
						</div>
					</div>
				</div>
				<div class="modal-footer">
					<button class="btn-cancel" id="cancel-task">取消</button>
					<button class="btn-save" id="save-task">保存</button>
				</div>
			</div>
		</div>

		<script>
			document.addEventListener("DOMContentLoaded", function () {
				// 状态栏
				loadStatusBar("status-bar-container");

				// 加载标签栏
				loadTabBar("tab-bar-container", "home");

				// 计算权重贡献
				const progressPercentage = 90; // 当前进度百分比
				const weightPercentage = 30; // 权重百分比
				const weightContribution = (progressPercentage * weightPercentage / 100).toFixed(1);
				
				// 更新权重贡献显示
				const weightValueElem = document.querySelector('.weight-value');
				if (weightValueElem) {
					weightValueElem.textContent = `${weightContribution}% (目标完成度 × 30%)`;
				}

				// 选项卡切换
				const tabs = document.querySelectorAll(".tab");
				const tabContents = document.querySelectorAll(".tab-content");

				tabs.forEach((tab) => {
					tab.addEventListener("click", function () {
						// 移除所有选项卡的活动状态
						tabs.forEach((t) => t.classList.remove("active"));
						// 添加当前选项卡的活动状态
						this.classList.add("active");

						// 隐藏所有内容
						tabContents.forEach((content) => content.classList.add("hidden"));
						// 显示当前选项卡对应的内容
						const tabId = this.getAttribute("data-tab");
						document.getElementById(tabId + "-tab").classList.remove("hidden");
					});
				});

				// 初始化第一个选项卡为活动状态
				if (tabs.length > 0 && tabContents.length > 0) {
					// 确保所有非活动内容都被隐藏
					tabContents.forEach((content, index) => {
						if (index !== 0) { // 第一个内容保持可见
							content.classList.add("hidden");
						}
					});
				}

				// 添加任务模态框
				const addTaskModal = document.getElementById("add-task-modal");
				const addTaskBtn = document.getElementById("add-task-btn");
				const closeTaskModal = document.getElementById("close-task-modal");
				const cancelTask = document.getElementById("cancel-task");
				const saveTask = document.getElementById("save-task");

				// 设置当前日期为默认日期
				const taskDateInput = document.getElementById("task-date");
				const today = new Date();
				const yyyy = today.getFullYear();
				const mm = String(today.getMonth() + 1).padStart(2, '0');
				const dd = String(today.getDate()).padStart(2, '0');
				taskDateInput.value = `${yyyy}-${mm}-${dd}`;

				// 打开模态框
				addTaskBtn.addEventListener("click", function() {
					addTaskModal.classList.add("show");
				});

				// 关闭模态框的各种方式
				function closeModal() {
					addTaskModal.classList.remove("show");
				}

				closeTaskModal.addEventListener("click", closeModal);
				cancelTask.addEventListener("click", closeModal);
				
				// 点击模态框外部关闭
				addTaskModal.addEventListener("click", function(e) {
					if (e.target === addTaskModal) {
						closeModal();
					}
				});

				// 保存任务
				saveTask.addEventListener("click", function() {
					const taskName = document.getElementById("task-name").value;
					const taskDate = document.getElementById("task-date").value;
					const cycleOption = document.querySelector('input[name="cycle"]:checked').value;
					const progressValue = document.getElementById("task-progress").value;
					
					if (!taskName) {
						alert("请输入任务名称");
						return;
					}
					
					// 在这里可以添加保存任务的逻辑
					// 例如将任务添加到任务列表中
					
					// 简单的演示：添加到任务列表
					const taskList = document.querySelector(".task-list");
					const newTask = document.createElement("div");
					newTask.className = "task-item";
					newTask.innerHTML = `
						<div class="task-name">
							<i class="far fa-circle text-gray-400"></i>
							<span>${taskName}</span>
						</div>
						<div class="task-status status-pending">待开始</div>
					`;
					taskList.appendChild(newTask);
					
					// 关闭模态框
					closeModal();
					
					// 切换到任务选项卡
					document.querySelector('.tab[data-tab="tasks"]').click();
				});

				// 接收主题变更消息
				window.addEventListener("message", function (event) {
					if (event.data && event.data.type === "THEME_CHANGE") {
						applyTheme(event.data.theme);
					}
				});

				// 应用主题颜色
				function applyTheme(theme) {
					// 设置主要颜色变量
					document.documentElement.style.setProperty(
						"--color-primary",
						theme.primary
					);
					document.documentElement.style.setProperty(
						"--color-primary-dark",
						adjustBrightness(theme.primary, -15)
					);
					document.documentElement.style.setProperty(
						"--color-primary-light",
						`${theme.primary}20`
					);

					// 更新头部背景
					const krHeader = document.querySelector(".kr-header");
					if (krHeader) {
						krHeader.style.background = `linear-gradient(to right, ${theme.primary}20, rgba(255, 255, 255, 0.8))`;
					}

					// 更新进度条
					const progressFill = document.querySelector(".progress-fill");
					if (progressFill) {
						progressFill.style.background = `linear-gradient(to right, ${theme.primary}, ${theme.primary}80)`;
					}

					// 更新选项卡激活状态颜色
					const activeTabs = document.querySelectorAll(".tab.active");
					activeTabs.forEach((tab) => {
						tab.style.color = theme.primary;
						tab.style.borderBottomColor = theme.primary;
					});

					// 更新里程碑指示点颜色
					const milestonePoints = document.querySelectorAll(
						".milestone-item.active::before, .milestone-item.completed::before"
					);
					milestonePoints.forEach((point) => {
						point.style.backgroundColor = theme.primary;
					});

					// 更新底部按钮
					const primaryBtn = document.querySelector(".primary-btn");
					if (primaryBtn) {
						primaryBtn.style.backgroundColor = theme.primary;
					}

					// 更新保存按钮颜色
					const saveBtn = document.querySelector(".btn-save");
					if (saveBtn) {
						saveBtn.style.backgroundColor = theme.primary;
					}
				}

				// 辅助函数：调整颜色亮度
				function adjustBrightness(hex, percent) {
					let r = parseInt(hex.substring(1, 3), 16);
					let g = parseInt(hex.substring(3, 5), 16);
					let b = parseInt(hex.substring(5, 7), 16);

					r = Math.max(0, Math.min(255, r + percent));
					g = Math.max(0, Math.min(255, g + percent));
					b = Math.max(0, Math.min(255, b + percent));

					const rr = r.toString(16).padStart(2, "0");
					const gg = g.toString(16).padStart(2, "0");
					const bb = b.toString(16).padStart(2, "0");

					return `#${rr}${gg}${bb}`;
				}
			});
		</script>
	</body>
</html>
