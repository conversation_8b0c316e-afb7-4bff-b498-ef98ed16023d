import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import useRecordH5 from '@/hooks/useRecordH5'

// 模拟mediaDevices API
const mockMediaStream = {
  getTracks: jest.fn().mockReturnValue([{ stop: jest.fn() }]),
}

// 模拟RecordRTC
jest.mock('recordrtc', () => {
  return {
    __esModule: true,
    default: jest.fn().mockImplementation(() => ({
      startRecording: jest.fn(),
      pauseRecording: jest.fn(),
      resumeRecording: jest.fn(),
      stopRecording: jest.fn((cb) => cb()),
      getBlob: jest.fn(() => new Blob(['test'], { type: 'audio/wav' })),
      destroy: jest.fn(),
    })),
    StereoAudioRecorder: jest.fn(),
  }
})

describe('useRecordH5', () => {
  // 模拟AudioContext和AnalyserNode
  global.AudioContext = jest.fn().mockImplementation(() => ({
    createAnalyser: jest.fn().mockReturnValue({
      fftSize: 0,
      frequencyBinCount: 128,
      getByteFrequencyData: jest.fn(),
      getByteTimeDomainData: jest.fn((array) => {
        for (let i = 0; i < array.length; i++) {
          array[i] = Math.floor(Math.random() * 255)
        }
      }),
    }),
    createMediaStreamSource: jest.fn().mockReturnValue({
      connect: jest.fn(),
    }),
    close: jest.fn().mockResolvedValue(undefined),
  }))

  // 模拟媒体设备API
  global.navigator.mediaDevices = {
    getUserMedia: jest.fn().mockResolvedValue(mockMediaStream),
  }

  // 模拟URL对象
  global.URL.createObjectURL = jest.fn((blob) => `blob:${blob}`)
  global.URL.revokeObjectURL = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  test('应该初始化所有状态为默认值', async () => {
    const wrapper = mount({
      template: '<div></div>',
      setup() {
        const record = useRecordH5()
        return { record }
      },
    })

    expect(wrapper.vm.record.isRecording.value).toBe(false)
    expect(wrapper.vm.record.isPaused.value).toBe(false)
    expect(wrapper.vm.record.duration.value).toBe(0)
    expect(wrapper.vm.record.volume.value).toBe(0)
    expect(wrapper.vm.record.recordBlob.value).toBe(null)
    expect(wrapper.vm.record.recordURL.value).toBe('')
  })

  test('startRecording 应该正确初始化录音', async () => {
    const wrapper = mount({
      template: '<div></div>',
      setup() {
        const record = useRecordH5()
        return { record }
      },
    })

    await wrapper.vm.record.startRecording()

    expect(navigator.mediaDevices.getUserMedia).toHaveBeenCalledWith({ audio: true })
    expect(wrapper.vm.record.isRecording.value).toBe(true)
    expect(wrapper.vm.record.isPaused.value).toBe(false)
  })

  test('stopRecording 应该停止录音并返回Blob', async () => {
    const wrapper = mount({
      template: '<div></div>',
      setup() {
        const record = useRecordH5()
        return { record }
      },
    })

    await wrapper.vm.record.startRecording()
    const blob = await wrapper.vm.record.stopRecording()

    expect(blob).toBeInstanceOf(Blob)
    expect(wrapper.vm.record.isRecording.value).toBe(false)
    expect(wrapper.vm.record.recordBlob.value).toEqual(blob)
    expect(wrapper.vm.record.recordURL.value).toBeTruthy()
  })

  test('pauseRecording 和 resumeRecording 应该正确更新状态', async () => {
    const wrapper = mount({
      template: '<div></div>',
      setup() {
        const record = useRecordH5()
        return { record }
      },
    })

    await wrapper.vm.record.startRecording()
    wrapper.vm.record.pauseRecording()

    expect(wrapper.vm.record.isPaused.value).toBe(true)

    wrapper.vm.record.resumeRecording()

    expect(wrapper.vm.record.isPaused.value).toBe(false)
  })

  test('cancelRecording 应该重置所有状态', async () => {
    const wrapper = mount({
      template: '<div></div>',
      setup() {
        const record = useRecordH5()
        return { record }
      },
    })

    await wrapper.vm.record.startRecording()
    wrapper.vm.record.cancelRecording()

    expect(wrapper.vm.record.isRecording.value).toBe(false)
    expect(wrapper.vm.record.isPaused.value).toBe(false)
    expect(wrapper.vm.record.duration.value).toBe(0)
    expect(wrapper.vm.record.recordBlob.value).toBe(null)
    expect(wrapper.vm.record.recordURL.value).toBe('')
  })

  test('getAudioWaveform 应该返回音频波形数据', async () => {
    const wrapper = mount({
      template: '<div></div>',
      setup() {
        const record = useRecordH5()
        return { record }
      },
    })

    await wrapper.vm.record.startRecording()
    const waveform = wrapper.vm.record.getAudioWaveform()

    expect(waveform).toBeInstanceOf(Uint8Array)
    expect(waveform.length).toBe(128) // 根据模拟的frequencyBinCount
  })

  test('destroy 应该清理所有资源', async () => {
    const wrapper = mount({
      template: '<div></div>',
      setup() {
        const record = useRecordH5()
        return { record }
      },
    })

    await wrapper.vm.record.startRecording()
    wrapper.vm.record.destroy()

    expect(wrapper.vm.record.isRecording.value).toBe(false)
    expect(mockMediaStream.getTracks()[0].stop).toHaveBeenCalled()
  })
})
