---
description: 
globs: 
alwaysApply: false
---
# UI 规范指南

为了确保整个项目的视觉统一性、可维护性以及未来主题的灵活性，在开发或修改 UI 组件时，请严格遵守以下关于 CSS 变量和样式定义的指南：

## 1. 全局样式变量 (`src/styles/variables.css`)

*   **核心原则**：所有在项目中可能被多处复用的样式值（颜色、间距、字体、阴影、圆角等）都**必须**定义为 CSS 自定义属性（CSS 变量），并统一存放在 `src/styles/variables.css` 文件中。
*   **检查现有变量**：
    *   在定义任何新的样式属性之前，请首先仔细检查 `src/styles/variables.css`，确认是否已存在可复用的变量。
    *   优先复用现有变量，这有助于保持应用整体视觉风格的一致性。
*   **添加新变量**：
    *   **适用场景**：仅当所需样式值具备全局复用性（例如，用于定义应用的基础主题、通用 UI 元素、布局规范等）时，才应将其添加为全局 CSS 变量。
    *   **命名规范**：新添加的变量名必须遵循 `kebab-case` 格式（例如 `--button-background-primary`, `--text-color-secondary`），并能清晰、准确地描述其用途和代表的样式属性。
    *   **组织与分类**：在 `src/styles/variables.css` 中添加新变量时，请将其放置在相关的类别注释下（例如 `/* Colors */`, `/* Spacing */`, `/* Typography */`, `/* Shadows */`, `/* Border Radius */` 等），以便于查找和管理。
    *   **何时不添加**：对于仅在单一组件内部使用、不具备跨组件复用性的特定样式值，应优先考虑使用组件内部的局部 CSS 变量、SCSS/Sass 变量或直接的样式属性，避免不必要的全局污染。

## 2. CSS 变量使用

*   **直接使用**: 在组件的 `<style>` 标签内，应直接通过 `var(--variable-name)` 的方式使用在 `variables.css` 中定义的全局 CSS 变量。
*   **避免 SCSS 别名**: 避免在 SCSS 中仅仅为了给 CSS 变量起别名而定义新的 SCSS 变量（例如，不推荐 `$primary-color: var(--color-primary);`，应直接使用 `var(--color-primary)`）。
*   **派生值**: 如果某些派生值（如主色的不同透明度、RGB表示）在多处使用，也应考虑将其定义为全局 CSS 变量，例如 `--color-primary-rgb: 67, 97, 238;`（用于 `rgba()`）或 `--color-primary-light`。

## 3. 单位使用

*   **布局与间距**: 对于页面布局、元素尺寸、内边距（padding）、外边距（margin）等，**优先使用 `rpx` 单位**，以确保在不同设备屏幕上的良好适配性。
*   **字体大小**: 字体大小可以使用 `px` 单位，需确保整个应用内字体层级和大小的一致性。
*   **边框宽度**: 边框宽度通常使用 `px` 单位。

## 4. 布局与间距

*   **页面内容区域**:
    *   标准内边距 (`.content-area` 或类似容器): `padding: 32rpx;`
    *   若页面底部有固定元素（如操作栏），内容区域的底部内边距可适当增加以避免遮挡，例如 `padding: 32rpx 32rpx 180rpx;` (顶部、左右、底部)。
*   **栅格与对齐**: 遵循统一的间距规范（如基于 8rpx 或 4px 的倍数）。

## 5. 排版 (Typography)

*   **字体家族**: 通过全局 CSS 变量 `--font-sans` (在 `variables.css` 中定义) 统一设置。
*   **字体层级**:
    *   **应用主标题** (例如 `okrList.vue` 中的 `.app-header .title`):
        *   `font-size: 28px;`
        *   `font-weight: 700;`
    *   **页面/模块标题** (例如 `okrEdit.vue` 中的导航栏标题):
        *   `font-size: 20px;`
        *   `font-weight: 600;`
    *   **卡片内标题** (例如 `.objective-title`):
        *   `font-size: 17px;`
        *   `font-weight: 600;`
    *   **正文/主要输入文本** (例如常规文本、输入框内文字):
        *   `font-size: 16px;`
        *   `font-weight: normal;` (或 `400`)
    *   **辅助/元信息文本** (例如日期、状态、小提示):
        *   `font-size: 13px;`
        *   `color: var(--color-gray-500);` (或相应的次要文本颜色变量)
    *   **更小级别的提示/标签文本**:
        *   `font-size: 12px;` (谨慎使用，确保可读性)
*   **行高**: 根据字体大小设置合适的行高，如 `line-height: 1.5;` 或 `1.6;`。

## 6. 卡片 (Cards)

*   **圆角**: `border-radius: 20px;` (或 `var(--rounded-xl)` 如果该变量定义为 `20px`)
*   **内边距**: 卡片内部内容区域的 `padding: 20px;` (或 `40rpx`)
*   **阴影**: `box-shadow: var(--shadow-sm);` (默认卡片阴影)

## 7. 按钮 (Buttons)

*   **主要操作按钮** (例如 `.create-btn`):
    *   `border-radius: 20px;` (与卡片圆角协调)
    *   `padding: 16px;` (或 `32rpx` 垂直方向, 水平方向可根据内容调整)
    *   `font-size: 16px;`
    *   `font-weight: 600;`
    *   `background-color: var(--color-primary);`
    *   `color: var(--color-white);`
    *   `box-shadow: 0 4px 12px rgba(var(--color-primary-rgb), 0.3);` (需要 `--color-primary-rgb` 定义在 `variables.css`)
*   **次要/图标按钮**:
    *   `border-radius: var(--rounded-full);` (或 `50%` 实现圆形)
    *   尺寸应统一，例如 `width: 32px; height: 32px;` 或通过 `padding` 控制点击区域。
    *   背景色、前景色根据具体场景使用全局颜色变量。
*   **标签式按钮/筛选器** (例如 `okrList.vue` 的 `.filter-tab`):
    *   `border-radius: var(--rounded-md);`
    *   激活状态使用主色相关的变量，如 `background: linear-gradient(135deg, var(--color-primary-light), var(--color-primary));`

## 8. 颜色 (Colors)

*   所有颜色值**必须**通过 `variables.css` 中定义的 CSS 变量引用。
*   例如: `var(--color-primary)`, `var(--color-accent)`, `var(--text-color)`, `var(--bg-color)`, `var(--color-gray-100)`, `var(--color-white)`, `var(--color-success)`.
*   确保有明确的文本颜色体系，如 `--text-color-primary`, `--text-color-secondary`, `--text-color-disabled`。

## 9. 阴影 (Shadows)

*   使用 `variables.css` 中定义的阴影变量。
*   `var(--shadow-sm)`: 用于常规卡片、元素的轻微抬升感。
*   `var(--shadow-md)`: 用于需要更明显区分的元素。
*   `var(--shadow-lg)`: 用于模态框、弹出层等需要强调用层级的元素。
*   特定组件的强调阴影（如按钮激活）：可使用如 `0 4px 12px rgba(var(--color-primary-rgb), 0.3);` 的形式，确保 `--color-primary-rgb` 已定义。

## 10. 圆角 (Border Radius)

*   使用 `variables.css` 中定义的圆角变量。
*   `var(--rounded-sm)`, `var(--rounded-md)`, `var(--rounded-lg)`, `var(--rounded-xl)`, `var(--rounded-full)`.
*   **具体应用参考**:
    *   卡片: `20px` (可定义为 `--rounded-card` 或直接使用 `--rounded-xl` 若其值为 `20px`)
    *   按钮 (常规): `20px` 或 `--rounded-xl`
    *   按钮 (圆形/图标): `var(--rounded-full)`
    *   输入框: `var(--rounded-md)` 或 `var(--rounded-lg)`

## 11. 图标 (Icons)

*   **推荐方式**: 使用字体图标库（如 Font Awesome）并通过 `<i>` 标签和相应的 CSS 类名来集成 (例如 `<i class="fas fa-plus"></i>`)。
*   **全局引入**: 确保图标库的 CSS 文件在项目中全局引入。
*   **避免组件内定义**: 不要在组件的局部样式中通过 `content: "000";` 等方式定义图标，这不利于管理和维护。

## 12. SCSS/Sass 使用

*   SCSS/Sass 的嵌套、混入（mixins）等特性可以按需使用，以提高代码的可读性和复用性。
*   但如前所述，避免使用 SCSS 变量简单地作为 CSS 全局变量的别名。

---

本指南旨在提供一个清晰、一致的 UI 开发框架。在实际开发中，请灵活运用这些规则，并在遇到未覆盖的场景时，与团队讨论并补充本规范。

