const keywordsBank = [
  '友谊',
  '冒险',
  '勇气',
  '挑战',
  '成长',
  '梦想',
  '家庭',
  '失落',
  '发现',
  '奇迹',
  '时间',
  '旅行',
  '秘密',
  '希望',
  '孤独',
  '自由',
  '魔法',
  '未来',
  '过去',
  '选择',
  '城市',
  '森林',
  '海洋',
  '山脉',
  '星空',
  '机器人',
  '外星人',
  '英雄',
  '怪物',
  '宝藏',
  '音乐',
  '艺术',
  '科技',
  '自然',
  '爱情',
  '背叛',
  '救赎',
  '牺牲',
  '智慧',
  '疯狂',
  '坚持',
  '善良',
  '嫉妒',
  '离别',
  '重逢',
  '遗忘',
  '记忆',
  '宿命',
  '改变',
  '永恒',
  '回忆',
  '奋斗',
  '成功',
  '失败',
  '机遇',
  '命运',
  '奇遇',
  '传说',
  '神话',
  '寓言',
  '黎明',
  '黄昏',
  '四季',
  '宇宙',
  '深渊',
  '废墟',
  '城堡',
  '迷宫',
  '花园',
  '乐园',
  '灵魂',
  '心灵',
  '人性',
  '欲望',
  '谎言',
  '真相',
  '和平',
  '战争',
  '信仰',
  '怀疑',
  '力量',
  '脆弱',
  '美丽',
  '丑陋',
  '光明',
  '黑暗',
  '新生',
  '死亡',
  '欢乐',
  '悲伤',
  '荣耀',
  '耻辱',
  '承诺',
  '誓言',
  '循环',
  '突破',
  '觉醒',
  '沉睡',
  '守护',
  '追逐',
  '灵感',
  '创作',
  '毁灭',
  '重生',
  '治愈',
  '伤痛',
  '微笑',
  '眼泪',
  '陪伴',
  '起点',
  '终点',
  '旅途',
  '归宿',
  '枷锁',
  '解放',
  '现实',
  '虚幻',
  '矛盾',
  '和谐',
  '风暴',
  '宁静',
  '火焰',
  '冰霜',
  '尘埃',
  '星辰',
  '低语',
  '呐喊',
  '沉默',
  '喧嚣',
  '纯真',
  '世故',
  '迷茫',
  '坚定',
  '等待',
  '追寻',
  '相遇',
  '错过',
  '遗迹',
  '预言',
  '契约',
  '忠诚',
  '欺骗',
  '规则',
  '混沌',
  '秩序',
  '混乱',
  '倒影',
  '实体',
  '面具',
  '自我',
  '幻象',
  '本质',
  '边界',
  '融合',
  '分裂',
  '统一',
  '回响',
  '寂静',
  '河流',
  '沙漠',
  '岛屿',
  '大陆',
  '天空',
  '大地',
  '永昼',
  '永夜',
  '恶棍',
  '凡人',
  '神明',
  '天使',
  '恶魔',
  '幽灵',
  '精灵',
  '巨人',
  '矮人',
  '低谷',
  '巅峰',
  '繁华',
  '荒芜',
  '盛开',
  '凋零',
  '温暖',
  '寒冷',
  '阴影',
  '禁忌',
  '探索',
  '传承',
  '创新',
  '顺从',
  '反抗',
  '依赖',
  '独立',
  '完美',
  '残缺',
  '低谷',
  '巅峰',
  '繁华',
  '荒芜',
  '盛开',
  '凋零',
]

/**
 * 获取指定数量的随机关键词
 * @param {number} count - 需要获取的关键词数量，默认为 3
 * @returns {string[]} 随机关键词数组
 */
export const getRandomKeywords = (count = 3) => {
  if (count > keywordsBank.length) {
    console.warn(`请求的关键词数量 (${count}) 大于关键词库的总数 (${keywordsBank.length})。将返回所有关键词。`)
    return [...keywordsBank]
  }

  const shuffled = [...keywordsBank].sort(() => 0.5 - Math.random())
  return shuffled.slice(0, count)
}
