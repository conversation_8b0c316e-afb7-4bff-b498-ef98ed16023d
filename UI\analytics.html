<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>数据分析 - OKR助手</title>
		<link
			href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css"
			rel="stylesheet"
		/>
		<link
			rel="stylesheet"
			href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
		/>
		<!-- 引入共享组件 -->
		<script>
			// 动态加载 shared-components.html
			(function () {
				const xhr = new XMLHttpRequest();
				xhr.open("GET", "shared-components.html", true);
				xhr.onreadystatechange = function () {
					if (xhr.readyState === 4 && xhr.status === 200) {
						// 创建临时容器
						const tempDiv = document.createElement("div");
						tempDiv.innerHTML = xhr.responseText;

						// 提取 style 标签并添加到 head
						const styleTags = tempDiv.getElementsByTagName("style");
						for (let i = 0; i < styleTags.length; i++) {
							const styleContent = styleTags[i].textContent;
							const style = document.createElement("style");
							style.textContent = styleContent;
							document.head.appendChild(style);
						}

						// 提取script标签内容并执行
						const scriptTags = tempDiv.getElementsByTagName("script");
						if (scriptTags.length > 0) {
							const scriptContent = scriptTags[0].textContent;
							const script = document.createElement("script");
							script.textContent = scriptContent;
							document.head.appendChild(script);

							// 等待脚本加载后初始化标签栏
							setTimeout(function () {
								loadTabBar("tab-bar-container", "analytics");
							}, 100);
						}

						// 添加模板到页面
						const templates = tempDiv.getElementsByTagName("template");
						for (let i = 0; i < templates.length; i++) {
							document.body.appendChild(templates[i].cloneNode(true));
						}
					}
				};
				xhr.send();
			})();
		</script>
		<style>
			@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap");

			body {
				font-family: "Poppins", -apple-system, BlinkMacSystemFont, "Segoe UI",
					Roboto, Helvetica, Arial, sans-serif;
				background: var(--color-bg);
				height: 100vh;
				display: flex;
				flex-direction: column;
				scrollbar-width: thin; /* Firefox */
				scrollbar-color: #a0a0a0 #f1f1f1; /* Firefox */
			}

			/* Webkit browsers (Chrome, Safari, Edge) */
			::-webkit-scrollbar {
				width: 5px;
			}

			::-webkit-scrollbar-track {
				background: rgba(241, 241, 241, 0.5);
				border-radius: 6px;
			}

			::-webkit-scrollbar-thumb {
				background: rgba(160, 160, 160, 0.5);
				border-radius: 6px;
			}

			::-webkit-scrollbar-thumb:hover {
				background: #888;
			}

			.content-area {
				flex: 1;
				overflow-y: auto;
				padding-bottom: 85px;
				max-width: 800px;
				margin: 0 auto;
				width: 100%;
			}

			.app-header {
				padding: 20px;
				background: var(--color-white);
				border-radius: 0 0 var(--rounded-lg) var(--rounded-lg);
				margin-bottom: 16px;
				box-shadow: var(--shadow-sm);
			}

			.page-title {
				font-weight: 700;
				font-size: 28px;
				margin-bottom: 16px;
				background: linear-gradient(
					120deg,
					var(--color-primary),
					var(--color-primary-dark)
				);
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
				letter-spacing: -0.5px;
			}

			.segment-control {
				display: flex;
				background-color: var(--color-gray-100);
				border-radius: var(--rounded-lg);
				padding: 4px;
				margin-bottom: 20px;
				box-shadow: var(--shadow-sm);
				border: none;
			}

			.segment-item {
				flex: 1;
				text-align: center;
				padding: 12px 0;
				font-size: 14px;
				border-radius: calc(var(--rounded-lg) - 4px);
				transition: all 0.3s ease;
				font-weight: 500;
				color: var(--color-gray-600);
			}

			.segment-item.active {
				background: linear-gradient(
					120deg,
					var(--color-primary),
					var(--color-primary-dark)
				);
				color: white;
				box-shadow: 0 4px 10px rgba(94, 106, 210, 0.25);
				transform: translateY(-1px);
			}

			.section-card {
				background: var(--color-white);
				border-radius: var(--rounded-lg);
				margin: 0 20px 20px;
				padding: 22px;
				box-shadow: var(--shadow-sm);
				transition: all 0.3s ease;
			}

			.section-card:hover {
				transform: translateY(-3px);
				box-shadow: var(--shadow-md);
			}

			.section-title {
				font-size: 18px;
				font-weight: 600;
				color: var(--color-gray-800);
				margin-bottom: 16px;
				display: flex;
				align-items: center;
			}

			.section-title i {
				margin-right: 10px;
				color: var(--color-primary);
				font-size: 20px;
			}

			.time-block {
				background: var(--color-gray-100);
				border-radius: var(--rounded-md);
				padding: 16px;
				margin-bottom: 12px;
				transition: all 0.2s ease;
			}

			.time-block:hover {
				background: var(--color-gray-200);
			}

			.time-block-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 10px;
			}

			.time-block-title {
				font-weight: 600;
				color: var(--color-gray-800);
				font-size: 16px;
			}

			.time-block-time {
				font-size: 14px;
				color: var(--color-gray-600);
				font-weight: 500;
			}

			.time-block-category {
				display: inline-block;
				padding: 4px 10px;
				border-radius: 20px;
				font-size: 12px;
				font-weight: 500;
				margin-right: 8px;
			}

			.category-work {
				background-color: rgba(94, 106, 210, 0.1);
				color: var(--color-primary);
			}

			.category-meeting {
				background-color: rgba(78, 168, 222, 0.1);
				color: var(--color-secondary);
			}

			.category-break {
				background-color: rgba(255, 174, 87, 0.1);
				color: var(--color-warning);
			}

			.progress-row {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 20px;
			}

			.progress-circle {
				width: 120px;
				height: 120px;
				border-radius: 50%;
				background: conic-gradient(
					var(--color-primary) 0% 65%,
					var(--color-gray-200) 65% 100%
				);
				display: flex;
				align-items: center;
				justify-content: center;
				position: relative;
				box-shadow: var(--shadow-sm);
			}

			.progress-circle::before {
				content: "";
				position: absolute;
				width: 90px;
				height: 90px;
				border-radius: 50%;
				background-color: var(--color-white);
			}

			.progress-content {
				position: relative;
				z-index: 1;
				text-align: center;
			}

			.progress-percentage {
				font-size: 24px;
				font-weight: 600;
				color: var(--color-primary);
			}

			.progress-label {
				font-size: 12px;
				color: var(--color-gray-600);
				margin-top: 4px;
			}

			.progress-details {
				flex: 1;
				margin-left: 20px;
			}

			.progress-item {
				margin-bottom: 12px;
			}

			.progress-item-header {
				display: flex;
				justify-content: space-between;
				margin-bottom: 6px;
			}

			.progress-item-title {
				font-weight: 500;
				color: var(--color-gray-700);
				font-size: 14px;
			}

			.progress-item-value {
				font-weight: 600;
				color: var(--color-gray-800);
				font-size: 14px;
			}

			.chart-bar {
				height: 8px;
				background-color: var(--color-gray-200);
				border-radius: 4px;
				overflow: hidden;
			}

			.chart-fill {
				height: 100%;
				border-radius: 4px;
				background: linear-gradient(
					to right,
					var(--color-primary-light),
					var(--color-primary)
				);
			}

			.chart-fill.success {
				background: linear-gradient(to right, #56c993, #3aab7a);
			}

			.chart-fill.warning {
				background: linear-gradient(to right, #ffae57, #ff9636);
			}

			.chart-fill.danger {
				background: linear-gradient(to right, #ff7c7c, #ff5252);
			}

			.chart-grid {
				display: grid;
				grid-template-columns: repeat(7, 1fr);
				gap: 8px;
				margin-top: 20px;
			}

			.chart-day {
				text-align: center;
			}

			.chart-day-bar {
				height: 100px;
				background-color: var(--color-gray-200);
				border-radius: var(--rounded-md);
				position: relative;
				margin-bottom: 8px;
				overflow: hidden;
			}

			.chart-day-fill {
				position: absolute;
				bottom: 0;
				left: 0;
				width: 100%;
				border-radius: 0 0 var(--rounded-md) var(--rounded-md);
				background: linear-gradient(
					to top,
					var(--color-primary),
					var(--color-primary-light)
				);
			}

			.chart-day-label {
				font-size: 12px;
				color: var(--color-gray-600);
				font-weight: 500;
			}

			.summary-stats {
				display: grid;
				grid-template-columns: repeat(2, 1fr);
				gap: 16px;
				margin-top: 20px;
			}

			.stat-card {
				background: var(--color-gray-100);
				border-radius: var(--rounded-md);
				padding: 16px;
				text-align: center;
			}

			.stat-card:hover {
				background: var(--color-gray-200);
			}

			.stat-value {
				font-size: 28px;
				font-weight: 700;
				color: var(--color-primary);
				margin-bottom: 4px;
			}

			.stat-label {
				font-size: 14px;
				color: var(--color-gray-600);
				font-weight: 500;
			}

			/* 日期选择器样式 */
			.date-selector {
				margin-bottom: 10px;
				padding: 15px;
			}

			.date-nav {
				display: flex;
				justify-content: space-between;
				align-items: center;
			}

			.date-nav-btn {
				width: 36px;
				height: 36px;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				background: var(--color-gray-100);
				border: none;
				cursor: pointer;
				color: var(--color-gray-600);
				transition: all 0.2s ease;
			}

			.date-nav-btn:hover {
				background: var(--color-gray-200);
				color: var(--color-primary);
			}

			.current-date {
				font-size: 18px;
				font-weight: 600;
				color: var(--color-gray-800);
				text-align: center;
				flex: 1;
				padding: 0 10px;
				background: linear-gradient(
					120deg,
					var(--color-primary),
					var(--color-primary-dark)
				);
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
			}
		</style>
	</head>
	<body>
		<!-- 状态栏 -->
		<div id="status-bar-container"></div>

		<!-- 内容区域 -->
		<div class="content-area">
			<!-- 顶部区域 -->
			<div class="app-header">
				<h1 class="page-title">数据分析</h1>
				<div class="segment-control">
					<div class="segment-item active">天</div>
					<div class="segment-item">周</div>
					<div class="segment-item">月</div>
				</div>
			</div>

			<!-- 日统计部分 -->
			<div id="day-section">
				<!-- 日期选择器 -->
				<div class="section-card date-selector">
					<div class="date-nav">
						<button class="date-nav-btn" onclick="changeDate('day', -1)">
							<i class="fas fa-chevron-left"></i>
						</button>
						<div class="current-date" id="current-day">2023 年 10 月 15 日</div>
						<button class="date-nav-btn" onclick="changeDate('day', 1)">
							<i class="fas fa-chevron-right"></i>
						</button>
					</div>
				</div>

				<!-- 今日数据卡片 -->
				<div class="section-card">
					<h2 class="section-title">
						<i class="fas fa-chart-line"></i>
						今日生产力
					</h2>
					<div class="progress-row">
						<div class="progress-circle">
							<div class="progress-content">
								<div class="progress-percentage">70%</div>
								<div class="progress-label">今日效率</div>
							</div>
						</div>
						<div class="progress-details">
							<div class="progress-item">
								<div class="progress-item-header">
									<div class="progress-item-title">目标进度</div>
									<div class="progress-item-value">68%</div>
								</div>
								<div class="chart-bar">
									<div class="chart-fill" style="width: 68%"></div>
								</div>
							</div>
							<div class="progress-item">
								<div class="progress-item-header">
									<div class="progress-item-title">任务完成率</div>
									<div class="progress-item-value">75%</div>
								</div>
								<div class="chart-bar">
									<div class="chart-fill success" style="width: 75%"></div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- 今日任务统计 -->
				<div class="section-card">
					<h2 class="section-title">
						<i class="fas fa-tasks"></i>
						今日任务情况
					</h2>
					<div class="time-block">
						<div class="time-block-header">
							<div class="time-block-title">产品设计讨论</div>
							<div class="time-block-time">已完成</div>
						</div>
						<div>
							<span class="time-block-category category-work">工作</span>
							<span class="time-block-category category-meeting">会议</span>
						</div>
					</div>
					<div class="time-block">
						<div class="time-block-header">
							<div class="time-block-title">UI 原型设计</div>
							<div class="time-block-time">进行中</div>
						</div>
						<div>
							<span class="time-block-category category-work">工作</span>
						</div>
					</div>
					<div class="time-block">
						<div class="time-block-header">
							<div class="time-block-title">代码优化</div>
							<div class="time-block-time">待开始</div>
						</div>
						<div>
							<span class="time-block-category category-work">常规</span>
						</div>
					</div>
				</div>

				<!-- 今日统计 -->
				<div class="section-card">
					<h2 class="section-title">
						<i class="fas fa-chart-pie"></i>
						今日总结
					</h2>

					<div class="summary-stats">
						<div class="stat-card">
							<div class="stat-value">3</div>
							<div class="stat-label">待完成任务</div>
						</div>
						<div class="stat-card">
							<div class="stat-value">3</div>
							<div class="stat-label">完成任务</div>
						</div>
						<div class="stat-card">
							<div class="stat-value">2</div>
							<div class="stat-label">进行中任务</div>
						</div>
						<div class="stat-card">
							<div class="stat-value">68%</div>
							<div class="stat-label">目标进度</div>
						</div>
					</div>
				</div>
			</div>

			<!-- 周统计部分 -->
			<div id="week-section" style="display: none">
				<!-- 周选择器 -->
				<div class="section-card date-selector">
					<div class="date-nav">
						<button class="date-nav-btn" onclick="changeDate('week', -1)">
							<i class="fas fa-chevron-left"></i>
						</button>
						<div class="current-date" id="current-week">
							2023 年第 42 周 (10 月 9 日 -10 月 15 日)
						</div>
						<button class="date-nav-btn" onclick="changeDate('week', 1)">
							<i class="fas fa-chevron-right"></i>
						</button>
					</div>
				</div>

				<!-- 本周生产力卡片 -->
				<div class="section-card">
					<h2 class="section-title">
						<i class="fas fa-chart-line"></i>
						本周生产力
					</h2>
					<div class="progress-row">
						<div class="progress-circle">
							<div class="progress-content">
								<div class="progress-percentage">65%</div>
								<div class="progress-label">周平均效率</div>
							</div>
						</div>
						<div class="progress-details">
							<div class="progress-item">
								<div class="progress-item-header">
									<div class="progress-item-title">目标完成率</div>
									<div class="progress-item-value">72%</div>
								</div>
								<div class="chart-bar">
									<div class="chart-fill success" style="width: 72%"></div>
								</div>
							</div>
							<div class="progress-item">
								<div class="progress-item-header">
									<div class="progress-item-title">任务完成率</div>
									<div class="progress-item-value">58%</div>
								</div>
								<div class="chart-bar">
									<div class="chart-fill warning" style="width: 58%"></div>
								</div>
							</div>
						</div>
					</div>

					<div class="chart-grid">
						<div class="chart-day">
							<div class="chart-day-bar">
								<div class="chart-day-fill" style="height: 45%"></div>
							</div>
							<div class="chart-day-label">一</div>
						</div>
						<div class="chart-day">
							<div class="chart-day-bar">
								<div class="chart-day-fill" style="height: 70%"></div>
							</div>
							<div class="chart-day-label">二</div>
						</div>
						<div class="chart-day">
							<div class="chart-day-bar">
								<div class="chart-day-fill" style="height: 60%"></div>
							</div>
							<div class="chart-day-label">三</div>
						</div>
						<div class="chart-day">
							<div class="chart-day-bar">
								<div class="chart-day-fill" style="height: 85%"></div>
							</div>
							<div class="chart-day-label">四</div>
						</div>
						<div class="chart-day">
							<div class="chart-day-bar">
								<div class="chart-day-fill" style="height: 75%"></div>
							</div>
							<div class="chart-day-label">五</div>
						</div>
						<div class="chart-day">
							<div class="chart-day-bar">
								<div class="chart-day-fill" style="height: 40%"></div>
							</div>
							<div class="chart-day-label">六</div>
						</div>
						<div class="chart-day">
							<div class="chart-day-bar">
								<div class="chart-day-fill" style="height: 30%"></div>
							</div>
							<div class="chart-day-label">日</div>
						</div>
					</div>
				</div>

				<!-- 本周目标进度 -->
				<div class="section-card">
					<h2 class="section-title">
						<i class="fas fa-bullseye"></i>
						本周目标进度
					</h2>
					<div class="progress-row">
						<div class="progress-circle">
							<div class="progress-content">
								<div class="progress-percentage">72%</div>
								<div class="progress-label">总体进度</div>
							</div>
						</div>
						<div class="progress-details">
							<div class="progress-item">
								<div class="progress-item-header">
									<div class="progress-item-title">重要目标 A</div>
									<div class="progress-item-value">85%</div>
								</div>
								<div class="chart-bar">
									<div class="chart-fill success" style="width: 85%"></div>
								</div>
							</div>
							<div class="progress-item">
								<div class="progress-item-header">
									<div class="progress-item-title">重要目标 B</div>
									<div class="progress-item-value">65%</div>
								</div>
								<div class="chart-bar">
									<div class="chart-fill" style="width: 65%"></div>
								</div>
							</div>
							<div class="progress-item">
								<div class="progress-item-header">
									<div class="progress-item-title">次要目标 C</div>
									<div class="progress-item-value">45%</div>
								</div>
								<div class="chart-bar">
									<div class="chart-fill warning" style="width: 45%"></div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- 本周总结 -->
				<div class="section-card">
					<h2 class="section-title">
						<i class="fas fa-chart-pie"></i>
						本周总结
					</h2>

					<div class="summary-stats">
						<div class="stat-card">
							<div class="stat-value">15</div>
							<div class="stat-label">待完成任务</div>
						</div>
						<div class="stat-card">
							<div class="stat-value">18</div>
							<div class="stat-label">完成任务</div>
						</div>
						<div class="stat-card">
							<div class="stat-value">72%</div>
							<div class="stat-label">目标进度</div>
						</div>
						<div class="stat-card">
							<div class="stat-value">4</div>
							<div class="stat-label">进行中项目</div>
						</div>
					</div>
				</div>
			</div>

			<!-- 月统计部分 -->
			<div id="month-section" style="display: none">
				<!-- 月选择器 -->
				<div class="section-card date-selector">
					<div class="date-nav">
						<button class="date-nav-btn" onclick="changeDate('month', -1)">
							<i class="fas fa-chevron-left"></i>
						</button>
						<div class="current-date" id="current-month">2023 年 10 月</div>
						<button class="date-nav-btn" onclick="changeDate('month', 1)">
							<i class="fas fa-chevron-right"></i>
						</button>
					</div>
				</div>

				<!-- 本月数据卡片 -->
				<div class="section-card">
					<h2 class="section-title">
						<i class="fas fa-chart-line"></i>
						本月效率分析
					</h2>
					<div class="progress-row">
						<div class="progress-circle">
							<div class="progress-content">
								<div class="progress-percentage">78%</div>
								<div class="progress-label">月平均效率</div>
							</div>
						</div>
						<div class="progress-details">
							<div class="progress-item">
								<div class="progress-item-header">
									<div class="progress-item-title">第一周</div>
									<div class="progress-item-value">65%</div>
								</div>
								<div class="chart-bar">
									<div class="chart-fill" style="width: 65%"></div>
								</div>
							</div>
							<div class="progress-item">
								<div class="progress-item-header">
									<div class="progress-item-title">第二周</div>
									<div class="progress-item-value">70%</div>
								</div>
								<div class="chart-bar">
									<div class="chart-fill" style="width: 70%"></div>
								</div>
							</div>
							<div class="progress-item">
								<div class="progress-item-header">
									<div class="progress-item-title">第三周</div>
									<div class="progress-item-value">85%</div>
								</div>
								<div class="chart-bar">
									<div class="chart-fill success" style="width: 85%"></div>
								</div>
							</div>
							<div class="progress-item">
								<div class="progress-item-header">
									<div class="progress-item-title">第四周</div>
									<div class="progress-item-value">88%</div>
								</div>
								<div class="chart-bar">
									<div class="chart-fill success" style="width: 88%"></div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- 本月目标完成情况 -->
				<div class="section-card">
					<h2 class="section-title">
						<i class="fas fa-bullseye"></i>
						本月目标完成情况
					</h2>
					<div class="progress-row">
						<div class="progress-circle">
							<div class="progress-content">
								<div class="progress-percentage">80%</div>
								<div class="progress-label">总体完成率</div>
							</div>
						</div>
						<div class="progress-details">
							<div class="progress-item">
								<div class="progress-item-header">
									<div class="progress-item-title">季度重点项目</div>
									<div class="progress-item-value">90%</div>
								</div>
								<div class="chart-bar">
									<div class="chart-fill success" style="width: 90%"></div>
								</div>
							</div>
							<div class="progress-item">
								<div class="progress-item-header">
									<div class="progress-item-title">技能提升目标</div>
									<div class="progress-item-value">75%</div>
								</div>
								<div class="chart-bar">
									<div class="chart-fill success" style="width: 75%"></div>
								</div>
							</div>
							<div class="progress-item">
								<div class="progress-item-header">
									<div class="progress-item-title">团队协作目标</div>
									<div class="progress-item-value">85%</div>
								</div>
								<div class="chart-bar">
									<div class="chart-fill success" style="width: 85%"></div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- 本月总结 -->
				<div class="section-card">
					<h2 class="section-title">
						<i class="fas fa-chart-pie"></i>
						本月总结
					</h2>

					<div class="summary-stats">
						<div class="stat-card">
							<div class="stat-value">65</div>
							<div class="stat-label">完成任务总数</div>
						</div>
						<div class="stat-card">
							<div class="stat-value">42</div>
							<div class="stat-label">完成任务数</div>
						</div>
						<div class="stat-card">
							<div class="stat-value">80%</div>
							<div class="stat-label">目标完成率</div>
						</div>
						<div class="stat-card">
							<div class="stat-value">12</div>
							<div class="stat-label">进行中项目</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- 底部标签栏容器 -->
		<div id="tab-bar-container"></div>

		<script>
			window.onload = function () {
				loadStatusBar("status-bar-container");
				loadTabBar("tab-bar-container", "analytics");

				// 初始化日期显示
				updateDateDisplay();

				// 切换标签功能
				const segmentItems = document.querySelectorAll(".segment-item");
				const sections = ["day-section", "week-section", "month-section"];

				segmentItems.forEach((item, index) => {
					item.addEventListener("click", () => {
						// 更新选项卡样式
						segmentItems.forEach((i) => i.classList.remove("active"));
						item.classList.add("active");

						// 显示对应内容，隐藏其他内容
						sections.forEach((sectionId, sIdx) => {
							const section = document.getElementById(sectionId);
							if (section) {
								section.style.display = index === sIdx ? "block" : "none";
							}
						});

						// 如果使用了主题，则重新应用主题
						if (window.currentTheme) {
							applyThemeToElements(window.currentTheme);
						}
					});
				});

				// 接收主题变更消息并应用额外的样式
				window.addEventListener("message", function (event) {
					if (event.data && event.data.type === "THEME_CHANGE") {
						const theme = event.data.theme;
						window.currentTheme = theme; // 保存当前主题以便切换标签时重新应用

						// 应用主题到特定元素
						applyThemeToElements(theme);
					}
				});
			};

			// 对特定元素应用主题
			function applyThemeToElements(theme) {
				// 页面标题应用渐变
				const pageTitle = document.querySelector(".page-title");
				if (pageTitle) {
					pageTitle.style.background = `linear-gradient(120deg, ${theme.primary}, ${theme.secondary})`;
					pageTitle.style.webkitBackgroundClip = "text";
					pageTitle.style.webkitTextFillColor = "transparent";
				}

				// 分段控制器活动项
				const activeSegment = document.querySelector(".segment-item.active");
				if (activeSegment) {
					activeSegment.style.background = `linear-gradient(120deg, ${theme.primary}, ${theme.secondary})`;
				}

				// 进度圆环
				const progressCircles = document.querySelectorAll(".progress-circle");
				progressCircles.forEach((circle) => {
					circle.style.background = `conic-gradient(${theme.primary} 0% 65%, var(--color-gray-200) 65% 100%)`;
				});

				// 进度百分比
				const progressPercentages = document.querySelectorAll(
					".progress-percentage"
				);
				progressPercentages.forEach((percent) => {
					percent.style.color = theme.primary;
				});

				// 图表填充
				const chartFills = document.querySelectorAll(
					".chart-fill:not(.success):not(.warning):not(.danger)"
				);
				chartFills.forEach((fill) => {
					fill.style.background = `linear-gradient(to right, ${theme.secondary}, ${theme.primary})`;
				});

				// 天数图表
				const dayFills = document.querySelectorAll(".chart-day-fill");
				dayFills.forEach((fill) => {
					fill.style.background = `linear-gradient(to top, ${
						theme.primary
					}, ${adjustColor(theme.primary, 30)})`;
				});

				// 统计卡片数值
				const statValues = document.querySelectorAll(".stat-value");
				statValues.forEach((value) => {
					value.style.color = theme.primary;
				});
			}

			// 辅助函数：调整颜色亮度
			function adjustColor(hex, percent) {
				// 将十六进制颜色转换为 RGB
				let r = parseInt(hex.substring(1, 3), 16);
				let g = parseInt(hex.substring(3, 5), 16);
				let b = parseInt(hex.substring(5, 7), 16);

				// 调整亮度
				r = Math.max(0, Math.min(255, r + percent));
				g = Math.max(0, Math.min(255, g + percent));
				b = Math.max(0, Math.min(255, b + percent));

				// 转回十六进制
				return (
					"#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)
				);
			}

			// 日期处理函数
			let currentDate = new Date();

			// 更新所有日期显示
			function updateDateDisplay() {
				// 更新日显示
				document.getElementById("current-day").textContent = formatDate(
					currentDate,
					"day"
				);

				// 更新周显示
				document.getElementById("current-week").textContent = formatDate(
					currentDate,
					"week"
				);

				// 更新月显示
				document.getElementById("current-month").textContent = formatDate(
					currentDate,
					"month"
				);
			}

			// 日期切换函数
			function changeDate(type, offset) {
				if (type === "day") {
					// 天的切换
					currentDate.setDate(currentDate.getDate() + offset);
				} else if (type === "week") {
					// 周的切换
					currentDate.setDate(currentDate.getDate() + offset * 7);
				} else if (type === "month") {
					// 月的切换
					currentDate.setMonth(currentDate.getMonth() + offset);
				}

				// 更新日期显示
				updateDateDisplay();

				// 在此处可以添加数据刷新逻辑
				// 模拟刷新数据
				simulateDataRefresh(type);
			}

			// 格式化日期
			function formatDate(date, type) {
				const year = date.getFullYear();
				const month = date.getMonth() + 1;
				const day = date.getDate();

				if (type === "day") {
					// 天格式：2023年10月15日
					return `${year}年${month}月${day}日`;
				} else if (type === "week") {
					// 获取周一和周日
					const currentDay = date.getDay(); // 0是周日，1-6是周一到周六
					const mondayOffset = currentDay === 0 ? -6 : 1 - currentDay;
					const sundayOffset = currentDay === 0 ? 0 : 7 - currentDay;

					const monday = new Date(date);
					monday.setDate(date.getDate() + mondayOffset);

					const sunday = new Date(date);
					sunday.setDate(date.getDate() + sundayOffset);

					// 获取当前是一年中的第几周
					const firstDayOfYear = new Date(year, 0, 1);
					const pastDaysOfYear = (date - firstDayOfYear) / 86400000;
					const weekNumber = Math.ceil(
						(pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7
					);

					// 周格式：2023年第42周 (10月9日-10月15日)
					return `${year}年第${weekNumber}周 (${
						monday.getMonth() + 1
					}月${monday.getDate()}日-${
						sunday.getMonth() + 1
					}月${sunday.getDate()}日)`;
				} else if (type === "month") {
					// 月格式：2023年10月
					return `${year}年${month}月`;
				}
			}

			// 模拟数据刷新
			function simulateDataRefresh(type) {
				// 简单实现，可以根据实际需要扩展
				if (type === "day") {
					// 更新日卡片标题
					const dayTitle = document.querySelector(
						"#day-section .section-title"
					);
					if (dayTitle) {
						dayTitle.innerHTML = `<i class="fas fa-chart-line"></i> ${formatDate(
							currentDate,
							"day"
						)}生产力`;
					}
				} else if (type === "week") {
					// 更新周卡片标题
					const weekTitle = document.querySelector(
						"#week-section .section-title"
					);
					if (weekTitle) {
						weekTitle.innerHTML = `<i class="fas fa-chart-line"></i> 本周生产力`;
					}
				} else if (type === "month") {
					// 更新月卡片标题
					const monthTitle = document.querySelector(
						"#month-section .section-title"
					);
					if (monthTitle) {
						monthTitle.innerHTML = `<i class="fas fa-chart-line"></i> ${currentDate.getFullYear()}年${
							currentDate.getMonth() + 1
						}月效率分析`;
					}
				}

				// 在这里可以添加模拟进度数据变化
				const randomProgress = Math.floor(Math.random() * 30) + 60; // 60-90 之间的随机数
				const progressCircles = document.querySelectorAll(
					`#${type}-section .progress-percentage`
				);
				progressCircles.forEach((circle) => {
					const randomValue = Math.floor(Math.random() * 30) + 60;
					circle.textContent = `${randomValue}%`;
				});
			}
		</script>
	</body>
</html>
