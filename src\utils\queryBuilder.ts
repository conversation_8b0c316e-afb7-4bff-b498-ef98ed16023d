/**
 * 类型安全的数据库查询构建器
 * 确保查询条件中的状态值类型正确
 */

import type { OkrStatus, TaskStatus } from '@/constants/status'
import type { QueryCondition, QueryBuilder } from '@/types/database'
import { validateQueryCondition, isValidOkrStatus, isValidTaskStatus } from '@/types/database'

/**
 * 创建类型安全的查询构建器
 */
export const createQueryBuilder = (): QueryBuilder => {
  return {
    okrStatus: {
      equals: (status: OkrStatus): QueryCondition => {
        if (!isValidOkrStatus(status)) {
          throw new Error(`Invalid OKR status: ${status}`)
        }
        const condition = `status === "${status}"`
        if (!validateQueryCondition(condition)) {
          throw new Error(`Invalid query condition: ${condition}`)
        }
        return condition
      },

      notEquals: (status: OkrStatus): QueryCondition => {
        if (!isValidOkrStatus(status)) {
          throw new Error(`Invalid OKR status: ${status}`)
        }
        const condition = `status !== "${status}"`
        if (!validateQueryCondition(condition)) {
          throw new Error(`Invalid query condition: ${condition}`)
        }
        return condition
      },

      in: (statuses: OkrStatus[]): QueryCondition => {
        if (!Array.isArray(statuses) || statuses.length === 0) {
          throw new Error('Statuses array cannot be empty')
        }
        
        for (const status of statuses) {
          if (!isValidOkrStatus(status)) {
            throw new Error(`Invalid OKR status: ${status}`)
          }
        }
        
        const statusList = statuses.map(s => `"${s}"`).join(', ')
        const condition = `status, anyOf(${statusList})`
        if (!validateQueryCondition(condition)) {
          throw new Error(`Invalid query condition: ${condition}`)
        }
        return condition
      },

      notIn: (statuses: OkrStatus[]): QueryCondition => {
        if (!Array.isArray(statuses) || statuses.length === 0) {
          throw new Error('Statuses array cannot be empty')
        }
        
        for (const status of statuses) {
          if (!isValidOkrStatus(status)) {
            throw new Error(`Invalid OKR status: ${status}`)
          }
        }
        
        const conditions = statuses.map(s => `status !== "${s}"`).join(' && ')
        if (!validateQueryCondition(conditions)) {
          throw new Error(`Invalid query condition: ${conditions}`)
        }
        return conditions
      }
    },

    taskStatus: {
      equals: (status: TaskStatus): QueryCondition => {
        if (!isValidTaskStatus(status)) {
          throw new Error(`Invalid task status: ${status}`)
        }
        const condition = `status === ${status}`
        if (!validateQueryCondition(condition)) {
          throw new Error(`Invalid query condition: ${condition}`)
        }
        return condition
      },

      notEquals: (status: TaskStatus): QueryCondition => {
        if (!isValidTaskStatus(status)) {
          throw new Error(`Invalid task status: ${status}`)
        }
        const condition = `status !== ${status}`
        if (!validateQueryCondition(condition)) {
          throw new Error(`Invalid query condition: ${condition}`)
        }
        return condition
      },

      in: (statuses: TaskStatus[]): QueryCondition => {
        if (!Array.isArray(statuses) || statuses.length === 0) {
          throw new Error('Statuses array cannot be empty')
        }
        
        for (const status of statuses) {
          if (!isValidTaskStatus(status)) {
            throw new Error(`Invalid task status: ${status}`)
          }
        }
        
        const statusList = statuses.join(', ')
        const condition = `status, anyOf(${statusList})`
        if (!validateQueryCondition(condition)) {
          throw new Error(`Invalid query condition: ${condition}`)
        }
        return condition
      },

      notIn: (statuses: TaskStatus[]): QueryCondition => {
        if (!Array.isArray(statuses) || statuses.length === 0) {
          throw new Error('Statuses array cannot be empty')
        }
        
        for (const status of statuses) {
          if (!isValidTaskStatus(status)) {
            throw new Error(`Invalid task status: ${status}`)
          }
        }
        
        const conditions = statuses.map(s => `status !== ${s}`).join(' && ')
        if (!validateQueryCondition(conditions)) {
          throw new Error(`Invalid query condition: ${conditions}`)
        }
        return conditions
      }
    }
  }
}

// 导出单例查询构建器
export const queryBuilder = createQueryBuilder()

// 便捷的查询条件构建函数
export const buildOkrQuery = {
  // 查询进行中的目标
  inProgress: () => queryBuilder.okrStatus.equals('inProgress'),
  
  // 查询非暂停状态的目标
  notPaused: () => queryBuilder.okrStatus.notEquals('paused'),
  
  // 查询活跃目标（进行中 + 待开始）
  active: () => queryBuilder.okrStatus.in(['inProgress', 'pending']),
  
  // 查询已结束目标（已完成 + 已放弃）
  finished: () => queryBuilder.okrStatus.in(['completed', 'abandoned']),
  
  // 查询未完成目标（待开始 + 进行中 + 暂停）
  unfinished: () => queryBuilder.okrStatus.in(['pending', 'inProgress', 'paused'])
}

export const buildTaskQuery = {
  // 查询未完成任务
  incomplete: () => queryBuilder.taskStatus.equals(0),
  
  // 查询已完成任务
  completed: () => queryBuilder.taskStatus.equals(1),
  
  // 查询非已放弃任务
  notAbandoned: () => queryBuilder.taskStatus.notEquals(2),
  
  // 查询活跃任务（未完成 + 已完成）
  active: () => queryBuilder.taskStatus.in([0, 1])
}

// 复合查询构建函数
export const buildComplexQuery = {
  // 查询进行中目标的未完成任务
  activeOkrIncompleteTasks: () => {
    return `okrId != "" && ${buildOkrQuery.inProgress()} && ${buildTaskQuery.incomplete()}`
  },
  
  // 查询指定日期范围内的活跃任务
  tasksInDateRange: (startDate: string, endDate: string) => {
    if (!startDate || !endDate) {
      throw new Error('Start date and end date are required')
    }
    return `startDate <= "${endDate}" && endDate >= "${startDate}" && ${buildTaskQuery.active()}`
  }
}
