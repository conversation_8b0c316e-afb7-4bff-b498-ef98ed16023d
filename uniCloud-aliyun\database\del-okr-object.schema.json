// 文档教程：https://uniapp.dcloud.net.cn/uniCloud/schema
{
  "bsonType": "object",
  "required": ["name", "date", "create_date", "uid"],
  "permission": {
    "read": "doc.uid == auth.uid",
    "create": true,
    "update": "doc.uid == auth.uid",
    "delete": "doc.uid == auth.uid"
  },
  "properties": {
    "_id": {
      "description": "ID，系统自动生成"
    },
    "name": {
      "bsonType": "string",
      "title": "目标名称",
      "trim": "both"
    },
    "date": {
      "bsonType": "array",
      "arrayType": "string",
      "title": "目标执行时间"
    },
    "motivation": {
      "bsonType": "array",
      "arrayType": "object",
      "title": "动机",
      "required": ["title"],
      "properties": {
        "title": {
          "bsonType": "string",
          "title": "动机标题",
          "trim": "both"
        },
        "content": {
          "bsonType": "string",
          "title": "动机解释",
          "trim": "both"
        }
      }
    },
    "feasibility": {
      "bsonType": "array",
      "arrayType": "object",
      "title": "可行性",
      "required": ["title"],
      "properties": {
        "title": {
          "bsonType": "string",
          "title": "可行性标题",
          "trim": "both"
        },
        "content": {
          "bsonType": "string",
          "title": "可行性解释",
          "trim": "both"
        }
      }
    },
    "update_date": {
      "bsonType": "timestamp",
      "title": "更新时间",
      "defaultValue": {
        "$env": "now"
      }
    },
    "create_date": {
      "bsonType": "timestamp",
      "title": "创建时间",
      "forceDefaultValue": {
        "$env": "now"
      }
    },
    "uid": {
      "bsonType": "string",
      "title": "用户 id",
      "forceDefaultValue": {
        "$env": "uid"
      }
    }
  }
}
