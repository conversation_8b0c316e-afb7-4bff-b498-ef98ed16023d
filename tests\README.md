# OKR 功能单元测试

本目录包含 OKR 功能的单元测试代码。

## 测试框架

项目使用以下测试工具：

- Jest: JavaScript 测试框架
- ts-jest: TypeScript 支持
- babel-jest: Babel 转换支持

## 安装测试依赖

在运行测试前，需要先安装测试依赖：

```bash
npm install
```

## 配置 package.json

项目已在 package.json 中添加测试脚本：

```json
"scripts": {
  "test": "jest --config tests/jest.config.js",
  "test:watch": "jest --config tests/jest.config.js --watch",
  "test:coverage": "jest --config tests/jest.config.js --coverage"
}
```

## 运行测试

运行所有测试：

```bash
npm run test
```

运行特定的测试文件：

```bash
npm run test tests/unit/example.test.js
```

监视模式（修改代码后自动运行测试）：

```bash
npm run test:watch
```

生成测试覆盖率报告：

```bash
npm run test:coverage
```

## 测试文件结构

- `tests/unit/example.test.js` - 基本 Jest 测试示例
- `tests/unit/okrSimple.test.js` - 简化的 OKR API 测试示例
- `tests/unit/okr.test.js` - OKR API 测试

## 注意事项

1. 在运行测试前确保安装了所有依赖
2. 测试环境使用 JSDOM 模拟浏览器环境
3. 使用模拟(mock)技术隔离测试单元，确保测试的独立性和可靠性
