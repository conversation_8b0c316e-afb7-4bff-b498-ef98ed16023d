<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>目标 - OKR助手</title>
		<link
			href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css"
			rel="stylesheet"
		/>
		<link
			rel="stylesheet"
			href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
		/>
		<style>
			@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap");

			/* 集成共享组件 CSS 变量 */
			:root {
				--color-primary: #5e6ad2;
				--color-primary-light: #7c84e8;
				--color-primary-dark: #4549a9;
				--color-secondary: #4ea8de;
				--color-secondary-light: #70c1e8;
				--color-secondary-dark: #2889c9;
				--color-accent: #ff7c7c;
				--color-accent-light: #ff9e9e;
				--color-accent-dark: #ff5252;
				--color-success: #56c993;
				--color-warning: #ffae57;
				--color-danger: #ff6b6b;
				--color-bg: #f8f9ff;
				--color-white: #ffffff;
				--color-gray-100: #f5f7ff;
				--color-gray-200: #ebeefe;
				--color-gray-300: #d8dcef;
				--color-gray-400: #b0b5d1;
				--color-gray-500: #868aaf;
				--color-gray-600: #585d80;
				--color-gray-700: #424366;
				--color-gray-800: #2b2c44;
				--color-gray-900: #1a1b2e;
				--rounded-lg: 16px;
				--rounded-md: 12px;
				--rounded-sm: 8px;
				--shadow-sm: 0 2px 8px rgba(94, 106, 210, 0.08);
				--shadow-md: 0 4px 16px rgba(94, 106, 210, 0.12);
				--shadow-lg: 0 8px 24px rgba(94, 106, 210, 0.16);
				--color-primary-rgb: 94, 106, 210;
			}

			body {
				background: var(--color-bg);
				font-family: "Poppins", -apple-system, BlinkMacSystemFont, "Segoe UI",
					Roboto, sans-serif;
				color: var(--color-gray-800);
				height: 100vh;
				display: flex;
				flex-direction: column;
				overflow-x: hidden;
				/* 添加滚动条样式 */
				scrollbar-width: thin; /* Firefox */
				scrollbar-color: #a0a0a0 #f1f1f1; /* Firefox */
			}

			/* 状态栏样式 */
			.ios-status-bar {
				height: 44px;
				width: 100%;
				background-color: var(--color-white);
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 0 20px;
				position: fixed;
				top: 0;
				left: 0;
				right: 0;
				z-index: 1000;
				border-bottom: none;
				box-shadow: var(--shadow-sm);
			}

			/* 底部导航栏样式 - 现代感风格 */
			.ios-tab-bar {
				height: 80px;
				background: var(--color-white);
				display: flex;
				justify-content: space-evenly;
				align-items: center;
				padding-bottom: 20px;
				border-top: none;
				position: fixed;
				bottom: 0;
				left: 0;
				right: 0;
				z-index: 1000;
				box-shadow: 0 -2px 10px rgba(94, 106, 210, 0.08);
				border-radius: 24px 24px 0 0;
			}

			.tab-item {
				display: flex;
				flex-direction: column;
				align-items: center;
				color: var(--color-gray-500);
				font-size: 12px;
				font-weight: 500;
				transition: all 0.3s ease;
				position: relative;
				padding: 10px 12px;
				border-radius: var(--rounded-md);
				width: 22%;
				text-decoration: none;
				cursor: pointer;
			}

			.tab-item.active {
				color: var(--color-primary);
				background: var(--color-gray-100);
				transform: translateY(-4px);
			}

			.tab-item.active::after {
				content: "";
				position: absolute;
				bottom: -5px;
				width: 30px;
				height: 4px;
				background: var(--color-primary);
				border-radius: 10px;
				opacity: 0.8;
			}

			.tab-item i {
				font-size: 22px;
				margin-bottom: 6px;
				transition: all 0.3s ease;
			}

			.tab-item.active i {
				color: var(--color-primary);
				transform: scale(1.15);
			}

			/* Webkit browsers (Chrome, Safari, Edge) */
			::-webkit-scrollbar {
				width: 5px; /* 更窄的滚动条 */
			}

			::-webkit-scrollbar-track {
				background: rgba(241, 241, 241, 0.5); /* 轨道背景色 */
				border-radius: 6px;
			}

			::-webkit-scrollbar-thumb {
				background: rgba(160, 160, 160, 0.5); /* 滑块颜色 */
				border-radius: 6px; /* 滑块圆角 */
			}

			::-webkit-scrollbar-thumb:hover {
				background: #888; /* 滑块鼠标悬停颜色 */
			}

			.content-area {
				flex: 1;
				overflow-y: auto;
				padding: 16px 20px 90px;
				max-width: 800px;
				margin: 0 auto;
				width: 100%;
				margin-top: 44px; /* 为状态栏腾出空间 */
			}

			.app-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 24px;
				padding: 10px 0;
			}

			.app-header h1 {
				font-size: 28px;
				font-weight: 700;
				color: var(--color-primary);
				letter-spacing: -0.5px;
				background: linear-gradient(
					120deg,
					var(--color-primary),
					var(--color-primary-dark)
				);
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
			}

			.header-container {
				margin-top: 15px;
				margin-bottom: 30px;
			}

			.header-card {
				padding: 28px;
				position: relative;
				overflow: hidden;
				border-radius: var(--rounded-lg);
				background: linear-gradient(
					135deg,
					var(--color-primary-light),
					var(--color-primary)
				);
				border: none;
				box-shadow: var(--shadow-md);
				color: white;
			}

			.header-card h2 {
				font-size: 22px;
				font-weight: 600;
				margin-bottom: 8px;
			}

			.header-card p {
				opacity: 0.9;
				font-size: 14px;
				margin-bottom: 16px;
			}

			.header-card::before {
				content: "";
				position: absolute;
				top: -50px;
				right: -50px;
				width: 150px;
				height: 150px;
				border-radius: 50%;
				background: rgba(255, 255, 255, 0.1);
			}

			.header-card::after {
				content: "";
				position: absolute;
				bottom: -30px;
				left: -30px;
				width: 100px;
				height: 100px;
				border-radius: 50%;
				background: rgba(255, 255, 255, 0.08);
			}

			.stats-row {
				display: grid;
				grid-template-columns: repeat(2, 1fr);
				gap: 16px;
				margin-top: 25px;
			}

			.stat-card {
				padding: 20px;
				border-radius: var(--rounded-md);
				background: var(--color-white);
				border: none;
				box-shadow: var(--shadow-sm);
				display: flex;
				flex-direction: column;
				transition: all 0.2s ease;
			}

			.stat-card:hover {
				transform: translateY(-3px);
				box-shadow: var(--shadow-md);
			}

			.stat-card .stat-value {
				font-size: 28px;
				font-weight: 700;
				margin-bottom: 5px;
				background: linear-gradient(
					120deg,
					var(--color-primary),
					var(--color-primary-dark)
				);
				-webkit-background-clip: text;
				-webkit-text-fill-color: transparent;
			}

			.stat-card .stat-label {
				font-size: 14px;
				color: var(--color-gray-600);
				font-weight: 500;
			}

			.section-header {
				margin: 32px 0 16px;
				font-size: 20px;
				font-weight: 600;
				color: var(--color-gray-800);
				display: flex;
				align-items: center;
				gap: 8px;
			}

			.objective-card {
				position: relative;
				transition: all 0.3s ease;
				margin-bottom: 20px;
				overflow: visible;
				background: var(--color-white);
				border: none;
				box-shadow: var(--shadow-sm);
				border-radius: var(--rounded-lg);
			}

			/* 完全移除颜色条 */
			.objective-card::before {
				display: none;
			}

			.objective-card:hover {
				transform: translateY(-3px);
				box-shadow: var(--shadow-md);
			}

			.objective-content {
				padding: 22px;
				transition: padding 0.3s ease;
			}

			.objective-content.kr-collapsed {
				padding-bottom: 10px;
			}

			.objective-title {
				font-size: 18px;
				font-weight: 600;
				margin-bottom: 10px;
				color: var(--color-gray-800);
			}

			.objective-meta {
				display: flex;
				flex-wrap: wrap;
				gap: 12px;
				margin-bottom: 14px;
			}

			.meta-item {
				display: flex;
				align-items: center;
				font-size: 13px;
				color: var(--color-gray-600);
				background: var(--color-gray-100);
				padding: 5px 10px;
				border-radius: 20px;
			}

			.meta-item i {
				margin-right: 6px;
				font-size: 14px;
				color: var(--color-primary);
			}

			.kr-list {
				display: flex;
				flex-direction: column;
				gap: 10px;
				margin-top: 14px;
				max-height: 1000px;
				transition: all 0.3s ease;
				overflow: hidden;
				opacity: 1;
			}

			.kr-list.hidden {
				max-height: 0;
				opacity: 0;
				margin-top: 0;
			}

			.kr-item {
				display: flex;
				align-items: center;
				gap: 10px;
				color: var(--color-gray-700);
				font-size: 14px;
				padding: 8px 12px;
				background: var(--color-gray-100);
				border-radius: var(--rounded-md);
				transition: all 0.2s ease;
			}

			.kr-item:hover {
				background: var(--color-gray-200);
			}

			.kr-item i {
				color: var(--color-primary);
				font-size: 14px;
			}

			.toggle-kr {
				cursor: pointer;
				color: var(--color-gray-600);
				font-size: 13px;
				display: inline-flex;
				align-items: center;
				margin-left: 8px;
				transition: all 0.2s ease;
				vertical-align: middle;
			}

			.toggle-kr i {
				transition: transform 0.3s ease;
				font-size: 12px;
			}

			.toggle-kr.collapsed i {
				transform: rotate(-90deg);
			}

			.toggle-kr:hover {
				color: var(--color-primary);
			}

			.progress-container {
				height: 8px;
				background: var(--color-gray-200);
				border-radius: 4px;
				overflow: hidden;
				margin: 14px 0 8px;
			}

			.progress-bar {
				height: 100%;
				background: linear-gradient(
					to right,
					var(--color-primary-light),
					var(--color-primary)
				);
				border-radius: 4px;
				transition: width 0.5s ease;
			}

			.progress-label {
				display: flex;
				justify-content: space-between;
				align-items: center;
				font-size: 12px;
				color: var(--color-gray-600);
			}

			.progress-info {
				display: flex;
				align-items: center;
			}

			.fab-button {
				position: fixed;
				bottom: 90px;
				right: 20px;
				width: 60px;
				height: 60px;
				border-radius: 50%;
				background: linear-gradient(
					135deg,
					var(--color-primary),
					var(--color-primary-dark)
				);
				color: white;
				display: flex;
				align-items: center;
				justify-content: center;
				box-shadow: 0 4px 15px rgba(94, 106, 210, 0.3);
				cursor: pointer;
				transition: all 0.3s ease;
				z-index: 100;
			}

			.fab-button:hover {
				transform: scale(1.05) rotate(5deg);
				box-shadow: 0 6px 20px rgba(94, 106, 210, 0.4);
			}

			.fab-button i {
				font-size: 24px;
			}

			.welcome-message {
				font-size: 15px;
				color: var(--color-gray-600);
				margin-bottom: 20px;
			}

			/* 筛选标签样式 */
			.filter-tabs {
				display: flex;
				gap: 7px;
				flex-wrap: wrap;
				margin-bottom: 18px;
			}

			.filter-tab {
				padding: 7px 12px;
				border-radius: 16px;
				font-size: 13px;
				font-weight: 500;
				background: var(--color-gray-100);
				color: var(--color-gray-600);
				transition: all 0.3s ease;
				cursor: pointer;
				border: none;
				box-shadow: var(--shadow-sm);
				opacity: 0.85;
				transform: scale(0.95);
				text-align: center;
			}

			.filter-tab span {
				transition: all 0.3s ease;
				white-space: nowrap;
			}

			.filter-tab:hover {
				background: var(--color-gray-200);
				transform: scale(0.98);
				opacity: 0.95;
			}

			.filter-tab.active {
				padding: 8px 16px;
				font-size: 14px;
				background: linear-gradient(
					135deg,
					var(--color-primary-light),
					var(--color-primary)
				);
				color: white;
				box-shadow: 0 4px 12px rgba(var(--color-primary-rgb), 0.3);
				opacity: 1;
				transform: scale(1);
				z-index: 1;
				font-weight: 600;
			}

			/* 下拉菜单筛选样式 */
			.filter-dropdown {
				width: 100%;
				max-width: 200px;
			}

			.status-select {
				width: 100%;
				padding: 10px 12px;
				border: 1px solid var(--color-gray-300);
				border-radius: var(--rounded-md);
				background-color: white;
				font-size: 14px;
				color: var(--color-gray-700);
				-webkit-appearance: none;
				-moz-appearance: none;
				appearance: none;
				background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
				background-repeat: no-repeat;
				background-position: right 10px center;
				background-size: 16px;
				transition: all 0.2s ease;
			}

			.status-select:focus {
				outline: none;
				border-color: var(--color-primary);
				box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.2);
			}
		</style>
	</head>
	<body>
		<!-- 集成状态栏 -->
		<div id="status-bar-container" class="ios-status-bar">
			<div class="time font-semibold">9:41</div>
			<div class="status-icons flex items-center space-x-2">
				<i class="fas fa-signal"></i>
				<i class="fas fa-wifi"></i>
				<i class="fas fa-battery-three-quarters"></i>
			</div>
		</div>

		<div class="content-area">
			<div class="app-header">
				<h1>我的目标</h1>
				<div>
					<i
						class="fas fa-plus text-xl text-gray-500 px-3 py-2 rounded-full bg-gray-100 hover:bg-gray-200 transition cursor-pointer"
					></i>
				</div>
			</div>

			<!-- 添加标签式筛选器 -->
			<div class="filter-tabs mb-4">
				<button class="filter-tab active" data-filter="all">
					<span>全部</span>
				</button>
				<button class="filter-tab" data-filter="in-progress">
					<span>进行中</span>
				</button>
				<button class="filter-tab" data-filter="pending">
					<span>待开始</span>
				</button>
				<button class="filter-tab" data-filter="completed">
					<span>已完成</span>
				</button>
			</div>

			<div class="section-header" data-type="in-progress">
				<i class="fas fa-spinner text-primary"></i>
				<span>进行中</span>
			</div>

			<div class="objective-card" data-status="in-progress">
				<div class="objective-content">
					<div class="objective-title">提升产品用户体验和交互设计</div>
					<div class="objective-meta">
						<div class="meta-item">
							<i class="fas fa-calendar-alt"></i>
							<span>截止：2023/06/30</span>
						</div>
					</div>
					<div class="progress-container">
						<div class="progress-bar" style="width: 75%"></div>
					</div>
					<div class="progress-label">
						<div class="progress-info">
							<span>完成度 75%</span>
							<div class="toggle-kr" title="点击展开/收起关键结果">
								<i class="fas fa-chevron-down ml-1"></i>
							</div>
						</div>
						<span>3/4 关键结果</span>
					</div>
					<div class="kr-list">
						<div class="kr-item">
							<i class="fas fa-check-circle"></i>
							<span>完成新版本 App 的用户界面设计</span>
						</div>
						<div class="kr-item">
							<i class="fas fa-check-circle"></i>
							<span>用户满意度调查分数达到 4.5 分（满分 5 分）</span>
						</div>
						<div class="kr-item">
							<i class="fas fa-check-circle"></i>
							<span>用户界面响应速度提升 30%</span>
						</div>
					</div>
				</div>
			</div>

			<div class="objective-card" data-status="in-progress">
				<div class="objective-content">
					<div class="objective-title">扩大市场份额和用户增长</div>
					<div class="objective-meta">
						<div class="meta-item">
							<i class="fas fa-calendar-alt"></i>
							<span>截止：2023/06/30</span>
						</div>
					</div>
					<div class="progress-container">
						<div class="progress-bar" style="width: 40%"></div>
					</div>
					<div class="progress-label">
						<div class="progress-info">
							<span>完成度 40%</span>
							<div class="toggle-kr" title="点击展开/收起关键结果">
								<i class="fas fa-chevron-down ml-1"></i>
							</div>
						</div>
						<span>2/5 关键结果</span>
					</div>
					<div class="kr-list">
						<div class="kr-item">
							<i class="fas fa-check-circle"></i>
							<span>月活跃用户增长 30%，达到 20 万</span>
						</div>
						<div class="kr-item">
							<i class="fas fa-check-circle"></i>
							<span>APP 商店评分提升至 4.7 分</span>
						</div>
					</div>
				</div>
			</div>

			<div class="section-header" data-type="pending">
				<i class="fas fa-pause-circle text-primary"></i>
				<span>待开始</span>
			</div>

			<div class="objective-card" data-status="pending">
				<div class="objective-content">
					<div class="objective-title">优化产品研发流程</div>
					<div class="objective-meta">
						<div class="meta-item">
							<i class="fas fa-calendar-alt"></i>
							<span>截止：2023/07/15</span>
						</div>
					</div>
					<div class="progress-container">
						<div class="progress-bar" style="width: 30%"></div>
					</div>
					<div class="progress-label">
						<div class="progress-info">
							<span>完成度 30%</span>
							<div class="toggle-kr" title="点击展开/收起关键结果">
								<i class="fas fa-chevron-down ml-1"></i>
							</div>
						</div>
						<span>1/3 关键结果</span>
					</div>
					<div class="kr-list">
						<div class="kr-item">
							<i class="fas fa-check-circle"></i>
							<span>优化自动化测试流程</span>
						</div>
					</div>
				</div>
			</div>

			<div class="section-header" data-type="completed">
				<i class="fas fa-check-circle text-primary"></i>
				<span>已完成</span>
			</div>

			<div class="objective-card" data-status="completed">
				<div class="objective-content">
					<div class="objective-title">提高团队工作效率</div>
					<div class="objective-meta">
						<div class="meta-item">
							<i class="fas fa-calendar-alt"></i>
							<span>截止：2023/05/20</span>
						</div>
						<div class="meta-item">
							<i class="fas fa-check"></i>
							<span>已完成</span>
						</div>
					</div>
					<div class="progress-container">
						<div class="progress-bar" style="width: 100%"></div>
					</div>
					<div class="progress-label">
						<div class="progress-info">
							<span>完成度 100%</span>
							<div class="toggle-kr" title="点击展开/收起关键结果">
								<i class="fas fa-chevron-down ml-1"></i>
							</div>
						</div>
						<span>3/3 关键结果</span>
					</div>
					<div class="kr-list">
						<div class="kr-item">
							<i class="fas fa-check-circle"></i>
							<span>实施敏捷开发方法</span>
						</div>
						<div class="kr-item">
							<i class="fas fa-check-circle"></i>
							<span>建立高效会议制度</span>
						</div>
						<div class="kr-item">
							<i class="fas fa-check-circle"></i>
							<span>每周团队反馈评分达到 4.8</span>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- 集成底部标签栏 -->
		<div id="tab-bar-container" class="ios-tab-bar">
			<div class="tab-item active">
				<i class="fas fa-bullseye"></i>
				<span>目标</span>
			</div>
			<div class="tab-item">
				<i class="fas fa-tasks"></i>
				<span>任务</span>
			</div>
			<div class="tab-item">
				<i class="fas fa-clock"></i>
				<span>时间</span>
			</div>
			<div class="tab-item">
				<i class="fas fa-chart-pie"></i>
				<span>分析</span>
			</div>
		</div>

		<script>
			window.onload = function () {
				// 设置收起功能
				setupToggleButtons();

				// 设置筛选标签功能
				setupFilterTabs();

				// 设置下拉菜单筛选功能
				setupFilterDropdown();

				// 应用默认主题
				applyTheme({
					primary: "#5E6AD2",
					secondary: "#4EA8DE",
					accent: "#FF7C7C",
				});

				// 将文本图标样式应用
				document.querySelectorAll(".text-primary").forEach((icon) => {
					icon.style.color = getComputedStyle(
						document.documentElement
					).getPropertyValue("--color-primary");
				});
			};

			// 设置收起功能
			function setupToggleButtons() {
				// 获取所有的切换按钮
				var toggleButtons = document.querySelectorAll(".toggle-kr");

				// 默认收起所有关键结果
				document.querySelectorAll(".kr-list").forEach(function (list) {
					list.classList.add("hidden");
				});

				// 设置所有按钮为收起状态
				toggleButtons.forEach(function (button) {
					button.classList.add("collapsed");
				});

				// 添加点击事件
				toggleButtons.forEach(function (button) {
					button.addEventListener("click", function (e) {
						e.stopPropagation();

						// 找到关联的关键结果列表
						var list =
							this.closest(".objective-content").querySelector(".kr-list");

						// 切换显示状态
						if (list.classList.contains("hidden")) {
							// 展开
							list.classList.remove("hidden");
							this.classList.remove("collapsed");
						} else {
							// 收起
							list.classList.add("hidden");
							this.classList.add("collapsed");
						}
					});
				});
			}

			// 应用主题颜色到特定元素
			function applyTheme(theme) {
				// 页面标题
				const pageTitle = document.querySelector(".app-header h1");
				if (pageTitle) {
					pageTitle.style.background = `linear-gradient(120deg, ${theme.primary}, ${theme.secondary})`;
					pageTitle.style.webkitBackgroundClip = "text";
					pageTitle.style.webkitTextFillColor = "transparent";
				}

				// 顶部卡片渐变背景
				const headerCard = document.querySelector(".header-card");
				if (headerCard) {
					headerCard.style.background = `linear-gradient(135deg, ${
						theme.primary
					}, ${adjustColor(theme.secondary, 20)})`;
				}

				// 进度条
				const progressBars = document.querySelectorAll(".progress-bar");
				progressBars.forEach((bar) => {
					bar.style.background = `linear-gradient(to right, ${adjustColor(
						theme.primary,
						20
					)}, ${theme.primary})`;
				});

				// 悬浮按钮
				const fabButton = document.querySelector(".fab-button");
				if (fabButton) {
					fabButton.style.background = `linear-gradient(135deg, ${theme.primary}, ${theme.secondary})`;
				}

				// 文字图标
				const iconTexts = document.querySelectorAll(".text-primary");
				iconTexts.forEach((icon) => {
					icon.style.color = theme.primary;
				});

				// 统计卡片数值
				const statValues = document.querySelectorAll(".stat-value");
				statValues.forEach((value) => {
					value.style.color = "white";
					value.style.fontWeight = "bold";
				});

				// 已完成图标
				const checkIcons = document.querySelectorAll(".fa-check-circle");
				checkIcons.forEach((icon) => {
					icon.style.color = theme.primary;
				});

				// 活动标签页
				const activeTab = document.querySelector(".tab-item.active");
				if (activeTab) {
					activeTab.style.color = theme.primary;
				}

				// 活动筛选标签
				const activeFilterTab = document.querySelector(".filter-tab.active");
				if (activeFilterTab) {
					activeFilterTab.style.background = `linear-gradient(135deg, ${adjustColor(
						theme.primary,
						20
					)}, ${theme.primary})`;
				}
			}

			// 辅助函数：调整颜色亮度
			function adjustColor(hex, percent) {
				// 将十六进制颜色转换为 RGB
				let r = parseInt(hex.substring(1, 3), 16);
				let g = parseInt(hex.substring(3, 5), 16);
				let b = parseInt(hex.substring(5, 7), 16);

				// 调整亮度
				r = Math.max(0, Math.min(255, r + percent));
				g = Math.max(0, Math.min(255, g + percent));
				b = Math.max(0, Math.min(255, b + percent));

				// 转回十六进制
				return (
					"#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)
				);
			}

			// 添加筛选标签功能
			function setupFilterTabs() {
				const filterTabs = document.querySelectorAll(".filter-tab");
				const objectiveCards = document.querySelectorAll(".objective-card");
				const sectionHeaders = document.querySelectorAll(".section-header");

				// 为每个筛选标签添加点击事件
				filterTabs.forEach((tab) => {
					tab.addEventListener("click", () => {
						// 移除所有标签的 active 类
						filterTabs.forEach((t) => t.classList.remove("active"));
						// 给当前点击的标签添加 active 类
						tab.classList.add("active");

						filterContent(tab.getAttribute("data-filter"));
					});
				});
			}

			// 添加下拉菜单筛选功能
			function setupFilterDropdown() {
				const statusSelect = document.getElementById("status-filter");
				if (statusSelect) {
					statusSelect.addEventListener("change", () => {
						filterContent(statusSelect.value);
					});
				}
			}

			// 筛选内容函数，被两种筛选方式共用
			function filterContent(filter) {
				const objectiveCards = document.querySelectorAll(".objective-card");
				const sectionHeaders = document.querySelectorAll(".section-header");

				// 处理所有目标卡片的显示/隐藏
				let hasVisibleCards = {
					"in-progress": false,
					pending: false,
					completed: false,
				};

				objectiveCards.forEach((card) => {
					const cardStatus = card.getAttribute("data-status");
					const isVisible = filter === "all" || cardStatus === filter;
					card.style.display = isVisible ? "block" : "none";

					// 记录每种状态是否有可见卡片
					if (isVisible && cardStatus) {
						hasVisibleCards[cardStatus] = true;
					}
				});

				// 处理章节标题的显示/隐藏
				sectionHeaders.forEach((header) => {
					const headerType = header.getAttribute("data-type");
					if (filter === "all") {
						header.style.display = "flex";
					} else if (headerType === filter) {
						header.style.display = "flex";
					} else {
						header.style.display = "none";
					}
				});

				// 应用选中状态的样式到筛选标签
				document.querySelectorAll(".filter-tab").forEach((tab) => {
					if (tab.getAttribute("data-filter") === filter) {
						tab.classList.add("active");
					} else {
						tab.classList.remove("active");
					}
				});
			}
		</script>
	</body>
</html>
