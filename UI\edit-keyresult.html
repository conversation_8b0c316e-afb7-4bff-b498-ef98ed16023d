<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>编辑关键结果 - OKR助手</title>
		<link
			href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css"
			rel="stylesheet"
		/>
		<link
			rel="stylesheet"
			href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css"
		/>
		<!-- 引入共享组件 -->
		<script>
			// 动态加载 shared-components.html
			(function () {
				const xhr = new XMLHttpRequest();
				xhr.open("GET", "shared-components.html", true);
				xhr.onreadystatechange = function () {
					if (xhr.readyState === 4 && xhr.status === 200) {
						// 创建临时容器
						const tempDiv = document.createElement("div");
						tempDiv.innerHTML = xhr.responseText;

						// 提取 style 标签并添加到 head
						const styleTags = tempDiv.getElementsByTagName("style");
						for (let i = 0; i < styleTags.length; i++) {
							const styleContent = styleTags[i].textContent;
							const style = document.createElement("style");
							style.textContent = styleContent;
							document.head.appendChild(style);
						}

						// 提取script标签内容并执行
						const scriptTags = tempDiv.getElementsByTagName("script");
						if (scriptTags.length > 0) {
							const scriptContent = scriptTags[0].textContent;
							const script = document.createElement("script");
							script.textContent = scriptContent;
							document.head.appendChild(script);

							// 等待脚本加载后初始化标签栏
							setTimeout(function () {
								loadTabBar("tab-bar-container");
							}, 100);
						}

						// 添加模板到页面
						const templates = tempDiv.getElementsByTagName("template");
						for (let i = 0; i < templates.length; i++) {
							document.body.appendChild(templates[i].cloneNode(true));
						}
					}
				};
				xhr.send();
			})();
		</script>
		<style>
			@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap");

			body {
				font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
					Helvetica, Arial, sans-serif;
				background: var(--color-bg);
				height: 100vh;
				display: flex;
				flex-direction: column;
				color: var(--color-gray-800);
				overflow-x: hidden;
			}

			.content-area {
				flex: 1;
				overflow-y: auto;
				padding: 0 16px 90px;
				max-width: 800px;
				margin: 0 auto;
				width: 100%;
			}

			.navbar {
				display: flex;
				align-items: center;
				padding: 15px 20px;
				background: var(--color-white);
				border-bottom: 1px solid var(--color-gray-200);
				position: sticky;
				top: 44px;
				z-index: 40;
			}

			.navbar-title {
				font-weight: 600;
				font-size: 18px;
				margin-left: 15px;
				color: var(--color-primary);
			}

			.navbar-back {
				color: var(--color-primary);
				font-size: 16px;
				width: 36px;
				height: 36px;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 8px;
				background: var(--color-gray-100);
			}

			.navbar-action {
				margin-left: auto;
				color: var(--color-primary);
				font-weight: 500;
				font-size: 15px;
				padding: 8px 16px;
				border-radius: 6px;
				background: var(--color-primary-light);
			}

			.form-section {
				margin-bottom: 24px;
			}

			.section-label {
				font-size: 14px;
				color: var(--color-gray-600);
				margin-bottom: 12px;
				margin-left: 5px;
				font-weight: 500;
			}

			.input-card {
				background-color: var(--color-white);
				border-radius: 16px;
				padding: 16px;
				box-shadow: var(--shadow-sm);
				margin-bottom: 5px;
				border: 1px solid var(--color-gray-200);
			}

			.input-field {
				display: flex;
				flex-direction: column;
				margin-bottom: 16px;
			}

			.input-field:last-child {
				margin-bottom: 0;
			}

			.input-label {
				font-size: 14px;
				color: var(--color-gray-600);
				margin-bottom: 8px;
			}

			.input-text {
				width: 100%;
				border: 1px solid var(--color-gray-300);
				border-radius: 8px;
				padding: 12px;
				font-size: 16px;
				color: var(--color-gray-800);
				background: var(--color-white);
				transition: all 0.2s;
			}

			.input-text:focus {
				outline: none;
				border-color: var(--color-primary);
				box-shadow: 0 0 0 2px var(--color-primary-light);
			}

			.input-hint {
				font-size: 13px;
				color: var(--color-gray-500);
				margin-top: 6px;
			}

			.select-container {
				position: relative;
			}

			.select-input {
				width: 100%;
				border: 1px solid var(--color-gray-300);
				border-radius: 8px;
				padding: 12px;
				font-size: 16px;
				color: var(--color-gray-800);
				background: var(--color-white);
				-webkit-appearance: none;
				-moz-appearance: none;
				appearance: none;
				cursor: pointer;
			}

			.select-input:focus {
				outline: none;
				border-color: var(--color-primary);
				box-shadow: 0 0 0 2px var(--color-primary-light);
			}

			.select-arrow {
				position: absolute;
				right: 12px;
				top: 50%;
				transform: translateY(-50%);
				color: var(--color-gray-500);
				pointer-events: none;
			}

			.progress-section {
				margin-top: 20px;
			}

			.progress-label {
				display: flex;
				justify-content: space-between;
				font-size: 14px;
				color: var(--color-gray-600);
				margin-bottom: 8px;
			}

			.progress-value {
				font-weight: 500;
				color: var(--color-primary);
			}

			.range-slider {
				-webkit-appearance: none;
				width: 100%;
				height: 8px;
				border-radius: 4px;
				background: var(--color-gray-200);
				outline: none;
				margin: 10px 0;
			}

			.range-slider::-webkit-slider-thumb {
				-webkit-appearance: none;
				appearance: none;
				width: 24px;
				height: 24px;
				border-radius: 50%;
				background: var(--color-primary);
				cursor: pointer;
				border: 2px solid var(--color-white);
				box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
			}

			.range-slider::-moz-range-thumb {
				width: 24px;
				height: 24px;
				border-radius: 50%;
				background: var(--color-primary);
				cursor: pointer;
				border: 2px solid var(--color-white);
				box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
			}

			.metric-type-options {
				display: flex;
				flex-wrap: wrap;
				gap: 10px;
				margin: 10px 0;
			}

			.metric-option {
				flex: 1;
				min-width: calc(33% - 10px);
				text-align: center;
				padding: 12px;
				border: 1px solid var(--color-gray-300);
				border-radius: 8px;
				cursor: pointer;
				transition: all 0.2s;
			}

			.metric-option.active {
				background: var(--color-primary-light);
				border-color: var(--color-primary);
				color: var(--color-primary);
				font-weight: 500;
			}

			.numeric-inputs {
				display: flex;
				gap: 12px;
				margin: 10px 0;
			}

			.numeric-input {
				flex: 1;
				position: relative;
			}

			.numeric-input input {
				width: 100%;
				border: 1px solid var(--color-gray-300);
				border-radius: 8px;
				padding: 12px;
				padding-left: 36px;
				font-size: 16px;
				color: var(--color-gray-800);
			}

			.numeric-input span {
				position: absolute;
				left: 12px;
				top: 50%;
				transform: translateY(-50%);
				color: var(--color-gray-500);
				font-weight: 500;
			}

			.weight-input-container {
				position: relative;
				display: flex;
				align-items: center;
			}

			.weight-input-container .input-text {
				padding-right: 40px;
			}

			.weight-unit {
				position: absolute;
				right: 12px;
				top: 50%;
				transform: translateY(-50%);
				color: var(--color-gray-500);
				font-weight: 500;
				font-size: 16px;
			}

			.radio-group {
				display: flex;
				gap: 16px;
				margin-top: 8px;
			}

			.radio-option {
				display: flex;
				align-items: center;
				gap: 8px;
				cursor: pointer;
			}

			.radio-option input {
				accent-color: var(--color-primary);
			}

			.task-list {
				max-height: 300px;
				overflow-y: auto;
				margin-top: 8px;
			}

			.task-item {
				background: var(--color-gray-50);
				border-radius: 8px;
				padding: 12px;
				margin-bottom: 8px;
				display: flex;
				justify-content: space-between;
				align-items: center;
			}

			.task-checkbox {
				margin-right: 10px;
				accent-color: var(--color-primary);
			}

			.task-actions {
				display: flex;
				gap: 8px;
			}

			.task-btn {
				border: none;
				background: none;
				cursor: pointer;
				color: var(--color-gray-500);
				transition: color 0.2s;
			}

			.task-btn:hover {
				color: var(--color-primary);
			}

			.add-task-btn {
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 8px;
				width: 100%;
				padding: 12px;
				border: 1px dashed var(--color-gray-300);
				border-radius: 8px;
				background: var(--color-gray-50);
				color: var(--color-primary);
				font-weight: 500;
				cursor: pointer;
				transition: all 0.2s;
			}

			.add-task-btn:hover {
				background: var(--color-primary-light);
				border-color: var(--color-primary);
			}

			.history-list {
				margin-top: 8px;
			}

			.history-item {
				background: var(--color-white);
				border: 1px solid var(--color-gray-200);
				border-radius: 8px;
				padding: 12px;
				margin-bottom: 8px;
			}

			.history-header {
				display: flex;
				justify-content: space-between;
				margin-bottom: 6px;
			}

			.history-date {
				font-size: 12px;
				color: var(--color-gray-500);
			}

			.history-value {
				font-weight: 500;
				color: var(--color-primary);
			}

			.history-note {
				font-size: 14px;
				color: var(--color-gray-600);
			}

			/* 底部保存按钮 */
			.footer {
				position: fixed;
				bottom: 0;
				left: 0;
				width: 100%;
				background: var(--color-white);
				padding: 16px;
				box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
				z-index: 50;
			}

			.save-btn {
				width: 100%;
				background: var(--color-primary);
				color: var(--color-white);
				border: none;
				border-radius: 12px;
				padding: 15px;
				font-size: 16px;
				font-weight: 600;
				display: flex;
				align-items: center;
				justify-content: center;
				cursor: pointer;
				transition: all 0.2s;
			}

			.save-btn:hover {
				background: var(--color-primary-dark);
			}
		</style>
	</head>
	<body>
		<!-- 状态栏 -->
		<div id="status-bar-container"></div>

		<!-- 导航栏 -->
		<div class="navbar">
			<a href="objective-detail.html" class="navbar-back">
				<i class="fas fa-chevron-left"></i>
			</a>
			<div class="navbar-title">编辑关键结果</div>
			<div class="navbar-action" id="save-kr">保存</div>
		</div>

		<!-- 内容区域 -->
		<div class="content-area">
			<div class="container">
				<form id="kr-form">
					<!-- 关键结果标题和描述部分 -->
					<div class="form-section">
						<div class="section-label">基本信息</div>
						<div class="input-card">
							<div class="input-field">
								<label class="input-label" for="kr-title">关键结果标题</label>
								<input
									type="text"
									id="kr-title"
									class="input-text"
									placeholder="例如：完成APP新版本UI设计"
									value="用户满意度评分达到4.8（满分5分）"
									required
								/>
								<div class="input-hint">明确、可衡量的结果描述</div>
							</div>
							<div class="input-field">
								<label class="input-label" for="kr-description"
									>详细描述</label
								>
								<textarea
									id="kr-description"
									class="input-text"
									rows="3"
									placeholder="添加更详细的说明..."
								>通过问卷调查收集用户反馈，确保产品用户体验满足高标准要求，每月进行一次用户满意度调查。</textarea>
							</div>
						</div>
					</div>

					<!-- 目标值设置 -->
					<div class="form-section">
						<div class="section-label">目标设置</div>
						<div class="input-card">
							<div class="input-field">
								<label class="input-label">目标值</label>
								<div class="numeric-inputs">
									<div class="numeric-input">
										<span>起始</span>
										<input
											type="number"
											id="start-value"
											value="3.8"
											step="0.1"
											min="0"
										/>
									</div>
									<div class="numeric-input">
										<span>目标</span>
										<input
											type="number"
											id="target-value"
											value="4.8"
											step="0.1"
											min="0"
										/>
									</div>
								</div>
								<div class="input-hint">目标应当具有挑战性但又可实现</div>
							</div>
							<div class="input-field">
								<label class="input-label">进度计算方式</label>
								<div class="radio-group">
									<label class="radio-option">
										<input type="radio" name="progress-type" value="sum" checked />
										<span>求和</span>
									</label>
									<label class="radio-option">
										<input type="radio" name="progress-type" value="latest" />
										<span>最新</span>
									</label>
								</div>
								<div class="input-hint">求和：累计所有进度记录；最新：仅使用最新记录</div>
							</div>
							<div class="input-field">
								<label class="input-label" for="kr-weight">权重</label>
								<div class="weight-input-container">
									<input 
										type="number" 
										id="kr-weight" 
										class="input-text" 
										min="1" 
										max="100" 
										value="30" 
										step="1"
									/>
									<span class="weight-unit">%</span>
								</div>
								<div class="input-hint">该关键结果在目标中所占的权重比例</div>
							</div>
						</div>
					</div>
				</form>
			</div>
		</div>

		<!-- 底部按钮 -->
		<div class="footer">
			<button type="button" class="save-btn" id="save-btn">保存修改</button>
		</div>

		<script>
			document.addEventListener("DOMContentLoaded", function () {
				// 状态栏
				loadStatusBar("status-bar-container");

				// 加载标签栏
				loadTabBar("tab-bar-container", "home");

				// 限制权重输入范围
				const weightInput = document.getElementById("kr-weight");
				weightInput.addEventListener("input", function() {
					const value = parseInt(this.value);
					if (value > 100) {
						this.value = 100;
					} else if (value < 1) {
						this.value = 1;
					}
				});

				// 保存按钮
				const saveBtn = document.getElementById("save-btn");
				const saveKrBtn = document.getElementById("save-kr");

				function handleSave() {
					// 获取权重值
					const weight = document.getElementById("kr-weight").value;
					// 模拟保存操作
					alert("关键结果更新已保存！权重设置为：" + weight + "%");
					// 返回详情页
					window.location.href = "objective-detail.html";
				}

				saveBtn.addEventListener("click", handleSave);
				saveKrBtn.addEventListener("click", handleSave);

				// 接收主题变更消息
				window.addEventListener("message", function (event) {
					if (event.data && event.data.type === "THEME_CHANGE") {
						applyTheme(event.data.theme);
					}
				});

				// 应用主题颜色
				function applyTheme(theme) {
					document.documentElement.style.setProperty(
						"--color-primary",
						theme.primary
					);
					document.documentElement.style.setProperty(
						"--color-primary-dark",
						adjustBrightness(theme.primary, -15)
					);
					document.documentElement.style.setProperty(
						"--color-primary-light",
						`${theme.primary}20`
					);

					const submitButton = document.querySelector(".save-btn");
					if (submitButton) {
						submitButton.style.backgroundColor = theme.primary;
					}

					const navbarTitle = document.querySelector(".navbar-title");
					if (navbarTitle) {
						navbarTitle.style.color = theme.primary;
					}

					const navbarBack = document.querySelector(".navbar-back");
					if (navbarBack) {
						navbarBack.style.color = theme.primary;
					}

					const navbarAction = document.querySelector(".navbar-action");
					if (navbarAction) {
						navbarAction.style.color = theme.primary;
						navbarAction.style.backgroundColor = `${theme.primary}20`;
					}
				}

				// 辅助函数：调整颜色亮度
				function adjustBrightness(hex, percent) {
					let r = parseInt(hex.substring(1, 3), 16);
					let g = parseInt(hex.substring(3, 5), 16);
					let b = parseInt(hex.substring(5, 7), 16);

					r = Math.max(0, Math.min(255, r + percent));
					g = Math.max(0, Math.min(255, g + percent));
					b = Math.max(0, Math.min(255, b + percent));

					const rr = r.toString(16).padStart(2, "0");
					const gg = g.toString(16).padStart(2, "0");
					const bb = b.toString(16).padStart(2, "0");

					return `#${rr}${gg}${bb}`;
				}
			});
		</script>
	</body>
</html>
