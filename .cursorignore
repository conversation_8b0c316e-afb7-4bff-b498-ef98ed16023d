# 依赖目录
node_modules/
.git/

# 构建输出
dist/
dist-ssr/
unpackage/
deploy/conf.js


# IDE 相关
.idea/
.vscode/
.hbuilderx/

# 日志文件
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 环境变量和配置文件
.env
.env.*
!.env.template

# 系统文件
.DS_Store
Thumbs.db

# 自动生成的文件
*.local
src/auto-imports.d.ts
src/components.d.ts

# 大型文件
package-lock.json
yarn.lock
pnpm-lock.yaml

# 压缩文件
*.zip
*.rar
*.7z
*.tar
*.gz
*.tgz
*.bz2
*.xz
*.iso

# 临时文件
*.tmp
*.temp
.cache/ 

# uniCloud-aliyun/
# src/uni_modules/