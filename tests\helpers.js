/**
 * OKR测试助手函数
 */

/**
 * 创建模拟OKR数据
 * @param {Object} overrides 要覆盖的字段
 * @returns {Object} 模拟OKR数据
 */
function createMockOkr(overrides = {}) {
  return {
    _id: 'mock-okr-id',
    title: '测试目标',
    startDate: '2025-01-01',
    endDate: '2025-03-31',
    color: '#4361ee',
    content: '',
    motivation: [],
    feasibility: [],
    curVal: 30,
    tgtVal: 100,
    ...overrides,
  }
}

/**
 * 创建模拟关键结果数据
 * @param {Object} overrides 要覆盖的字段
 * @returns {Object} 模拟关键结果数据
 */
function createMockKeyResult(overrides = {}) {
  return {
    _id: 'mock-kr-id',
    okrId: 'mock-okr-id',
    title: '测试关键结果',
    type: 'kr',
    status: 0,
    curVal: 10,
    tgtVal: 100,
    ...overrides,
  }
}

/**
 * 模拟数据库表操作
 * @returns {Object} 模拟数据库表操作对象
 */
function mockDbTable() {
  return {
    table: jest.fn().mockReturnThis(),
    add: jest.fn().mockResolvedValue('new-id'),
    update: jest.fn().mockResolvedValue(undefined),
    get: jest.fn().mockResolvedValue(createMockOkr()),
    where: jest.fn().mockReturnThis(),
    toArray: jest.fn().mockResolvedValue([]),
    modify: jest.fn().mockResolvedValue(undefined),
  }
}

/**
 * 模拟uni-app API
 */
function mockUniApi() {
  global.uni = {
    showToast: jest.fn(),
    showModal: jest.fn(),
    navigateTo: jest.fn(),
    navigateBack: jest.fn(),
    redirectTo: jest.fn(),
    switchTab: jest.fn(),
    reLaunch: jest.fn(),
  }
}

module.exports = {
  createMockOkr,
  createMockKeyResult,
  mockDbTable,
  mockUniApi,
}
