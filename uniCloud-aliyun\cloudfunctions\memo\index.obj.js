// 云对象教程: https://uniapp.dcloud.net.cn/uniCloud/cloud-obj
// jsdoc语法提示教程：https://ask.dcloud.net.cn/docs/#//ask.dcloud.net.cn/article/129
module.exports = {
	_before: function () { // 通用预处理器

	},
	/**
	 * method1方法描述
	 * @param {string} param1 参数1描述
	 * @returns {object} 返回值描述
	 */
	async label(param1) {
		const db = uniCloud.database();
		const res = await db.collection('memos-label').where({
			user_id: '647315a928064a7587bc78a6'
		}).get()
		return {
			code: 200,
			data: res.data
		}
	},
	async memo(params) {
		const now = new Date().getTime()
		const p = {
			content: params.content,
			create_date: now,
			update_date: now,
			user_id: '647315a928064a7587bc78a6',
		}
		if(params.label_id) p.label_id = params.label_id
		
		const db = uniCloud.database();
		const res = await db.collection('memos').add(p)
		return {
			code: 200,
			data: null
		}
	}
	
	/**
	 * method1方法描述
	 * @param {string} param1 参数1描述
	 * @returns {object} 返回值描述
	 */
	/* 
	method1(param1) {
		// 参数校验，如无参数则不需要
		if (!param1) {
			return {
				errCode: 'PARAM_IS_NULL',
				errMsg: '参数不能为空'
			}
		}
		// 业务逻辑
		
		// 返回结果
		return {
			param1 //请根据实际需要返回值
		}
	}
	*/
}
