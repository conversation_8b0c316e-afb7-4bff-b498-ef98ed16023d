// 云对象教程: https://uniapp.dcloud.net.cn/uniCloud/cloud-obj
const {
	log
} = require("console")
const uniID = require('uni-id-common')

function generateUUID() {
	let d = new Date().getTime() // Timestamp
	// let d2 = (performance && performance.now && performance.now() * 1000) || 0 // Time in microseconds since page-load or 0 if unsupported
	let d2 = 0 // Time in microseconds since page-load or 0 if unsupported
	return 'xxxxxxxxxxxxxxxxxxxxxxxx'.replace(/[x]/g, function(c) {
		let r = Math.random() * 16 // random number between 0 and 16
		if (d > 0) {
			// Use timestamp until depleted
			r = (d + r) % 16 | 0
			d = Math.floor(d / 16)
		} else {
			// Use microseconds since page-load if supported
			r = (d2 + r) % 16 | 0
			d2 = Math.floor(d2 / 16)
		}
		return (c === 'x' ? r : (r & 0x3) | 0x8).toString(16)
	})
}
// jsdoc语法提示教程：https://ask.dcloud.net.cn/docs/#//ask.dcloud.net.cn/article/129
module.exports = {
	async option() {
		const db = uniCloud.database()
		const tasks = await db.collection('task').limit(200).get()

		const list = tasks.data.filter(e=>{
			return !!e.compInfos
		})

		// for(let i = 0; i < list.length; i++){
		// 	const todo = list[i]
		// 	if(!todo.compInfos){
		// 		todo.compInfos = []
		// 	}else{
		// 		todo.compInfos = JSON.parse(todo.compInfos)
		// 	}
		// 	todo.compInfos = [
		// 		...todo.compDates,
		// 		...todo.compInfos
		// 	]

		// 	 await db.collection('task').doc(todo._id).update({
		// 		 compInfos: JSON.stringify(todo.compInfos),
		// 	 })
		// }
		console.log(list)
		console.log(list.length)
		return list
	}
}