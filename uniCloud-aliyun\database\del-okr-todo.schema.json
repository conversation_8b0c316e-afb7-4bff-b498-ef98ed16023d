// 文档教程：https://uniapp.dcloud.net.cn/uniCloud/schema
{
  "bsonType": "object",
  "required": ["title", "status", "create_date", "uid"],
  "permission": {
    "read": "doc.uid == auth.uid",
    "create": true,
    "update": "doc.uid == auth.uid",
    "delete": "doc.uid == auth.uid"
  },
  "properties": {
    "_id": {
      "description": "ID，系统自动生成"
    },
    "result_id": {
      "bsonType": "string",
      "title": "关键结果 id",
      "description": "外键，所属关键结果 id",
      "foreignKey": "okr-key-result._id"
    },
    "title": {
      "bsonType": "string",
      "title": "todo 内容",
      "trim": "both"
    },
    "status": {
      "bsonType": "number",
      "title": "todo 状态",
      "description": "0 未完成，1 已完成，2 已放弃",
      "enum": [0, 1, 2]
    },
    "repeat_id": {
      "bsonType": "string",
      "title": "循环 todo id",
      "description": "复制自哪个 todo，用于记录循环 todo 的来源"
    },
    "repeat_rule": {
      "bsonType": "string",
      "title": "重复规则",
      "description": "代办事项的重复规则：daily 每日重复，weekly 每周重复",
      "enum": ["daily", "每日重复，weekly"]
    },
    "record_number": {
      "bsonType": "number",
      "title": "添加记录数",
      "description": "完成时添加多少记录值"
    },
    "complete_date": {
      "bsonType": "timestamp",
      "title": "完成时间"
    },
    "delete_date": {
      "bsonType": "timestamp",
      "title": "删除时间"
    },
    "create_date": {
      "bsonType": "timestamp",
      "title": "创建时间",
      "forceDefaultValue": {
        "$env": "now"
      }
    },
    "update_date": {
      "bsonType": "timestamp",
      "title": "更新时间",
      "defaultValue": {
        "$env": "now"
      }
    },
    "uid": {
      "bsonType": "string",
      "title": "用户 id",
      "forceDefaultValue": {
        "$env": "uid"
      },
      "foreignKey": "uni-id-users._id"
    }
  }
}
