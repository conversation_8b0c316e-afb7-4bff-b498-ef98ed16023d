// 在本文件中可配置云数据库初始化，数据格式见：https://uniapp.dcloud.io/uniCloud/hellodb?id=db-init

// 编写完毕后对本文件点右键，可按配置规则创建表和添加数据

{
	"uni-id-users": {
		"data": [{
			"_id": "_uni_starter_test_user_id",
			"username": "uni-starter预置用户名",
			"nickname": "测试用户昵称",
			"avatar": "https://unicloud.dcloud.net.cn/assets/logo.dca09351.png",
			"mobile": "18888888888",
			"mobile_confirmed": 1
		}]
	},
	"uni-id-roles": {
		"data": [{
			"role_id": "admin",
			"role_name": "超级管理员",
			"permission": [],
			"comment": "超级管理员拥有所有权限",
			"create_date": 0
		}]
	},
	"opendb-banner": {
		"data": [{
				"status": true,
				"bannerfile": {
					"name": "094a9dc0-50c0-11eb-b680-7980c8a877b8.jpg",
					"extname": "jpg",
					"fileType": "image",
					"url": "https://web-assets.dcloud.net.cn/unidoc/zh/shuijiao.jpg",
					"size": 70880,
					"image": {
						"width": 500,
						"height": 333
					},
					"path": "https://web-assets.dcloud.net.cn/unidoc/zh/shuijiao.jpg"
				},
				"open_url": "https://www.dcloud.io/",
				"title": "测试",
				"sort": 1,
				"category_id": "",
				"description": ""
			},
			{
				"status": true,
				"bannerfile": {
					"name": "094a9dc0-50c0-11eb-b680-7980c8a877b8.jpg",
					"extname": "jpg",
					"fileType": "image",
					"url": "https://web-assets.dcloud.net.cn/unidoc/zh/shuijiao.jpg",
					"size": 70880,
					"image": {
						"width": 500,
						"height": 333
					},
					"path": "https://web-assets.dcloud.net.cn/unidoc/zh/shuijiao.jpg"
				},
				"open_url": "https://www.dcloud.io/",
				"title": "",
				"category_id": "",
				"description": ""
			}
		]
	},
	"opendb-news-articles": {
		"data": [{
			"title": "阿里小程序IDE官方内嵌uni-app，为开发者提供多端开发服务",
			"excerpt": "阿里小程序IDE官方内嵌uni-app，为开发者提供多端开发服务",
			"content": "<p>随着微信、阿里、百度、头条、QQ纷纷推出小程序，开发者的开发维护成本持续上升，负担过重。这点已经成为共识，现在连小程序平台厂商也充分意识到了。</p>\n<p>阿里小程序团队，为了减轻开发者的负担，在官方的小程序开发者工具中整合了多端框架。</p>\n<p>经过阿里团队仔细评估，uni-app 在产品完成度、跨平台支持度、开发者社区、可持续发展等多方面优势明显，最终选定 uni-app内置于阿里小程序开发工具中，为开发者提供多端开发解决方案。</p>\n<p>经过之前1个月的公测，10月10日，阿里小程序正式发布0.70版开发者工具，通过 uni-app 实现多端开发，成为本次版本更新的亮点功能！</p>\n<p>如下图，在阿里小程序工具左侧主导航选择 uni-app，创建项目，即可开发。</p>\n<div class=\"aw-comment-upload-img-list active\"><img class=\"img-polaroid\" width=\"100%\" src=\"https://ask.dcloud.net.cn/uploads/article/20191014/56f7dc1bd5f265e824649f7cb4f78d5b.png\" /></div>\n<p><br />阿里小程序开发工具更新说明详见：https://docs.alipay.com/mini/ide/0.70-stable</p>\n<p>&nbsp;</p>\n<p>集成uni-app，这对于阿里团队而言，并不是一个容易做出的决定。毕竟 uni-app 是一个三方产品，要经过复杂的评审流程。</p>\n<p>这一方面突显出阿里团队以开发者需求为本的优秀价值观，另一方面也证明 uni-app的产品确实过硬。</p>\n<p>很多开发者都有多端需求，但又没有足够精力去了解、评估 uni-app，而处于观望态度。现在大家可以更放心的使用 uni-app 了，它没有让阿里失望，也不会让你失望。</p>\n<p>自从uni-app推出以来，DCloud也取得了高速的发展，目前拥有370万开发者，框架运行在4.6亿手机用户设备上，月活达到1.35亿（仅包括部分接入DCloud统计平台的数据）。并且数据仍在高速增长中，在市场占有率上处于遥遥领先的位置。</p>\n<p>本次阿里小程序工具集成 uni-app，会让 uni-app 继续快速爆发，取得更大的成功。</p>\n<p>后续DCloud还将深化与阿里的合作，在serverless等领域给开发者提供更多优质服务。</p>\n<p>使用多端框架开发各端应用，是多赢的模式。开发者减轻了负担，获得了更多新流量。而小程序平台厂商，也能保证自己平台上的各种应用可以被及时的更新。</p>\n<p>DCloud欢迎更多小程序平台厂商，与我们一起合作，为开发者、平台、用户的多赢而努力。</p>\n<p>进一步了解uni-app，详见：https://uniapp.dcloud.io</p>\n<p>欢迎扫码关注DCloud公众号，转发消息到朋友圈。<br /><img src=\"https://web-assets.dcloud.net.cn/unidoc/zh/weixin.jpg\"  width=\"80%\" /></p>",
			"avatar": "https://ask.dcloud.net.cn/uploads/article/20191014/56f7dc1bd5f265e824649f7cb4f78d5b.png",
			"type": 0,
			"user_id": "_uni_starter_test_user_id",
			"comment_count": 0,
			"like_count": 0,
			"comment_status": 0,
			"article_status": 1,
			"publish_date": 1616092287006,
			"last_modify_date": 1616092303031,
			"create_date": 1616092287006
		}]
	},
	"opendb-admin-menus": {
		"data": [{
				"menu_id": "index",
				"name": "首页",
				"icon": "uni-icons-home",
				"url": "/",
				"sort": 100,
				"parent_id": "",
				"permission": [],
				"enable": true,
				"create_date": 1602662469396
			}, {
				"menu_id": "system_management",
				"name": "系统管理",
				"icon": "admin-icons-fl-xitong",
				"url": "",
				"sort": 1000,
				"parent_id": "",
				"permission": [],
				"enable": true,
				"create_date": 1602662469396
			}, {
				"menu_id": "system_user",
				"name": "用户管理",
				"icon": "admin-icons-manager-user",
				"url": "/pages/system/user/list",
				"sort": 1010,
				"parent_id": "system_management",
				"permission": [],
				"enable": true,
				"create_date": 1602662469398
			}, {
				"menu_id": "system_role",
				"name": "角色管理",
				"icon": "admin-icons-manager-role",
				"url": "/pages/system/role/list",
				"sort": 1020,
				"parent_id": "system_management",
				"permission": [],
				"enable": true,
				"create_date": 1602662469397
			}, {
				"menu_id": "system_permission",
				"name": "权限管理",
				"icon": "admin-icons-manager-permission",
				"url": "/pages/system/permission/list",
				"sort": 1030,
				"parent_id": "system_management",
				"permission": [],
				"enable": true,
				"create_date": 1602662469396
			}, {
				"menu_id": "system_menu",
				"name": "菜单管理",
				"icon": "admin-icons-manager-menu",
				"url": "/pages/system/menu/list",
				"sort": 1040,
				"parent_id": "system_management",
				"permission": [],
				"enable": true,
				"create_date": 1602662469396
			}, {
				"menu_id": "system_app",
				"name": "应用管理",
				"icon": "admin-icons-manager-app",
				"url": "/pages/system/app/list",
				"sort": 1035,
				"parent_id": "system_management",
				"permission": [],
				"enable": true,
				"create_date": 1602662469399
			}, {
				"menu_id": "system_update",
				"name": "App升级中心",
				"icon": "uni-icons-cloud-upload",
				"url": "/uni_modules/uni-upgrade-center/pages/version/list",
				"sort": 1036,
				"parent_id": "system_management",
				"permission": [],
				"enable": true,
				"create_date": 1656491532434
			}, {
				"menu_id": "system_tag",
				"name": "标签管理",
				"icon": "admin-icons-manager-tag",
				"url": "/pages/system/tag/list",
				"sort": 1037,
				"parent_id": "system_management",
				"permission": [],
				"enable": true,
				"create_date": 1602662479389
			}, {
				"permission": [],
				"enable": true,
				"menu_id": "safety_statistics",
				"name": "安全审计",
				"icon": "admin-icons-safety",
				"url": "",
				"sort": 3100,
				"parent_id": "",
				"create_date": 1638356430871
			}, {
				"permission": [],
				"enable": true,
				"menu_id": "safety_statistics_user_log",
				"name": "用户日志",
				"icon": "",
				"url": "/pages/system/safety/list",
				"sort": 3101,
				"parent_id": "safety_statistics",
				"create_date": 1638356430871
			}, {
				"permission": [],
				"enable": true,
				"menu_id": "uni-stat",
				"name": "uni 统计",
				"icon": "admin-icons-tongji",
				"url": "",
				"sort": 2100,
				"parent_id": "",
				"create_date": 1638356430871
			}, {
				"parent_id": "uni-stat",
				"permission": [],
				"enable": true,
				"menu_id": "uni-stat-device",
				"name": "设备统计",
				"icon": "admin-icons-shebeitongji",
				"url": "",
				"sort": 2120,
				"create_date": 1638356902516
			}, {
				"parent_id": "uni-stat-device",
				"permission": [],
				"enable": true,
				"menu_id": "uni-stat-device-overview",
				"name": "概况",
				"icon": "",
				"url": "/pages/uni-stat/device/overview/overview",
				"sort": 2121,
				"create_date": 1638356902516
			}, {
				"parent_id": "uni-stat-device",
				"permission": [],
				"enable": true,
				"menu_id": "uni-stat-device-activity",
				"name": "活跃度",
				"icon": "",
				"url": "/pages/uni-stat/device/activity/activity",
				"sort": 2122,
				"create_date": 1638356902516
			}, {
				"parent_id": "uni-stat-device",
				"permission": [],
				"enable": true,
				"menu_id": "uni-stat-device-trend",
				"name": "趋势分析",
				"icon": "",
				"url": "/pages/uni-stat/device/trend/trend",
				"sort": 2123,
				"create_date": 1638356902516
			}, {
				"parent_id": "uni-stat-device",
				"permission": [],
				"enable": true,
				"menu_id": "uni-stat-device-retention",
				"name": "留存",
				"icon": "",
				"url": "/pages/uni-stat/device/retention/retention",
				"sort": 2124,
				"create_date": 1638356902516
			}, {
				"parent_id": "uni-stat-device",
				"permission": [],
				"enable": true,
				"menu_id": "uni-stat-device-comparison",
				"name": "平台对比",
				"icon": "",
				"url": "/pages/uni-stat/device/comparison/comparison",
				"sort": 2125,
				"create_date": 1638356902516
			}, {
				"parent_id": "uni-stat-device",
				"permission": [],
				"enable": true,
				"menu_id": "uni-stat-device-stickiness",
				"name": "粘性",
				"icon": "",
				"url": "/pages/uni-stat/device/stickiness/stickiness",
				"sort": 2126,
				"create_date": 1638356902516
			}, {
				"parent_id": "uni-stat",
				"permission": [],
				"enable": true,
				"menu_id": "uni-stat-user",
				"name": "注册用户统计",
				"icon": "admin-icons-yonghutongji",
				"url": "",
				"sort": 2122,
				"create_date": 1638356902516
			}, {
				"parent_id": "uni-stat-user",
				"permission": [],
				"enable": true,
				"menu_id": "uni-stat-user-overview",
				"name": "概况",
				"icon": "",
				"url": "/pages/uni-stat/user/overview/overview",
				"sort": 2121,
				"create_date": 1638356902516
			}, {
				"parent_id": "uni-stat-user",
				"permission": [],
				"enable": true,
				"menu_id": "uni-stat-user-activity",
				"name": "活跃度",
				"icon": "",
				"url": "/pages/uni-stat/user/activity/activity",
				"sort": 2122,
				"create_date": 1638356902516
			}, {
				"parent_id": "uni-stat-user",
				"permission": [],
				"enable": true,
				"icon": "",
				"menu_id": "uni-stat-user-trend",
				"name": "趋势分析",
				"url": "/pages/uni-stat/user/trend/trend",
				"sort": 2123,
				"create_date": 1638356902516
			}, {
				"parent_id": "uni-stat-user",
				"permission": [],
				"enable": true,
				"menu_id": "uni-stat-user-retention",
				"name": "留存",
				"icon": "",
				"url": "/pages/uni-stat/user/retention/retention",
				"sort": 2124,
				"create_date": 1638356902516
			}, {
				"parent_id": "uni-stat-user",
				"permission": [],
				"enable": true,
				"menu_id": "uni-stat-user-comparison",
				"name": "平台对比",
				"icon": "",
				"url": "/pages/uni-stat/user/comparison/comparison",
				"sort": 2125,
				"create_date": 1638356902516
			}, {
				"parent_id": "uni-stat-user",
				"permission": [],
				"enable": true,
				"menu_id": "uni-stat-user-stickiness",
				"name": "粘性",
				"icon": "",
				"url": "/pages/uni-stat/user/stickiness/stickiness",
				"sort": 2126,
				"create_date": 1638356902516
			}, {
				"parent_id": "uni-stat",
				"permission": [],
				"enable": true,
				"menu_id": "uni-stat-page-analysis",
				"name": "页面统计",
				"icon": "admin-icons-page-ent",
				"url": "",
				"sort": 2123,
				"create_date": 1638356902516
			}, {
				"parent_id": "uni-stat-page-analysis",
				"permission": [],
				"enable": true,
				"menu_id": "uni-stat-page-res",
				"name": "受访页",
				"icon": "",
				"url": "/pages/uni-stat/page-res/page-res",
				"sort": 2131,
				"create_date": 1638356902516
			}, {
				"parent_id": "uni-stat-page-analysis",
				"permission": [],
				"enable": true,
				"menu_id": "uni-stat-page-ent",
				"name": "入口页",
				"icon": "",
				"url": "/pages/uni-stat/page-ent/page-ent",
				"sort": 2132,
				"create_date": 1638356902516
			}, {
				"parent_id": "uni-stat",
				"permission": [],
				"enable": true,
				"menu_id": "uni-stat-senceChannel",
				"name": "渠道/场景值分析",
				"icon": "admin-icons-qudaofenxi",
				"url": "",
				"sort": 2150,
				"create_date": 1638356902516
			}, {
				"parent_id": "uni-stat-senceChannel",
				"permission": [],
				"enable": true,
				"menu_id": "uni-stat-senceChannel-scene",
				"name": "场景值（小程序）",
				"icon": "",
				"url": "/pages/uni-stat/scene/scene",
				"sort": 2151,
				"create_date": 1638356902516
			}, {
				"parent_id": "uni-stat-senceChannel",
				"permission": [],
				"enable": true,
				"menu_id": "uni-stat-senceChannel-channel",
				"name": "渠道（app）",
				"icon": "",
				"url": "/pages/uni-stat/channel/channel",
				"sort": 2152,
				"create_date": 1638356902516
			}, {
				"parent_id": "uni-stat",
				"permission": [],
				"enable": true,
				"menu_id": "uni-stat-event-event",
				"name": "自定义事件",
				"icon": "admin-icons-shijianfenxi",
				"url": "/pages/uni-stat/event/event",
				"sort": 2160,
				"create_date": 1638356902516
			}, {
				"parent_id": "uni-stat",
				"permission": [],
				"enable": true,
				"menu_id": "uni-stat-error",
				"name": "错误统计",
				"icon": "admin-icons-cuowutongji",
				"url": "",
				"sort": 2170,
				"create_date": 1638356902516
			}, {
				"parent_id": "uni-stat-error",
				"permission": [],
				"enable": true,
				"menu_id": "uni-stat-error-js",
				"name": "js报错",
				"icon": "",
				"url": "/pages/uni-stat/error/js/js",
				"sort": 2171,
				"create_date": 1638356902516
			}, {
				"parent_id": "uni-stat-error",
				"permission": [],
				"enable": true,
				"menu_id": "uni-stat-error-app",
				"name": "app崩溃",
				"icon": "",
				"url": "/pages/uni-stat/error/app/app",
				"sort": 2172,
				"create_date": 1638356902516
			},
			{
				"menu_id": "uni-stat-pay",
				"name": "支付统计",
				"icon": "uni-icons-circle",
				"url": "",
				"sort": 2122,
				"parent_id": "uni-stat",
				"permission": [],
				"enable": true,
				"create_date": 1667386977981
			}, {
				"menu_id": "uni-stat-pay-overview",
				"name": "概况",
				"icon": "",
				"url": "/pages/uni-stat/pay-order/overview/overview",
				"sort": 21221,
				"parent_id": "uni-stat-pay",
				"permission": [],
				"enable": true,
				"create_date": 1667387038602
			},
			{
				"menu_id": "uni-stat-pay-funnel",
				"name": "转换漏斗分析",
				"icon": "",
				"url": "/pages/uni-stat/pay-order/funnel/funnel",
				"sort": 21222,
				"parent_id": "uni-stat-pay",
				"permission": [],
				"enable": true,
				"create_date": 1668430092890
			},
			{
				"menu_id": "uni-stat-pay-ranking",
				"name": "价值用户排行",
				"icon": "",
				"url": "/pages/uni-stat/pay-order/ranking/ranking",
				"sort": 21223,
				"parent_id": "uni-stat-pay",
				"permission": [],
				"enable": true,
				"create_date": 1668430128302
			},
			{
				"menu_id": "uni-stat-pay-order-list",
				"name": "订单明细",
				"icon": "",
				"url": "/pages/uni-stat/pay-order/list/list",
				"sort": 21224,
				"parent_id": "uni-stat-pay",
				"permission": [],
				"enable": true,
				"create_date": 1667387078947
			}
		]
	},
	"uni-id-permissions": {},
	"uni-id-log": {},
	"uni-id-tag": {},
	"uni-id-device": {},
	"uni-id-scores": {},
	"opendb-verify-codes": {},
	"opendb-app-list": {},
	"opendb-app-versions": {},
	"opendb-device": {},
	"opendb-department": {},
	"opendb-sms-task": {},
	"opendb-sms-log": {},
	"opendb-sms-template": {},
	"opendb-open-data": {},
	"uni-stat-app-versions": {},
	"uni-stat-active-devices": {},
	"uni-stat-active-users": {},
	"uni-stat-app-channels": {},
	"uni-stat-app-crash-logs": {},
	"uni-stat-app-platforms": {},
	"uni-stat-error-logs": {},
	"uni-stat-error-result": {},
	"uni-stat-error-source-map": {},
	"uni-stat-event-logs": {},
	"uni-stat-event-result": {},
	"uni-stat-events": {},
	"uni-stat-loyalty-result": {},
	"uni-stat-mp-scenes": {},
	"uni-stat-page-logs": {},
	"uni-stat-page-result": {},
	"uni-stat-pages": {},
	"uni-stat-result": {},
	"uni-stat-run-errors": {},
	"uni-stat-session-logs": {},
	"uni-stat-share-logs": {},
	"uni-stat-user-session-logs": {},
	"uni-stat-pay-result": {},
	"uni-pay-orders": {},
	"opendb-tempdata": {},
	"opendb-feedback": {},
	"opendb-news-categories": {},
	"opendb-news-comments": {},
	"opendb-news-favorite": {},
	"opendb-search-hot": {},
	"opendb-search-log": {},
	"opendb-sign-in": {},
	"read-news-log": {}
}
