import App from './App'
import i18n from './lang/i18n'
import uView from './uni_modules/vk-uview-ui'
import '@fortawesome/fontawesome-free/css/all.min.css'
import './styles/variables.css'
import './styles/fonts.css'
import 'uno.css'

// #ifdef VUE3
import { createSSRApp } from 'vue'

export function createApp() {
  const app = createSSRApp(App)

  // #ifndef H5
  app.mixin({
    onShow() {
        const app = getApp()
        if (app && app.setTheme) {
          const savedTheme = uni.getStorageSync('theme') || 'ocean'
          app.setTheme(savedTheme, true)
        }
    },
  })
  // #endif

  

  app.use(i18n)
  app.use(uView)
  return {
    app,
  }
}
// #endif
