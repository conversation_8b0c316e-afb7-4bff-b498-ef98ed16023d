/**
 * OKR 详情页面测试
 */
import { describe, it, expect, beforeEach, jest } from '@jest/globals'
import { getOkrApi, delOkrApi } from '@/api/okr'
import { getTaskListApi, updateTaskApi, getTaskApi } from '@/api/task'
import { router, getRoute } from '@/utils/tools'
import { createMockOkr, createMockKeyResult, mockUniApi } from '../helpers'

// 模拟依赖
jest.mock('@/api/okr')
jest.mock('@/api/task')
jest.mock('@/utils/tools', () => ({
  router: {
    push: jest.fn(),
    back: jest.fn(),
  },
  getRoute: {
    params: jest.fn(),
  },
}))

describe('OKR 详情页面测试', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    mockUniApi()
  })

  describe('页面数据加载', () => {
    it('应成功加载 OKR 目标详情和关联数据', async () => {
      // 模拟 URL 参数
      getRoute.params.mockReturnValue({
        id: 'mock-okr-id',
      })

      // 准备模拟数据
      const mockOkr = createMockOkr({
        title: '2025 年健康目标',
        content: '改善身体健康状况',
        startDate: '2025-01-01',
        endDate: '2025-03-31',
        motivation: JSON.stringify([
          {
            title: '内在动机',
            content: '保持健康的生活方式',
          },
        ]),
        feasibility: JSON.stringify([
          {
            title: '可行性分析',
            content: '制定合理的锻炼计划',
          },
        ]),
      })

      // 准备关键结果数据
      const mockKeyResults = [
        createMockKeyResult({
          _id: 'kr-1',
          okrId: 'mock-okr-id',
          title: '每周健身 3 次',
          parentId: '',
          type: 'kr',
          curVal: 6,
          tgtVal: 36,
        }),
        createMockKeyResult({
          _id: 'kr-2',
          okrId: 'mock-okr-id',
          title: '每天 8 小时睡眠',
          parentId: '',
          type: 'kr',
          curVal: 45,
          tgtVal: 90,
        }),
      ]

      // 准备子任务数据
      const mockSubtasks = [
        {
          _id: 'task-1',
          okrId: 'mock-okr-id',
          parentId: 'kr-1',
          title: '办理健身卡',
          status: 1,
        },
        {
          _id: 'task-2',
          okrId: 'mock-okr-id',
          parentId: 'kr-1',
          title: '购买运动装备',
          status: 0,
        },
      ]

      // 所有任务合并在一起
      const allTasks = [...mockKeyResults, ...mockSubtasks]

      // 模拟 API 返回数据
      getOkrApi.mockResolvedValue(mockOkr)
      getTaskListApi.mockResolvedValue(allTasks)

      // 调用获取 OKR 详情方法
      const okrResult = await getOkrApi('mock-okr-id')
      const tasksResult = await getTaskListApi(`okrId == "mock-okr-id" && deleteTime == ""`)

      // 验证 API 调用
      expect(getOkrApi).toHaveBeenCalledWith('mock-okr-id')
      expect(getTaskListApi).toHaveBeenCalledWith(`okrId == "mock-okr-id" && deleteTime == ""`)

      // 验证 OKR 数据
      expect(okrResult).toEqual(mockOkr)
      expect(okrResult.title).toBe('2025 年健康目标')

      // 验证任务数据
      expect(tasksResult).toEqual(allTasks)
      expect(tasksResult.length).toBe(4)

      // 验证关键结果数据
      const krs = tasksResult.filter((task) => task.parentId === '' && task.type === 'kr')
      expect(krs.length).toBe(2)
      expect(krs[0].title).toBe('每周健身 3 次')
      expect(krs[1].title).toBe('每天 8 小时睡眠')

      // 验证子任务
      const subtasksOfKr1 = tasksResult.filter((task) => task.parentId === 'kr-1')
      expect(subtasksOfKr1.length).toBe(2)
      expect(subtasksOfKr1[0].title).toBe('办理健身卡')
      expect(subtasksOfKr1[0].status).toBe(1) // 已完成
    })

    it('应处理缺少 OKR ID 的情况', async () => {
      // 模拟 URL 参数 - 没有 ID
      getRoute.params.mockReturnValue({})

      // 模拟获取 OKR 详情的函数（示例）
      const fetchOkrDetail = async () => {
        const params = getRoute.params()
        const id = params.id

        if (!id) {
          uni.showToast({
            title: '缺少目标 ID',
            icon: 'none',
          })
          return null
        }

        return await getOkrApi(id)
      }

      // 执行函数
      const result = await fetchOkrDetail()

      // 验证结果
      expect(result).toBeNull()
      expect(uni.showToast).toHaveBeenCalledWith({
        title: '缺少目标 ID',
        icon: 'none',
      })
    })
  })

  describe('OKR 操作功能', () => {
    it('应能成功删除 OKR 目标', async () => {
      // 模拟 API 返回
      delOkrApi.mockResolvedValue(undefined)

      // 模拟确认对话框返回确认
      uni.showModal.mockImplementation((options) => {
        options.success({ confirm: true })
        return {}
      })

      // 模拟删除函数
      const deleteOkr = async () => {
        try {
          // 显示加载状态
          uni.showLoading({
            title: '正在删除...',
          })

          // 调用删除 API
          await delOkrApi('mock-okr-id')

          // 隐藏加载状态
          uni.hideLoading()

          // 显示成功提示
          uni.showToast({
            title: '删除成功',
            icon: 'success',
          })

          return true
        } catch (error) {
          uni.hideLoading()
          uni.showToast({
            title: '删除失败',
            icon: 'none',
          })
          return false
        }
      }

      // 执行删除
      const result = await deleteOkr()

      // 验证 API 调用和结果
      expect(delOkrApi).toHaveBeenCalledWith('mock-okr-id')
      expect(result).toBe(true)
      expect(uni.showLoading).toHaveBeenCalled()
      expect(uni.hideLoading).toHaveBeenCalled()
      expect(uni.showToast).toHaveBeenCalledWith({
        title: '删除成功',
        icon: 'success',
      })
    })

    it('应能导航到编辑页面', () => {
      // 模拟编辑操作
      const handleEdit = () => {
        router.push('/pages/okr/okrEdit', { id: 'mock-okr-id' })
      }

      // 执行编辑操作
      handleEdit()

      // 验证路由跳转
      expect(router.push).toHaveBeenCalledWith('/pages/okr/okrEdit', { id: 'mock-okr-id' })
    })
  })

  describe('关键结果和任务操作', () => {
    it('应能切换显示/隐藏关键结果的子任务', () => {
      // 准备测试数据
      const keyResults = [
        { _id: 'kr-1', title: 'KR 1', tasksVisible: false, subtasks: [] },
        { _id: 'kr-2', title: 'KR 2', tasksVisible: false, subtasks: [] },
      ]

      // 模拟切换函数
      const toggleTasksVisibility = (krId) => {
        const kr = keyResults.find((item) => item._id === krId)
        if (kr) {
          kr.tasksVisible = !kr.tasksVisible
        }
        return kr
      }

      // 执行切换操作
      const result = toggleTasksVisibility('kr-1')

      // 验证结果
      expect(result.tasksVisible).toBe(true)
      expect(keyResults[0].tasksVisible).toBe(true)
      expect(keyResults[1].tasksVisible).toBe(false) // 其他项不受影响
    })

    it('应能正确处理任务状态变更', async () => {
      // 准备测试数据
      const keyResults = [
        {
          _id: 'kr-1',
          title: 'KR 1',
          subtasks: [
            { _id: 'task-1', title: '任务 1', completed: false },
            { _id: 'task-2', title: '任务 2', completed: true },
          ],
        },
      ]

      // 模拟任务状态变更函数
      const handleTaskStatusChange = (result) => {
        for (const kr of keyResults) {
          const task = kr.subtasks.find((t) => t._id === result.taskId)
          if (task) {
            task.completed = result.status === 1
            break
          }
        }
      }

      // 执行状态变更
      handleTaskStatusChange({ taskId: 'task-1', status: 1 })

      // 验证结果
      expect(keyResults[0].subtasks[0].completed).toBe(true)
      expect(keyResults[0].subtasks[1].completed).toBe(true) // 不变

      // 再次切换状态
      handleTaskStatusChange({ taskId: 'task-2', status: 0 })

      // 验证结果
      expect(keyResults[0].subtasks[1].completed).toBe(false)
    })
  })

  describe('页面展示和交互', () => {
    it('应正确计算并显示 OKR 进度', () => {
      // 准备测试数据
      const objective = {
        curVal: 30,
        tgtVal: 100,
      }

      // 计算进度函数
      const calculateProgress = (curVal, tgtVal) => {
        if (tgtVal === 0) return 0
        const progress = Math.round((curVal / tgtVal) * 100)
        return Math.min(Math.max(0, progress), 100) // 限制在 0-100 之间
      }

      // 计算进度
      const progress = calculateProgress(objective.curVal, objective.tgtVal)

      // 验证结果
      expect(progress).toBe(30)
    })

    it('应能处理返回上一页操作', () => {
      // 执行返回操作
      router.back()

      // 验证结果
      expect(router.back).toHaveBeenCalled()
    })
  })
})
