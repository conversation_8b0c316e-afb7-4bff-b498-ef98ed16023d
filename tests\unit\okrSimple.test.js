// 简化的OKR API测试

// 模拟API
const mockAddOkr = jest.fn().mockResolvedValue('new-okr-id')
const mockGetOkr = jest.fn().mockResolvedValue({
  _id: 'test-okr-id',
  title: '测试目标',
  motivation: [],
  feasibility: [],
})

// 模拟API模块
jest.mock('../../src/api/okr', () => ({
  addOkrApi: mockAddOkr,
  getOkrApi: mockGetOkr,
}))

// 导入模拟后的模块
const { addOkrApi, getOkrApi } = require('../../src/api/okr')

test('addOkrApi - 应该成功添加OKR', async () => {
  const mockData = {
    title: '测试目标',
    startDate: '2025-01-01',
    endDate: '2025-01-31',
  }

  const result = await addOkrApi(mockData)

  expect(result).toBe('new-okr-id')
  expect(mockAddOkr).toHaveBeenCalledWith(mockData)
})

test('getOkrApi - 应该获取OKR详情', async () => {
  const result = await getOkrApi('test-okr-id')

  expect(result).toBeDefined()
  expect(result.title).toBe('测试目标')
  expect(mockGetOkr).toHaveBeenCalledWith('test-okr-id')
})
